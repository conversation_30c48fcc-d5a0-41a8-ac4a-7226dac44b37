<?php

namespace App\Providers;

use ClickHouseDB\Client;
use Illuminate\Support\ServiceProvider;

class ClickHouseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(Client::class, function () {
            $config = [
                'host' => env('CLICKHOUSE_HOST', '127.0.0.1'),
                'port' => env('CLICKHOUSE_PORT', 8123),
                'username' => env('CLICKHOUSE_USERNAME', 'default'),
                'password' => env('CLICKHOUSE_PASSWORD', ''),
                'https' => (bool) env('CLICKHOUSE_HTTPS', null),
            ];

            $client = new Client($config);
            $client->database(env('CLICKHOUSE_DATABASE', 'default'));

            return $client;
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
