<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EventStatisticsSheet implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles, WithTitle
{
    protected $statistics;

    protected $storeName;

    protected $multipleStores;

    public function __construct($statistics, $storeName, $multipleStores = false)
    {
        $this->statistics = $statistics;
        $this->storeName = $storeName;
        $this->multipleStores = $multipleStores;
    }

    public function title(): string
    {
        return 'Events';
    }

    private function formatNumber($value): float
    {
        if ($value === null || $value === '' || ! is_numeric($value)) {
            return 0.00;
        }

        return (float) $value;
    }

    public function array(): array
    {
        if ($this->multipleStores && is_array($this->statistics)) {
            $data = [];
            foreach ($this->statistics as $storeName => $storeStats) {
                $data[] = [
                    'Merchant Name' => $storeName,
                    'Fudai Count' => (int) ($storeStats['fudai']['count'] ?? 0),
                    'Fudai Amount' => $this->formatNumber($storeStats['fudai']['sum'] ?? 0),
                    'Fudai User Count' => (int) ($storeStats['fudai']['user_count'] ?? 0),
                    'Lucky Spin Count' => (int) ($storeStats['luckySpin']['count'] ?? 0),
                    'Lucky Spin Amount' => $this->formatNumber($storeStats['luckySpin']['sum'] ?? 0),
                    'Lucky Spin User Count' => (int) ($storeStats['luckySpin']['user_count'] ?? 0),
                    'Rebate Count' => (int) ($storeStats['rebate']['count'] ?? 0),
                    'Rebate Amount' => $this->formatNumber($storeStats['rebate']['sum'] ?? 0),
                    'Referral Count' => (int) ($storeStats['referral']['count'] ?? 0),
                    'Referral Amount' => $this->formatNumber($storeStats['referral']['sum'] ?? 0),
                ];
            }

            if (count($data) > 1) {
                $grandTotalRow = ['Merchant Name' => 'Grand Total'];
                $numericColumns = array_keys($data[0]);
                array_shift($numericColumns);

                foreach ($numericColumns as $column) {
                    $grandTotalRow[$column] = array_sum(array_column($data, $column));
                }

                $data[] = $grandTotalRow;
            }

            return $data;
        }

        return [[
            'Merchant Name' => $this->storeName,
            'Fudai Count' => (int) ($this->statistics['fudai']['count'] ?? 0),
            'Fudai Amount' => $this->formatNumber($this->statistics['fudai']['sum'] ?? 0),
            'Fudai User Count' => (int) ($this->statistics['fudai']['user_count'] ?? 0),
            'Lucky Spin Count' => (int) ($this->statistics['luckySpin']['count'] ?? 0),
            'Lucky Spin Amount' => $this->formatNumber($this->statistics['luckySpin']['sum'] ?? 0),
            'Lucky Spin User Count' => (int) ($this->statistics['luckySpin']['user_count'] ?? 0),
            'Rebate Count' => (int) ($this->statistics['rebate']['count'] ?? 0),
            'Rebate Amount' => $this->formatNumber($this->statistics['rebate']['sum'] ?? 0),
            'Referral Count' => (int) ($this->statistics['referral']['count'] ?? 0),
            'Referral Amount' => $this->formatNumber($this->statistics['referral']['sum'] ?? 0),
        ]];
    }

    public function headings(): array
    {
        return [
            'Merchant Name',
            'Fudai Count',
            'Fudai Amount',
            'Fudai User Count',
            'Lucky Spin Count',
            'Lucky Spin Amount',
            'Lucky Spin User Count',
            'Rebate Count',
            'Rebate Amount',
            'Referral Count',
            'Referral Amount',
        ];
    }

    public function columnFormats(): array
    {
        // We're handling format in the styles method directly
        return [];
    }

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();

        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        $lastRow = $sheet->getHighestRow();

        if ($this->multipleStores && $lastRow > 2) {
            $sheet->getStyle("A{$lastRow}:".$highestColumn.$lastRow)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E9F1FA'],
                ],
            ]);
        }

        // Define column groups
        $countColumns = ['B', 'D', 'E', 'G', 'I', 'J'];
        $monetaryColumns = ['C', 'F', 'H', 'K'];

        for ($row = 2; $row <= $lastRow; $row++) {
            foreach ($countColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER);
            }

            foreach ($monetaryColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2);
            }
        }

        return $sheet;
    }
}
