<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class EventStatisticsExport implements WithMultipleSheets
{
    protected $statistics;

    protected $storeName;

    protected $multipleStores;

    public function __construct($statistics, $storeName, $multipleStores = false)
    {
        $this->statistics = $statistics;
        $this->storeName = $storeName;
        $this->multipleStores = $multipleStores;
    }

    public function sheets(): array
    {
        return [
            'Events' => new EventStatisticsSheet($this->statistics, $this->storeName, $this->multipleStores),
        ];
    }
}
