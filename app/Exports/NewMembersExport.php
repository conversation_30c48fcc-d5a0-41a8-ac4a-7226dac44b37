<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class NewMembersExport implements FromArray, ShouldAutoSize, WithHeadings, WithStyles
{
    protected $newMembers;

    public function __construct($newMembers)
    {
        $this->newMembers = $newMembers;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->newMembers as $member) {
            // Format the registration date
            $registrationDate = '';
            if (!empty($member['created_at'])) {
                try {
                    $date = new \DateTime($member['created_at']);
                    $registrationDate = $date->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    $registrationDate = $member['created_at'];
                }
            }

            $data[] = [
                'User ID' => $member['id'] ?? '',
                'Name' => $member['name'] ?? '',
                'Phone Number' => $member['phone_no'] ?? '',
                'Store' => $member['store_name'] ?? '',
                'Registration Date' => $registrationDate,
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'User ID',
            'Name',
            'Phone Number',
            'Store',
            'Registration Date',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();

        // Style the header row
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        return $sheet;
    }
}
