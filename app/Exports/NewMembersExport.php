<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class NewMembersExport implements FromArray, ShouldAutoSize, WithHeadings, WithStyles
{
    protected $newMembers;

    public function __construct($newMembers)
    {
        $this->newMembers = $newMembers;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->newMembers as $member) {
            $data[] = [
                'User ID' => $member['user_id'] ?? '',
                'Username' => $member['username'] ?? '',
                'Phone Number' => $member['phone_no'] ?? '',
                'Registration Date' => $member['created_at'] ?? '',
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'User ID',
            'Username', 
            'Phone Number',
            'Registration Date',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();

        // Style the header row
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        return $sheet;
    }
}
