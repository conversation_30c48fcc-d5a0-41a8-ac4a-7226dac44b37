<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithPreCalculateFormulas;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Traits\GameLogTrait;

class AllPlayerLogsExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithPreCalculateFormulas, WithStyles
{
    use GameLogTrait;
    protected $data;

    protected $grandTotal;

    public function __construct($data, $grandTotal)
    {
        $this->data = $data;
        $this->grandTotal = $grandTotal;
    }

    public function array(): array
    {
        $exportData = [];

        foreach ($this->data as $item) {
            // Create a simple indexed array instead of associative array
            $exportData[] = [
                $item['username'] ?? '',
                $item['account'],
                $item['source'],
                $item['transaction_time'],
                $item['source'] === 'GSC' ? $this->getSiteName($item['site'] ?? '') : ($item['site'] ?? ''),
                $item['source'] === 'GSC' ? $this->getProductName($item['product'] ?? '') : ($item['product'] ?? ''),
                (float) ($item['initial_balance'] ?? 0),
                (float) ($item['final_balance'] ?? 0),
                (float) ($item['turnover'] ?? 0),
                (float) ($item['bet'] ?? 0),
                (float) ($item['payout'] ?? 0),
                (float) ($item['win_loss'] ?? 0),
            ];
        }

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Username',
            'Account',
            'Source',
            'Transaction Time',
            'Site',
            'Product',
            'Initial Balance',
            'Final Balance',
            'Turnover',
            'Bet',
            'Payout',
            'Win/Loss',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'G' => '0.00',
            'H' => '0.00',
            'I' => '0.00',
            'J' => '0.00',
            'K' => '0.00',
            'L' => '0.00',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Style for headers
        $sheet->getStyle('A1:L1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'E6E6E6',
                ],
            ],
        ]);

        // Format numeric columns
        $lastRow = count($this->data) + 1;
        for ($row = 2; $row <= $lastRow; $row++) {
            for ($col = 'G'; $col <= 'L'; $col++) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                if ($value === null || $value === '' || $value === 0) {
                    $cell->setValue(0);
                }

                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode('0.00');
            }
        }

        // Add grand totals at the bottom
        $totalRow = $lastRow + 1;

        $sheet->setCellValue("A{$totalRow}", 'Grand Total');
        $sheet->setCellValue("G{$totalRow}", $this->grandTotal['total_initial_balance'] ?? 0);
        $sheet->setCellValue("H{$totalRow}", $this->grandTotal['total_final_balance'] ?? 0);
        $sheet->setCellValue("I{$totalRow}", $this->grandTotal['total_turnover'] ?? 0);
        $sheet->setCellValue("J{$totalRow}", $this->grandTotal['total_bet'] ?? 0);
        $sheet->setCellValue("K{$totalRow}", $this->grandTotal['total_payout'] ?? 0);
        $sheet->setCellValue("L{$totalRow}", $this->grandTotal['total_win_loss'] ?? 0);

        $sheet->getStyle("A{$totalRow}:L{$totalRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'F5F5F5',
                ],
            ],
        ]);

        // Format total row numbers
        for ($col = 'G'; $col <= 'L'; $col++) {
            $sheet->getStyle($col.$totalRow)->getNumberFormat()->setFormatCode('0.00');
        }

        // Add GSC totals
        $gscTotalRow = $totalRow + 1;
        $sheet->setCellValue("A{$gscTotalRow}", 'GSC Total');
        $sheet->setCellValue("G{$gscTotalRow}", $this->grandTotal['total_gsc_initial_balance'] ?? 0);
        $sheet->setCellValue("H{$gscTotalRow}", $this->grandTotal['total_gsc_final_balance'] ?? 0);
        $sheet->setCellValue("I{$gscTotalRow}", $this->grandTotal['total_gsc_turnover'] ?? 0);
        $sheet->setCellValue("J{$gscTotalRow}", $this->grandTotal['total_gsc_bet'] ?? 0);
        $sheet->setCellValue("K{$gscTotalRow}", $this->grandTotal['total_gsc_payout'] ?? 0);
        $sheet->setCellValue("L{$gscTotalRow}", $this->grandTotal['total_gsc_win_loss'] ?? 0);

        $sheet->getStyle("A{$gscTotalRow}:L{$gscTotalRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        // Format GSC total row numbers
        for ($col = 'G'; $col <= 'L'; $col++) {
            $sheet->getStyle($col.$gscTotalRow)->getNumberFormat()->setFormatCode('0.00');
        }

        // Add WWJ totals
        $wwjTotalRow = $gscTotalRow + 1;
        $sheet->setCellValue("A{$wwjTotalRow}", 'WWJ Total');
        $sheet->setCellValue("G{$wwjTotalRow}", $this->grandTotal['total_wwj_initial_balance'] ?? 0);
        $sheet->setCellValue("H{$wwjTotalRow}", $this->grandTotal['total_wwj_final_balance'] ?? 0);
        $sheet->setCellValue("I{$wwjTotalRow}", $this->grandTotal['total_wwj_turnover'] ?? 0);
        $sheet->setCellValue("J{$wwjTotalRow}", $this->grandTotal['total_wwj_bet'] ?? 0);
        $sheet->setCellValue("K{$wwjTotalRow}", $this->grandTotal['total_wwj_payout'] ?? 0);
        $sheet->setCellValue("L{$wwjTotalRow}", $this->grandTotal['total_wwj_win_loss'] ?? 0);

        $sheet->getStyle("A{$wwjTotalRow}:L{$wwjTotalRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        // Format WWJ total row numbers
        for ($col = 'G'; $col <= 'L'; $col++) {
            $sheet->getStyle($col.$wwjTotalRow)->getNumberFormat()->setFormatCode('0.00');
        }

        return $sheet;
    }
}
