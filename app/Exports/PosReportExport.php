<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PosReportExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'Date Time',
            'Terminal',
            'Card Serial No',
            'Transaction Type',
            'Initial Amount',
            'Recharge Amount',
            'Balance',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'E' => '#,##0.00', // Custom format to always show 2 decimals
            'F' => '#,##0.00',
            'G' => '#,##0.00',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Apply number format to all cells in amount columns
        foreach ($sheet->getColumnIterator('E', 'G') as $column) {
            foreach ($column->getCellIterator() as $cell) {
                if ($cell->getRow() > 1) { // Skip header row
                    $value = $cell->getValue();
                    if ($value === null || $value === '' || $value === 0) {
                        $cell->setValue('0.00');
                    }
                }
            }
        }

        // Make headers bold
        $sheet->getStyle('A1:G1')->getFont()->setBold(true);

        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
