<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PlayerLogsExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles
{
    protected $data;

    protected $grandTotal;

    public function __construct($data, $grandTotal)
    {
        $this->data = $data;
        $this->grandTotal = $grandTotal;
    }

    public function array(): array
    {
        $exportData = [];

        foreach ($this->data as $item) {
            $exportData[] = [
                $item['username'],
                $item['account'],
                $item['total_initial_balance'],
                $item['total_bet'],
                $item['total_final_balance'],
                $item['total_win_loss'],
                $item['last_transaction'],
            ];
        }

        // Add grand total row
        $exportData[] = [
            'Grand Total',
            '',
            $this->grandTotal['total_initial_balance'] ?? 0,
            $this->grandTotal['total_bet'] ?? 0,
            $this->grandTotal['total_final_balance'] ?? 0,
            $this->grandTotal['total_win_loss'] ?? 0,
            '',
        ];

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Username',
            'Account',
            'Total Initial Balance',
            'Total Bet',
            'Total Final Balance',
            'Total Win/Loss',
            'Date & Time',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => '0.00',
            'D' => '0.00',
            'E' => '0.00',
            'F' => '0.00',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Style for headers - bold text
        $sheet->getStyle('A1:G1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        // Get the last row number
        $lastRow = count($this->data) + 2; // +2 for header and grand total

        // Style for grand total row - bold text
        $sheet->getStyle("A{$lastRow}:G{$lastRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        // Apply custom number format to ensure zeros display as 0.00
        for ($row = 2; $row <= $lastRow; $row++) {
            for ($col = 'C'; $col <= 'F'; $col++) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '') {
                    $cell->setValue(0);
                }

                // Apply the number format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode('0.00');
            }
        }
    }
}
