<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TopPlayersExport implements FromCollection, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles, WithTitle
{
    protected $topPlayers;

    protected $title;

    public function __construct($topPlayers, $title)
    {
        $this->topPlayers = $topPlayers;
        $this->title = $title;
    }

    public function collection()
    {
        $data = collect($this->topPlayers)->map(function ($player) {
            // Force all numeric values to be numeric with explicit 0 for null/empty values
            $depositAmount = isset($player['deposit_amount']) && $player['deposit_amount'] !== ''
                ? (float) $player['deposit_amount']
                : 0;

            $withdrawalAmount = isset($player['withdrawal_amount']) && $player['withdrawal_amount'] !== ''
                ? (float) $player['withdrawal_amount']
                : 0;

            $turnoverAmount = isset($player['turnover_amount']) && $player['turnover_amount'] !== ''
                ? (float) $player['turnover_amount']
                : 0;

            $p2pAmount = isset($player['p2p_amount']) && $player['p2p_amount'] !== ''
                ? (float) $player['p2p_amount']
                : 0;

            return [
                'store_name' => $player['store_name'] ?? '',
                'username' => $player['username'] ?? '',
                'phone_no' => $player['phone_no'] ?? '-',
                'deposit_amount' => $depositAmount,
                'withdrawal_amount' => $withdrawalAmount,
                'turnover_amount' => $turnoverAmount,
                'p2p_amount' => $p2pAmount,
                'last_active_at' => $player['last_active_at'] ?? '-',
                'last_deposit_at' => $player['last_deposit_at'] ?? '-',
            ];
        });

        // Calculate totals
        $totalDeposit = $data->sum('deposit_amount');
        $totalWithdrawal = $data->sum('withdrawal_amount');
        $totalTurnover = $data->sum('turnover_amount');
        $totalP2P = $data->sum('p2p_amount');

        // Add grand total row
        $data->push([
            'store_name' => 'Grand Total',
            'username' => '',
            'phone_no' => '',
            'deposit_amount' => $totalDeposit,
            'withdrawal_amount' => $totalWithdrawal,
            'turnover_amount' => $totalTurnover,
            'p2p_amount' => $totalP2P,
            'last_active_at' => '',
            'last_deposit_at' => '',
        ]);

        return $data;
    }

    public function headings(): array
    {
        return [
            'Store Name',
            'Name',
            'Phone Number',
            'Deposit Amount (RM)',
            'Withdrawal Amount (RM)',
            'Total Turnover (RM)',
            'P2P Transfer Amount (RM)',
            'Last Active At',
            'Last Deposit At',
        ];
    }

    public function title(): string
    {
        return $this->title;
    }

    public function columnFormats(): array
    {
        return [
            'D' => '0.00',
            'E' => '0.00',
            'F' => '0.00',
            'G' => '0.00',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $lastDataRow = count($this->topPlayers);
        $totalRow = $lastDataRow + 2; // +2 because there's a header row (1) and the data rows start from 2

        // Style for the header row
        $sheet->getStyle('A1:I1')->applyFromArray([
            'font' => [
                'bold' => true,
                'size' => 12,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E9ECEF'],
            ],
        ]);

        // Style for the grand total row
        $sheet->getStyle("A{$totalRow}:I{$totalRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'F8F9FA'],
            ],
        ]);

        // Format numbers to always show with 2 decimal places
        for ($row = 2; $row <= $totalRow; $row++) {
            for ($col = 'D'; $col <= 'G'; $col++) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                if ($value === null || $value === '' || $value === 0) {
                    $cell->setValue(0);
                }

                // Apply number format to ensure 0.00 display
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode('0.00');
            }
        }

        return [];
    }
}
