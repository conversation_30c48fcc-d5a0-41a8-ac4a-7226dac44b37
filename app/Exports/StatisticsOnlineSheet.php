<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StatisticsOnlineSheet implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles, WithTitle
{
    protected $statistics;

    protected $storeName;

    protected $multipleStores;

    public function __construct($statistics, $storeName, $multipleStores = false)
    {
        $this->statistics = $statistics;
        $this->storeName = $storeName;
        $this->multipleStores = $multipleStores;
    }

    public function title(): string
    {
        return 'Online';
    }

    private function formatNumber($value): float
    {
        // Ensure null, empty, or non-numeric values are converted to 0
        if ($value === null || $value === '' || ! is_numeric($value)) {
            return 0.00;
        }

        // Convert to float to ensure proper decimal formatting
        return (float) $value;
    }

    public function array(): array
    {
        $isAdmin = request()->user()->hasRole('admin');

        if ($this->multipleStores && is_array($this->statistics)) {
            $data = [];
            foreach ($this->statistics as $storeName => $storeStats) {
                $row = [
                    'Merchant Name' => $storeName,
                    'Total Registrations' => (int) ($storeStats['totalRegistrations'] ?? 0),
                    'Active Users' => (int) ($storeStats['totalActiveUsers'] ?? 0),
                    'First-Time Deposits Count' => (int) ($storeStats['totalFTD']['count'] ?? 0),
                    'First-Time Deposits Amount' => $this->formatNumber($storeStats['totalFTD']['sum'] ?? 0),
                    'Retention Rate (%)' => (float) ($storeStats['retentionRate'] ?? 0),
                    'Second-Time Deposits Count' => (int) ($storeStats['totalSTD']['count'] ?? 0),
                    'Second-Time Deposits Amount' => $this->formatNumber($storeStats['totalSTD']['sum'] ?? 0),
                    'Second-Time Deposits Rate (%)' => (float) ($storeStats['totalSTDRate'] ?? 0),
                    'Third-Time Deposits Count' => (int) ($storeStats['totalTTD']['count'] ?? 0),
                    'Third-Time Deposits Amount' => $this->formatNumber($storeStats['totalTTD']['sum'] ?? 0),
                    'Third-Time Deposits Rate (%)' => (float) ($storeStats['totalTTDRate'] ?? 0),
                    'Total Deposits Count' => (int) ($storeStats['totalDeposits']['count'] ?? 0),
                    'Total Deposits Amount' => $this->formatNumber($storeStats['totalDeposits']['sum'] ?? 0),
                    'Total Withdrawals Count' => (int) ($storeStats['totalWithdrawals']['count'] ?? 0),
                    'Total Withdrawals Amount' => $this->formatNumber($storeStats['totalWithdrawals']['sum'] ?? 0),
                    'Total Nett Transfer' => $this->formatNumber($storeStats['totalTransfer']['sum'] ?? 0),
                ];

                // Only include promotion and adjustment data for admin users
                if ($isAdmin) {
                    $row = array_merge($row, [
                        'Total Promotion In' => $this->formatNumber($storeStats['totalPromotion']['promotion_in'] ?? 0),
                        'Total Promotion In Count' => (int) ($storeStats['totalPromotion']['promotion_in_count'] ?? 0),
                        'Total Promotion Out' => $this->formatNumber($storeStats['totalPromotion']['promotion_out'] ?? 0),
                        'Total Promotion Out Count' => (int) ($storeStats['totalPromotion']['promotion_out_count'] ?? 0),
                        'Total Promotion Burnt' => $this->formatNumber($storeStats['totalPromotion']['promotion_burnt'] ?? 0),
                        'Total Adjustment In Count' => (int) ($storeStats['totalAdjustment']['adjustment_in']['count'] ?? 0),
                        'Total Adjustment In Sum' => $this->formatNumber($storeStats['totalAdjustment']['adjustment_in']['sum'] ?? 0),
                        'Total Adjustment Out Count' => (int) ($storeStats['totalAdjustment']['adjustment_out']['count'] ?? 0),
                        'Total Adjustment Out Sum' => $this->formatNumber($storeStats['totalAdjustment']['adjustment_out']['sum'] ?? 0),
                        'Total Adjustment Nett' => $this->formatNumber($storeStats['totalAdjustment']['adjustment_nett'] ?? 0),
                    ]);
                }

                if ($isAdmin) {
                    $row = array_merge($row, [
                        'Total Advance Credit Count' => (int) ($storeStats['totalAdvanceCredit']['count'] ?? 0),
                        'Total Advance Credit Amount' => $this->formatNumber($storeStats['totalAdvanceCredit']['sum'] ?? 0),
                    ]);
                }

                $data[] = $row;
            }

            // Add a grand total row if there are multiple stores
            if (count($data) > 1) {
                $grandTotalRow = ['Merchant Name' => 'Grand Total'];
                $numericColumns = array_keys($data[0]);
                // Skip the first one which is Merchant Name
                array_shift($numericColumns);

                // Calculate totals for each column
                foreach ($numericColumns as $column) {
                    $grandTotalRow[$column] = array_sum(array_column($data, $column));
                }

                $data[] = $grandTotalRow;
            }

            return $data;
        }

        // Original single store format
        $row = [
            'Merchant Name' => $this->storeName,
            'Total Registrations' => (int) ($this->statistics['totalRegistrations'] ?? 0),
            'Active Users' => (int) ($this->statistics['totalActiveUsers'] ?? 0),
            'First-Time Deposits Count' => (int) ($this->statistics['totalFTD']['count'] ?? 0),
            'First-Time Deposits Amount' => $this->formatNumber($this->statistics['totalFTD']['sum'] ?? 0),
            'Retention Rate (%)' => (float) ($this->statistics['retentionRate'] ?? 0),
            'Second-Time Deposits Count' => (int) ($this->statistics['totalSTD']['count'] ?? 0),
            'Second-Time Deposits Amount' => $this->formatNumber($this->statistics['totalSTD']['sum'] ?? 0),
            'Second-Time Deposits Rate (%)' => (float) ($this->statistics['totalSTDRate'] ?? 0),
            'Third-Time Deposits Count' => (int) ($this->statistics['totalTTD']['count'] ?? 0),
            'Third-Time Deposits Amount' => $this->formatNumber($this->statistics['totalTTD']['sum'] ?? 0),
            'Third-Time Deposits Rate (%)' => (float) ($this->statistics['totalTTDRate'] ?? 0),
            'Total Deposits Count' => (int) ($this->statistics['totalDeposits']['count'] ?? 0),
            'Total Deposits Amount' => $this->formatNumber($this->statistics['totalDeposits']['sum'] ?? 0),
            'Total Withdrawals Count' => (int) ($this->statistics['totalWithdrawals']['count'] ?? 0),
            'Total Withdrawals Amount' => $this->formatNumber($this->statistics['totalWithdrawals']['sum'] ?? 0),
            'Total Nett Transfer' => $this->formatNumber($this->statistics['totalTransfer']['sum'] ?? 0),
        ];

        if ($isAdmin) {
            $row = array_merge($row, [
                'Total Promotion In' => $this->formatNumber($this->statistics['totalPromotion']['promotion_in'] ?? 0),
                'Total Promotion In Count' => (int) ($this->statistics['totalPromotion']['promotion_in_count'] ?? 0),
                'Total Promotion Out' => $this->formatNumber($this->statistics['totalPromotion']['promotion_out'] ?? 0),
                'Total Promotion Out Count' => (int) ($this->statistics['totalPromotion']['promotion_out_count'] ?? 0),
                'Total Promotion Burnt' => $this->formatNumber($this->statistics['totalPromotion']['promotion_burnt'] ?? 0),
                'Total Adjustment In Count' => (int) ($this->statistics['totalAdjustment']['adjustment_in']['count'] ?? 0),
                'Total Adjustment In Sum' => $this->formatNumber($this->statistics['totalAdjustment']['adjustment_in']['sum'] ?? 0),
                'Total Adjustment Out Count' => (int) ($this->statistics['totalAdjustment']['adjustment_out']['count'] ?? 0),
                'Total Adjustment Out Sum' => $this->formatNumber($this->statistics['totalAdjustment']['adjustment_out']['sum'] ?? 0),
                'Total Adjustment Nett' => $this->formatNumber($this->statistics['totalAdjustment']['adjustment_nett'] ?? 0),
            ]);
        }

        if ($isAdmin) {
            $row = array_merge($row, [
                'Total Advance Credit Count' => (int) ($this->statistics['totalAdvanceCredit']['count'] ?? 0),
                'Total Advance Credit Amount' => $this->formatNumber($this->statistics['totalAdvanceCredit']['sum'] ?? 0),
            ]);
        }

        return [$row];
    }

    public function headings(): array
    {
        $headings = [
            'Merchant Name',
            'Total Registrations',
            'Active Users',
            'First-Time Deposits Count',
            'First-Time Deposits Amount',
            'Retention Rate (%)',
            'Second-Time Deposits Count',
            'Second-Time Deposits Amount',
            'Second-Time Deposits Rate (%)',
            'Third-Time Deposits Count',
            'Third-Time Deposits Amount',
            'Third-Time Deposits Rate (%)',
            'Total Deposits Count',
            'Total Deposits Amount',
            'Total Withdrawals Count',
            'Total Withdrawals Amount',
            'Total Nett Transfer',
        ];

        // Only include promotion and adjustment headings for admin users
        if (request()->user()->hasRole('admin')) {
            $headings = array_merge($headings, [
                'Total Promotion In',
                'Total Promotion In Count',
                'Total Promotion Out',
                'Total Promotion Out Count',
                'Total Promotion Burnt',
                'Total Adjustment In Count',
                'Total Adjustment In Sum',
                'Total Adjustment Out Count',
                'Total Adjustment Out Sum',
                'Total Adjustment Nett',
            ]);
        }

        if (request()->user()->hasRole('admin')) {
            $headings = array_merge($headings, [
                'Total Advance Credit Count',
                'Total Advance Credit Amount',
            ]);
        }

        return $headings;
    }

    public function columnFormats(): array
    {
        // We're handling format in the styles method directly
        return [];
    }

    public function styles(Worksheet $sheet)
    {
        $isAdmin = request()->user()->hasRole('admin');

        // Get the highest column letter
        $highestColumn = $sheet->getHighestColumn();

        // Style for headers
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        // Get the last row number
        $lastRow = $sheet->getHighestRow();

        // Style for grand total row if exists
        if ($this->multipleStores) {
            $sheet->getStyle("A{$lastRow}:".$highestColumn.$lastRow)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E9F1FA'],
                ],
            ]);
        }

        // Define column groups - adjust based on whether admin columns are included
        $countColumns = ['B', 'C', 'D', 'G', 'J', 'M', 'O'];
        $monetaryColumns = ['E', 'H', 'K', 'N', 'P', 'Q'];
        $percentageColumns = ['F', 'I', 'L'];

        // Add admin-only columns if user is admin
        if ($isAdmin) {
            $countColumns = array_merge($countColumns, ['S', 'U', 'W', 'Y']);
            $monetaryColumns = array_merge($monetaryColumns, ['R', 'T', 'V', 'X', 'Z', 'AA']);

            $countColumns[] = 'AB';
            $monetaryColumns[] = 'AC';
        }

        // Process all data rows (skip header row)
        for ($row = 2; $row <= $lastRow; $row++) {
            // Handle count columns (integer format)
            foreach ($countColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply the number format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
            }

            // Handle monetary columns (decimal format)
            foreach ($monetaryColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply the number format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2);
            }

            // Handle percentage columns
            foreach ($percentageColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply the percentage format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_PERCENTAGE_00);
            }
        }

        return $sheet;
    }
}
