<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithPreCalculateFormulas;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Traits\GameLogTrait;

class GscDetailLogsExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithPreCalculateFormulas, WithStyles
{
    use GameLogTrait;
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $exportData = [];

        foreach ($this->data as $item) {
            // Force all numeric values to be numeric with explicit 0 for null/empty values
            $turnover = $item['turnover'] === null || $item['turnover'] === '' ? 0 : (float) $item['turnover'];
            $bet = $item['bet'] === null || $item['bet'] === '' ? 0 : (float) $item['bet'];
            $winLoss = $item['win_loss'] === null || $item['win_loss'] === '' ? 0 : (float) $item['win_loss'];
            $payout = $bet + $winLoss;

            $exportData[] = [
                $item['username'] ?? '-', // Username as first column
                $item['account'],
                $this->getSiteName($item['site']),
                $this->getProductName($item['product']),
                $turnover,
                $bet,
                $payout,
                $winLoss,
                $item['match_time_mas'],
            ];
        }

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Username',
            'Account',
            'Site',
            'Product',
            'Turnover',
            'Bet',
            'Payout',
            'Win/Loss',
            'Match Time',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'E' => '0.00', // Updated column references due to added username column
            'F' => '0.00',
            'G' => '0.00',
            'H' => '0.00',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:I1')->applyFromArray([ // Updated to include username column
            'font' => [
                'bold' => true,
            ],
        ]);

        $lastRow = count($this->data) + 1;

        for ($row = 2; $row <= $lastRow; $row++) {
            for ($col = 'E'; $col <= 'H'; $col++) { // Updated column references
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                if ($value === null || $value === '' || $value === 0) {
                    $cell->setValue(0);
                }

                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode('0.00');
            }
        }
    }
}
