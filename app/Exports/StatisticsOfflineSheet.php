<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StatisticsOfflineSheet implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles, WithTitle
{
    protected $statistics;

    protected $storeName;

    protected $multipleStores;

    public function __construct($statistics, $storeName, $multipleStores = false)
    {
        $this->statistics = $statistics;
        $this->storeName = $storeName;
        $this->multipleStores = $multipleStores;
    }

    public function title(): string
    {
        return 'Offline';
    }

    private function formatNumber($value): float
    {
        // Ensure null, empty, or non-numeric values are converted to 0
        if ($value === null || $value === '' || ! is_numeric($value)) {
            return 0.00;
        }

        // Convert to float to ensure proper decimal formatting
        return (float) $value;
    }

    public function array(): array
    {
        if ($this->multipleStores && is_array($this->statistics)) {
            $data = [];
            foreach ($this->statistics as $storeName => $storeStats) {
                $data[] = [
                    'Merchant Name' => $storeName,
                    'Total Registrations' => (int) ($storeStats['totalRegistrations'] ?? 0),
                    'Active Users' => (int) ($storeStats['totalActiveUsers'] ?? 0),
                    'First-Time Deposits Count' => (int) ($storeStats['totalFTD']['count'] ?? 0),
                    'First-Time Deposits Amount' => $this->formatNumber($storeStats['totalFTD']['sum'] ?? 0),
                    'Retention Rate (%)' => (float) ($storeStats['retentionRate'] ?? 0),
                    'Total Deposits Count' => (int) ($storeStats['totalDeposits']['count'] ?? 0),
                    'Total Deposits Amount' => $this->formatNumber($storeStats['totalDeposits']['sum'] ?? 0),
                    'Total Withdrawals Count' => (int) ($storeStats['totalWithdrawals']['count'] ?? 0),
                    'Total Withdrawals Amount' => $this->formatNumber($storeStats['totalWithdrawals']['sum'] ?? 0),
                    'Total Nett Transfer' => $this->formatNumber($storeStats['totalTransfer']['sum'] ?? 0),
                ];
            }

            // Add a grand total row if there are multiple stores
            if (count($data) > 1) {
                $grandTotalRow = ['Merchant Name' => 'Grand Total'];
                $numericColumns = array_keys($data[0]);
                // Skip the first one which is Merchant Name
                array_shift($numericColumns);

                // Calculate totals for each column
                foreach ($numericColumns as $column) {
                    $grandTotalRow[$column] = array_sum(array_column($data, $column));
                }

                $data[] = $grandTotalRow;
            }

            return $data;
        }

        // Original single store format
        return [[
            'Merchant Name' => $this->storeName,
            'Total Registrations' => (int) ($this->statistics['totalRegistrations'] ?? 0),
            'Active Users' => (int) ($this->statistics['totalActiveUsers'] ?? 0),
            'First-Time Deposits Count' => (int) ($this->statistics['totalFTD']['count'] ?? 0),
            'First-Time Deposits Amount' => $this->formatNumber($this->statistics['totalFTD']['sum'] ?? 0),
            'Retention Rate (%)' => (float) ($this->statistics['retentionRate'] ?? 0),
            'Total Deposits Count' => (int) ($this->statistics['totalDeposits']['count'] ?? 0),
            'Total Deposits Amount' => $this->formatNumber($this->statistics['totalDeposits']['sum'] ?? 0),
            'Total Withdrawals Count' => (int) ($this->statistics['totalWithdrawals']['count'] ?? 0),
            'Total Withdrawals Amount' => $this->formatNumber($this->statistics['totalWithdrawals']['sum'] ?? 0),
            'Total Nett Transfer' => $this->formatNumber($this->statistics['totalTransfer']['sum'] ?? 0),
        ]];
    }

    public function headings(): array
    {
        return [
            'Merchant Name',
            'Total Registrations',
            'Active Users',
            'First-Time Deposits Count',
            'First-Time Deposits Amount',
            'Retention Rate (%)',
            'Total Deposits Count',
            'Total Deposits Amount',
            'Total Withdrawals Count',
            'Total Withdrawals Amount',
            'Total Nett Transfer',
        ];
    }

    public function columnFormats(): array
    {
        // We're handling format in the styles method directly
        return [];
    }

    public function styles(Worksheet $sheet)
    {
        // Get the highest column letter
        $highestColumn = $sheet->getHighestColumn();

        // Style for headers
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        // Get the last row number
        $lastRow = $sheet->getHighestRow();

        // Style for grand total row if exists
        if ($this->multipleStores) {
            $sheet->getStyle("A{$lastRow}:".$highestColumn.$lastRow)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E9F1FA'],
                ],
            ]);
        }

        // Define column groups
        $countColumns = ['B', 'C', 'D', 'G', 'I']; // Columns with count values (display as 0)
        $monetaryColumns = ['E', 'H', 'J', 'K']; // Columns with monetary values (display as 0.00)
        $percentageColumns = ['F']; // Columns with percentage values (display as 0.00%)

        // Process all data rows (skip header row)
        for ($row = 2; $row <= $lastRow; $row++) {
            // Handle count columns (integer format)
            foreach ($countColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply the number format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
            }

            // Handle monetary columns (decimal format)
            foreach ($monetaryColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply the number format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2);
            }

            // Handle percentage columns
            foreach ($percentageColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply the percentage format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_PERCENTAGE_00);
            }
        }

        return $sheet;
    }
}
