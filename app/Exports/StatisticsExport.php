<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class StatisticsExport implements WithMultipleSheets
{
    protected $statistics;

    protected $storeName;

    protected $multipleStores;

    public function __construct($statistics, $storeName, $multipleStores = false)
    {
        $this->statistics = $statistics;
        $this->storeName = $storeName;
        $this->multipleStores = $multipleStores;
    }

    public function sheets(): array
    {
        return [
            'Online' => new StatisticsOnlineSheet($this->statistics, $this->storeName, $this->multipleStores),
            // 'Offline' => new StatisticsOfflineSheet($this->statistics, $this->storeName, $this->multipleStores),
        ];
    }
}
