<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class FirstTimeDepositsExport implements FromArray, ShouldAutoSize, WithHeadings, WithStyles
{
    protected $firstTimeDeposits;

    public function __construct($firstTimeDeposits)
    {
        $this->firstTimeDeposits = $firstTimeDeposits;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->firstTimeDeposits as $deposit) {
            // Format the approved date
            $approvedDate = '';
            if (!empty($deposit['approved_at'])) {
                try {
                    $date = new \DateTime($deposit['approved_at']);
                    $approvedDate = $date->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    $approvedDate = $deposit['approved_at'] ?? '';
                }
            }

            $data[] = [
                'User ID' => (string) ($deposit['id'] ?? ''),
                'Name' => (string) ($deposit['name'] ?? ''),
                'Phone Number' => (string) ($deposit['phone_no'] ?? ''),
                'Store' => (string) ($deposit['store_name'] ?? ''),
                'Amount' => (string) ($deposit['amount'] ?? '0.00'),
                'Approved Date' => (string) $approvedDate,
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'User ID',
            'Name', 
            'Phone Number',
            'Store',
            'Amount',
            'Approved Date',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();

        // Style the header row
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        return $sheet;
    }
}
