<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class FirstTimeDepositsExport implements FromArray, ShouldAutoSize, WithHeadings, WithStyles
{
    protected $firstTimeDeposits;

    public function __construct($firstTimeDeposits)
    {
        $this->firstTimeDeposits = $firstTimeDeposits;
    }

    public function array(): array
    {
        $data = [];

        foreach ($this->firstTimeDeposits as $deposit) {
            // Format the registration date
            $registrationDate = '';
            if (!empty($deposit['created_at'])) {
                try {
                    $date = new \DateTime($deposit['created_at']);
                    $registrationDate = $date->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    $registrationDate = $deposit['created_at'] ?? '';
                }
            }

            // Format the FTD date
            $ftdDate = '';
            if (!empty($deposit['ftd_date'])) {
                try {
                    $date = new \DateTime($deposit['ftd_date']);
                    $ftdDate = $date->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    $ftdDate = $deposit['ftd_date'] ?? '';
                }
            }

            $data[] = [
                'User ID' => (string) ($deposit['id'] ?? ''),
                'Name' => (string) ($deposit['name'] ?? ''),
                'Phone Number' => (string) ($deposit['phone_no'] ?? ''),
                'Store' => (string) ($deposit['store_name'] ?? ''),
                'Registration Date' => (string) $registrationDate,
                'FTD Date' => (string) $ftdDate,
                'FTD Type' => (string) ($deposit['ftd_type'] ?? ''),
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'User ID',
            'Name',
            'Phone Number',
            'Store',
            'Registration Date',
            'FTD Date',
            'FTD Type',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();

        // Style the header row
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        return $sheet;
    }
}
