<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithPreCalculateFormulas;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PlayerDetailLogsExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithPreCalculateFormulas, WithStyles
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $exportData = [];

        foreach ($this->data as $item) {
            // Force all numeric values to be numeric with explicit 0 for null/empty values
            $initialBalance = $item['initial_balance'] === null || $item['initial_balance'] === '' ? 0 : (float) $item['initial_balance'];
            $bet = $item['bet'] === null || $item['bet'] === '' ? 0 : (float) $item['bet'];
            $finalBalance = $item['final_balance'] === null || $item['final_balance'] === '' ? 0 : (float) $item['final_balance'];
            $winLoss = $item['win_loss'] === null || $item['win_loss'] === '' ? 0 : (float) $item['win_loss'];

            $exportData[] = [
                $item['username'],
                $item['account'],
                $item['create_time'],
                $initialBalance,
                $bet,
                $finalBalance,
                $winLoss,
                $item['win_loss_status'],
            ];
        }

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Username',
            'Account',
            'Date & Time',
            'Initial Balance',
            'Bet',
            'Final Balance',
            'Win/Loss',
            'Status',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => '0.00',
            'D' => '0.00',
            'E' => '0.00',
            'F' => '0.00',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:G1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        $lastRow = count($this->data) + 1;

        for ($row = 2; $row <= $lastRow; $row++) {
            for ($col = 'C'; $col <= 'F'; $col++) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                if ($value === null || $value === '' || $value === 0) {
                    $cell->setValue(0);
                }

                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode('0.00');
            }
        }
    }
}
