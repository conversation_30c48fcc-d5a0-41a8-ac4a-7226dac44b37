<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class CurrencyDetailExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $exportData = [];
        foreach ($this->data as $item) {
            $exportData[] = [
                $item['date_time'],
                $item['terminal_serial'],
                $item['member_card_id'],
                $item['member_card_no'],
                "\t".$item['phone_no'], // Add tab prefix to force text format
                $item['operation_type'],
                $item['amount'],
                $item['member_balance'],
                $item['latest_member_balance'],
            ];
        }

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Date Time',
            'Terminal Serial',
            'Member Card ID',
            'Member Card No',
            'Phone Number',
            'Operation Type',
            'Amount',
            'Member Balance',
            'Latest Member Balance',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'G' => NumberFormat::FORMAT_NUMBER_00,
            'H' => NumberFormat::FORMAT_NUMBER_00,
            'I' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }
}
