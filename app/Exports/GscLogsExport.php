<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Traits\GameLogTrait;

class GscLogsExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles
{
    use GameLogTrait;
    protected $data;

    protected $grandTotal;

    public function __construct($data, $grandTotal)
    {
        $this->data = $data;
        $this->grandTotal = $grandTotal;
    }

    public function array(): array
    {
        $exportData = [];

        foreach ($this->data as $item) {
            $totalPayout = ($item['total_bet'] ?? 0) + ($item['total_win_loss'] ?? 0);

            $exportData[] = [
                $item['username'],
                $item['account'],
                $this->getSiteName($item['site']),
                $this->getProductName($item['product']),
                $item['total_turnover'] ?? 0,
                $item['total_bet'] ?? 0,
                $totalPayout,
                $item['total_win_loss'] ?? 0,
                $item['last_match_time'],
            ];
        }

        // Add grand total row
        $grandTotalPayout = ($this->grandTotal['total_bet'] ?? 0) + ($this->grandTotal['total_win_loss'] ?? 0);

        $exportData[] = [
            'Grand Total',
            '',
            '',
            '',
            $this->grandTotal['total_turnover'] ?? 0,
            $this->grandTotal['total_bet'] ?? 0,
            $grandTotalPayout,
            $this->grandTotal['total_win_loss'] ?? 0,
            '',
        ];

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Username',
            'Account',
            'Site',
            'Product',
            'Total Turnover',
            'Total Bet',
            'Total Payout',
            'Total Win/Loss',
            'Last Match Time',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'E' => '0.00',
            'F' => '0.00',
            'G' => '0.00',
            'H' => '0.00',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Style for headers - bold text
        $sheet->getStyle('A1:I1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        // Get the last row number
        $lastRow = count($this->data) + 2; // +2 for header and grand total

        // Style for grand total row - bold text
        $sheet->getStyle("A{$lastRow}:I{$lastRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        // Apply custom number format to ensure zeros display as 0.00
        for ($row = 2; $row <= $lastRow; $row++) {
            for ($col = 'E'; $col <= 'H'; $col++) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // If the value is null or empty, explicitly set it to 0
                if ($value === null || $value === '') {
                    $cell->setValue(0);
                }

                // Apply the number format
                $sheet->getStyle($col.$row)->getNumberFormat()->setFormatCode('0.00');
            }
        }
    }
}
