<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class MachineSummaryExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        $exportData = [];

        foreach ($this->data['items'] as $item) {
            $exportData[] = [
                $item['machine_no'],
                $item['machine_name'],
                $item['token_in'],
                $item['token_out'],
                $item['total_profit'],
            ];
        }

        // Add grand total row
        $exportData[] = [
            'Grand Total',
            '',
            $this->data['grand_total']['token_in'],
            $this->data['grand_total']['token_out'],
            $this->data['grand_total']['total_profit'],
        ];

        return $exportData;
    }

    public function headings(): array
    {
        return [
            'Machine Number',
            'Machine Name',
            'Token In',
            'Token Out',
            'Total Profit',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_NUMBER_00,
            'D' => NumberFormat::FORMAT_NUMBER_00,
            'E' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Style for headers - only bold text
        $sheet->getStyle('A1:E1')->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);

        // Style for grand total row - only bold text
        $lastRow = count($this->data['items']) + 2; // +2 for header and 0-based index
        $sheet->getStyle("A{$lastRow}:E{$lastRow}")->applyFromArray([
            'font' => [
                'bold' => true,
            ],
        ]);
    }
}
