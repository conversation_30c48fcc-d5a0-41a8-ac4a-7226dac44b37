<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ActiveUsersExport implements FromArray, ShouldAutoSize, WithHeadings, WithStyles
{
    protected $activeUsers;

    public function __construct($activeUsers)
    {
        $this->activeUsers = $activeUsers;
    }

    public function array(): array
    {
        $data = [];
        
        foreach ($this->activeUsers as $user) {
            // Format the last activity date
            $lastActivityDate = '';
            if (!empty($user['last_activity_at'])) {
                try {
                    $date = new \DateTime($user['last_activity_at']);
                    $lastActivityDate = $date->format('Y-m-d H:i:s');
                } catch (\Exception $e) {
                    $lastActivityDate = $user['last_activity_at'];
                }
            }

            $data[] = [
                'User ID' => $user['id'] ?? '',
                'Name' => $user['name'] ?? '',
                'Phone Number' => $user['phone_no'] ?? '',
                'Store' => $user['store_name'] ?? '',
                'Last Activity' => $lastActivityDate,
            ];
        }

        return $data;
    }

    public function headings(): array
    {
        return [
            'User ID',
            'Name', 
            'Phone Number',
            'Store',
            'Last Activity',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $highestColumn = $sheet->getHighestColumn();

        // Style the header row
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        return $sheet;
    }
}
