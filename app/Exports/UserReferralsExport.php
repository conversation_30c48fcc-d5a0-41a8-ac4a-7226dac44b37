<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class UserReferralsExport implements FromArray, ShouldAutoSize, WithHeadings, WithMapping, WithStyles
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'Store Name',
            'Referrer Name',
            'Downline Name',
            'Total Turnover',
        ];
    }

    public function map($row): array
    {
        return [
            $row['store_name'],
            $row['referrer_name'],
            $row['downline_name'],
            (float) ($row['total_turnover'] ?? 0), // Just convert to float, formatting will be handled by styles
        ];
    }

    public function styles(Worksheet $sheet)
    {
        // Get the highest column letter (should be D for 4 columns)
        $highestColumn = $sheet->getHighestColumn();

        // Style for headers
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        // Get the last row number
        $lastRow = $sheet->getHighestRow();

        // Apply number format to the Total Turnover column (column D)
        for ($row = 2; $row <= $lastRow; $row++) {
            $cell = $sheet->getCell('D'.$row);
            $value = $cell->getValue();

            // If the value is null or empty, explicitly set it to 0
            if ($value === null || $value === '' || ! is_numeric($value)) {
                $cell->setValue(0);
            }

            // Apply the number format (2 decimal places with comma separator)
            $sheet->getStyle('D'.$row)->getNumberFormat()
                ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2);
        }

        return $sheet;
    }
}
