<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DashboardStatisticsExport implements FromArray, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithStyles
{
    protected $statistics;

    protected $storeName;

    protected $multipleStores;

    protected $isAdmin;

    public function __construct($statistics, $storeName, $multipleStores = false)
    {
        $this->statistics = $statistics;
        $this->storeName = $storeName;
        $this->multipleStores = $multipleStores;
        $this->isAdmin = request()->user() && request()->user()->hasRole('admin');
    }

    private function formatNumber($value): float
    {
        // Ensure null, empty, or non-numeric values are converted to 0
        if ($value === null || $value === '' || ! is_numeric($value)) {
            return 0.00;
        }

        // Convert to float to ensure proper decimal formatting
        return (float) $value;
    }

    public function array(): array
    {
        if ($this->multipleStores && is_array($this->statistics)) {
            $data = [];
            foreach ($this->statistics as $storeName => $storeStats) {
                $row = [
                    'Merchant Name' => $storeName,
                    'New Member' => (int) ($storeStats['totalRegistrations'] ?? 0),
                    'Active Users' => (int) ($storeStats['totalActiveUsers'] ?? 0),
                    'First-Time Deposit Count' => (int) ($storeStats['totalFTD']['count'] ?? 0),
                    'First-Time Deposit Amount' => $this->formatNumber($storeStats['totalFTD']['sum'] ?? 0),
                    'Second-Time Deposit Count' => (int) ($storeStats['totalSTD']['count'] ?? 0),
                    'Second-Time Deposit Amount' => $this->formatNumber($storeStats['totalSTD']['sum'] ?? 0),
                    'Third-Time Deposit Count' => (int) ($storeStats['totalTTD']['count'] ?? 0),
                    'Third-Time Deposit Amount' => $this->formatNumber($storeStats['totalTTD']['sum'] ?? 0),
                    'Deposit Count' => (int) ($storeStats['totalDeposits']['count'] ?? 0),
                    'Deposit Amount' => $this->formatNumber($storeStats['totalDeposits']['sum'] ?? 0),
                    'Withdrawal Count' => (int) ($storeStats['totalWithdrawals']['count'] ?? 0),
                    'Withdrawal Amount' => $this->formatNumber($storeStats['totalWithdrawals']['sum'] ?? 0),
                    'Nett Deposit' => $this->formatNumber($storeStats['totalNettDeposits'] ?? 0),
                    'Transfer In Count' => (int) ($storeStats['transferDetails']['transfer_in']['count'] ?? 0),
                    'Transfer In Amount' => $this->formatNumber($storeStats['transferDetails']['transfer_in']['sum'] ?? 0),
                    'Transfer Out Count' => (int) ($storeStats['transferDetails']['transfer_out']['count'] ?? 0),
                    'Transfer Out Amount' => $this->formatNumber($storeStats['transferDetails']['transfer_out']['sum'] ?? 0),
                    'Nett Transfer' => $this->formatNumber($storeStats['totalTransfer']['sum'] ?? 0),
                    'Wallet To Card Count' => (int) ($storeStats['walletToCard']['count'] ?? 0),
                    'Wallet To Card Amount' => $this->formatNumber($storeStats['walletToCard']['sum'] ?? 0),
                    'Card To Wallet Count' => (int) ($storeStats['cardToWallet']['count'] ?? 0),
                    'Card To Wallet Amount' => $this->formatNumber($storeStats['cardToWallet']['sum'] ?? 0),
                    'P2P Transfer In Count' => (int) ($storeStats['p2pTransferIn']['count'] ?? 0),
                    'P2P Transfer In Amount' => $this->formatNumber($storeStats['p2pTransferIn']['sum'] ?? 0),
                    'Turnover Count' => (int) ($storeStats['totalTurnover']['count'] ?? 0),
                    'Turnover Amount' => $this->formatNumber($storeStats['totalTurnover']['sum'] ?? 0),
                ];

                // Only include promotion and adjustment data for admin users
                if ($this->isAdmin) {
                    $row = array_merge($row, [
                        'Promotion In' => $this->formatNumber($storeStats['promotion']['in'] ?? 0),
                        'Promotion Out' => $this->formatNumber($storeStats['promotion']['out'] ?? 0),
                        'Promotion Burnt' => $this->formatNumber($storeStats['promotion']['burnt'] ?? 0),
                        'Adjustment In Count' => (int) ($storeStats['adjustment']['in']['count'] ?? 0),
                        'Adjustment In Amount' => $this->formatNumber($storeStats['adjustment']['in']['sum'] ?? 0),
                        'Adjustment Out Count' => (int) ($storeStats['adjustment']['out']['count'] ?? 0),
                        'Adjustment Out Amount' => $this->formatNumber($storeStats['adjustment']['out']['sum'] ?? 0),
                        'Adjustment Nett' => $this->formatNumber($storeStats['adjustment']['nett'] ?? 0),
                        'Advance Credit Count' => (int) ($storeStats['advanceCredit']['count'] ?? 0),
                        'Advance Credit Amount' => $this->formatNumber($storeStats['advanceCredit']['sum'] ?? 0),
                    ]);
                }

                $data[] = $row;
            }

            // Add a grand total row if there are multiple stores
            if (count($data) > 1) {
                $grandTotalRow = ['Merchant Name' => 'Grand Total'];
                $numericColumns = array_keys($data[0]);
                array_shift($numericColumns); // Remove 'Merchant Name'

                // Calculate totals for each column
                foreach ($numericColumns as $column) {
                    $grandTotalRow[$column] = array_sum(array_column($data, $column));
                }

                $data[] = $grandTotalRow;
            }

            return $data;
        }

        // Original single store format
        $row = [
            'Merchant Name' => $this->storeName,
            'New Member' => (int) ($this->statistics['totalRegistrations'] ?? 0),
            'Active Users' => (int) ($this->statistics['totalActiveUsers'] ?? 0),
            'First-Time Deposit Count' => (int) ($this->statistics['totalFTD']['count'] ?? 0),
            'First-Time Deposit Amount' => $this->formatNumber($this->statistics['totalFTD']['sum'] ?? 0),
            'Second-Time Deposit Count' => (int) ($this->statistics['totalSTD']['count'] ?? 0),
            'Second-Time Deposit Amount' => $this->formatNumber($this->statistics['totalSTD']['sum'] ?? 0),
            'Third-Time Deposit Count' => (int) ($this->statistics['totalTTD']['count'] ?? 0),
            'Third-Time Deposit Amount' => $this->formatNumber($this->statistics['totalTTD']['sum'] ?? 0),
            'Deposit Count' => (int) ($this->statistics['totalDeposits']['count'] ?? 0),
            'Deposit Amount' => $this->formatNumber($this->statistics['totalDeposits']['sum'] ?? 0),
            'Withdrawal Count' => (int) ($this->statistics['totalWithdrawals']['count'] ?? 0),
            'Withdrawal Amount' => $this->formatNumber($this->statistics['totalWithdrawals']['sum'] ?? 0),
            'Nett Deposit' => $this->formatNumber($this->statistics['totalNettDeposits'] ?? 0),
            'Transfer In Count' => (int) ($this->statistics['transferDetails']['transfer_in']['count'] ?? 0),
            'Transfer In Amount' => $this->formatNumber($this->statistics['transferDetails']['transfer_in']['sum'] ?? 0),
            'Transfer Out Count' => (int) ($this->statistics['transferDetails']['transfer_out']['count'] ?? 0),
            'Transfer Out Amount' => $this->formatNumber($this->statistics['transferDetails']['transfer_out']['sum'] ?? 0),
            'Nett Transfer' => $this->formatNumber($this->statistics['totalTransfer']['sum'] ?? 0),
            'Wallet To Card Count' => (int) ($this->statistics['walletToCard']['count'] ?? 0),
            'Wallet To Card Amount' => $this->formatNumber($this->statistics['walletToCard']['sum'] ?? 0),
            'Card To Wallet Count' => (int) ($this->statistics['cardToWallet']['count'] ?? 0),
            'Card To Wallet Amount' => $this->formatNumber($this->statistics['cardToWallet']['sum'] ?? 0),
            'P2P Transfer In Count' => (int) ($this->statistics['p2pTransferIn']['count'] ?? 0),
            'P2P Transfer In Amount' => $this->formatNumber($this->statistics['p2pTransferIn']['sum'] ?? 0),
            'Turnover Count' => (int) ($this->statistics['totalTurnover']['count'] ?? 0),
            'Turnover Amount' => $this->formatNumber($this->statistics['totalTurnover']['sum'] ?? 0),
        ];

        // Only include promotion and adjustment data for admin users
        if ($this->isAdmin) {
            $row = array_merge($row, [
                'Promotion In' => $this->formatNumber($this->statistics['promotion']['in'] ?? 0),
                'Promotion Out' => $this->formatNumber($this->statistics['promotion']['out'] ?? 0),
                'Promotion Burnt' => $this->formatNumber($this->statistics['promotion']['burnt'] ?? 0),
                'Adjustment In Count' => (int) ($this->statistics['adjustment']['in']['count'] ?? 0),
                'Adjustment In Amount' => $this->formatNumber($this->statistics['adjustment']['in']['sum'] ?? 0),
                'Adjustment Out Count' => (int) ($this->statistics['adjustment']['out']['count'] ?? 0),
                'Adjustment Out Amount' => $this->formatNumber($this->statistics['adjustment']['out']['sum'] ?? 0),
                'Adjustment Nett' => $this->formatNumber($this->statistics['adjustment']['nett'] ?? 0),
                'Advance Credit Count' => (int) ($this->statistics['advanceCredit']['count'] ?? 0),
                'Advance Credit Amount' => $this->formatNumber($this->statistics['advanceCredit']['sum'] ?? 0),
            ]);
        }

        return [$row];
    }

    public function headings(): array
    {
        $headings = [
            'Merchant Name',
            'New Member',
            'Active Users',
            'First-Time Deposit Count',
            'First-Time Deposit Amount',
            'Second-Time Deposit Count',
            'Second-Time Deposit Amount',
            'Third-Time Deposit Count',
            'Third-Time Deposit Amount',
            'Deposit Count',
            'Deposit Amount',
            'Withdrawal Count',
            'Withdrawal Amount',
            'Nett Deposit',
            'Transfer In Count',
            'Transfer In Amount',
            'Transfer Out Count',
            'Transfer Out Amount',
            'Nett Transfer',
            'Wallet To Card Count',
            'Wallet To Card Amount',
            'Card To Wallet Count',
            'Card To Wallet Amount',
            'P2P Transfer In Count',
            'P2P Transfer In Amount',
            'Turnover Count',
            'Turnover Amount',
        ];

        // Only include promotion and adjustment headings for admin users
        if ($this->isAdmin) {
            $headings = array_merge($headings, [
                'Promotion In',
                'Promotion Out',
                'Promotion Burnt',
                'Adjustment In Count',
                'Adjustment In Amount',
                'Adjustment Out Count',
                'Adjustment Out Amount',
                'Adjustment Nett',
                'Advance Credit Count',
                'Advance Credit Amount',
            ]);
        }

        return $headings;
    }

    public function columnFormats(): array
    {
        // We're now handling format in the styles method directly
        return [];
    }

    public function styles(Worksheet $sheet)
    {
        // Get the highest column letter
        $highestColumn = $sheet->getHighestColumn();

        // Style for headers
        $sheet->getStyle('A1:'.$highestColumn.'1')->applyFromArray([
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'EEEEEE'],
            ],
        ]);

        // Get the last row number
        $lastRow = $sheet->getHighestRow();

        // Style for grand total row if exists
        if ($this->multipleStores) {
            $sheet->getStyle("A{$lastRow}:".$highestColumn.$lastRow)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E9F1FA'],
                ],
            ]);
        }

        // Define count and monetary columns dynamically based on headings
        $countColumns = [];
        $monetaryColumns = [];

        // Get all headings to determine column positions
        $headings = $this->headings();
        $colIndex = 'A';

        foreach ($headings as $index => $heading) {
            // Skip merchant name column (A)
            if ($index > 0) {
                $colIndex++;
                // Check if this is a count or monetary column based on heading name
                if (strpos($heading, 'Count') !== false || $heading == 'New Member' || $heading == 'Active Users') {
                    $countColumns[] = $colIndex;
                } else if (strpos($heading, 'Amount') !== false || strpos($heading, 'Nett') !== false ||
                          strpos($heading, 'Promotion') !== false || $heading == 'Adjustment Nett') {
                    $monetaryColumns[] = $colIndex;
                }
            }
        }

         // Process all data rows (skip header row)
         for ($row = 2; $row <= $lastRow; $row++) {
            // Handle count columns (integer format)
            foreach ($countColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // Set empty or null values to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply integer format
                $sheet->getStyle($col.$row)
                    ->getNumberFormat()
                    ->setFormatCode('#,##0');
            }

            // Handle monetary columns (decimal format)
            foreach ($monetaryColumns as $col) {
                $cell = $sheet->getCell($col.$row);
                $value = $cell->getValue();

                // Set empty or null values to 0
                if ($value === null || $value === '' || ! is_numeric($value)) {
                    $cell->setValue(0);
                }

                // Apply decimal format
                $sheet->getStyle($col.$row)
                    ->getNumberFormat()
                    ->setFormatCode('#,##0.00');
            }
        }

        return $sheet;
    }
}
