<?php

namespace App\Services;

use App\Services\ClickHouse\WWJService as ClickHouseWWJService;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class WWJService
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function import()
    {
        // Todo:Optimize the logic.
        $start_time = $date ?? now()->addDays(-1)->format('Y-m-d\T20:00:00.000Z');
        $end_time = $date ?? now()->format('Y-m-d\T23:59:59.000Z');

        $agents = (new ClickHouseWWJService)->getAgentIds();

        foreach ($agents as $agent) {
            echo $agent['code'].' for '.$start_time.' to '.$end_time."\n";
            $response = $this->getGameBetTransactions(['start_time' => $start_time, 'end_time' => $end_time, 'agent_id' => $agent['code']]);
            (new ClickHouseWWJService)->insertBetTransactionLog($response, $agent['code']);
        }
    }

    public function generateAccessToken($qString, $agentId)
    {
        $firstRandStr = random_int(100000, 999999);
        $lastRandStr = str()->random(6);
        $date = gmdate('Ymd');

        $keyG = md5($date.$agentId.env('JK_AGENT_KEY'));
        $md5String = md5($qString.$keyG);

        return $firstRandStr.$md5String.$lastRandStr;
    }

    public function getGameBetTransactions($params = [])
    {
        $account = '';
        $token = $this->generateAccessToken('GameId=101&Account='.$account.'&StartTime='.$params['start_time'].'&EndTime='.$params['end_time'].'&AgentId='.$params['agent_id'], $params['agent_id']);
        $response = $this->httpPostRequest('/GetBetTransactions', [
            'GameId' => 101,
            'Account' => $account,
            'StartTime' => $params['start_time'],
            'EndTime' => $params['end_time'],
            'AgentId' => $params['agent_id'],
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(3)->post(env('JK_API_URL').$path, $params);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } elseif (! $response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('CQ9 Provider server error.'));
        }

        return $response;
    }
}
