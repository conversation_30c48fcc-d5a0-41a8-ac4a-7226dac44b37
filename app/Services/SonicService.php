<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class SonicService
{
    public $baseUrl = 'https://liveposapi.sonicsdk.com';

    public function __construct() {}

    public function getMachineSummary($storeId, $startDate, $endDate)
    {
        $url = $this->baseUrl.'/MachineSummary';

        $response = Http::withHeaders([
            'Accept' => '*/*',
            'Content-Type' => 'application/json',
        ])
            ->post($url, [
                'storeID' => (int) $storeId,
                'startDate' => $startDate,
                'endDate' => $endDate,
            ]);

        return $response;
    }

    public function getPosReport($storeId, $startDate, $endDate, $page, $perPage, $isIncludeOnline, $operationType = 1)
    {
        $url = $this->baseUrl.'/MembershipData';

        $response = Http::withHeaders([
            'Accept' => '*/*',
            'Content-Type' => 'application/json',
        ])
            ->post($url, [
                'storeID' => (int) $storeId,
                'page' => (int) $page,
                'pageSize' => (int) $perPage,
                'isIncludeOnline' => $isIncludeOnline,
                'operationType' => $operationType,
                'startDate' => $startDate,
                'endDate' => $endDate,
            ]);

        return $response;
    }

    public function getCurrencyDetail($storeId, $startDate, $endDate, $page, $perPage)
    {
        $url = $this->baseUrl.'/CurrencyDetail';

        $response = Http::withHeaders([
            'Accept' => '*/*',
            'Content-Type' => 'application/json',
        ])
            ->post($url, [
                'storeID' => (int) $storeId,
                'page' => (int) $page,
                'pageSize' => (int) $perPage,
                'startDate' => $startDate,
                'endDate' => $endDate,
            ]);

        return $response;
    }

    public function getStore()
    {
        $url = $this->baseUrl . '/GetStore';

        $response = Http::withHeaders([
            'Accept' => '*/*',
            'Content-Type' => 'application/json',
        ])->get($url);

        return $response;
    }
}
