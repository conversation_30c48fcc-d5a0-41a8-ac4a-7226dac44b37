<?php

namespace App\Services;

use App\Models\CurlLog;
use Illuminate\Http\Client\Response as HttpResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class JKService
{
    protected string $baseUrl;

    protected string $agentId;

    protected string $agentKey;

    protected ?string $key;

    public function __construct()
    {
        if (env('APP_ENV') == 'local' || env('APP_ENV') == 'staging') {
            $this->baseUrl = env('JK_STAGING_API_URL');
            $this->agentId = env('JK_STAGING_AGENT_ID');
            $this->agentKey = env('JK_STAGING_AGENT_KEY');
        } elseif (env('APP_ENV') == 'production') {
            $this->baseUrl = env('JK_LIVE_API_URL');
            $this->agentId = env('JK_LIVE_AGENT_ID');
            $this->agentKey = env('JK_LIVE_AGENT_KEY');
        }
    }

    public function generateAccessToken($qString, $agentId)
    {
        $firstRandStr = random_int(100000, 999999);
        $lastRandStr = str()->random(6);
        $date = gmdate('Ymd');

        $keyG = md5($date.$agentId.$this->agentKey);
        $md5String = md5($qString.$keyG);

        return $firstRandStr.$md5String.$lastRandStr;
    }

    public function getLoginUrl($account, $cashIn, $agentId, $useDbCash = false)
    {
        // $this->logout($account);
        $isDbCash = $useDbCash ? 1 : 0;

        // $account = 'fws_oVtynAMT8nFx';
        // $cashIn = 50;
        // $agentId = 'Dev-01';
        // $isDbCash = 0;

        $token = $this->generateAccessToken('Account='.$account.'&GameId=101&CashIn='.$cashIn.'&UseDbCash='.$isDbCash.'&AgentId='.$agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl.'/Login', [
            'Account' => $account,
            'GameId' => 101,
            'CashIn' => $cashIn,
            'UseDbCash' => $isDbCash,
            'Lang' => 'en-EN',
            'EnableFullScreen' => 1,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        $data = $response->json();
        if ($data['Error'] == 0) {
            return [
                'status' => true,
                'data' => $data['Data'],
            ];
        }

        return [
            'status' => false,
            'data' => null,
        ];
    }

    public function logout($account, $agentId)
    {
        $token = $this->generateAccessToken('Account='.$account.'&GameId=101&AgentId='.$agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl.'/Logout', [
            'Account' => $account,
            'GameId' => 101,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        $data = $response->json();
        if ($data['Error'] == 0) {
            return [
                'status' => true,
                'data' => $data['State'],
            ];
        }

        return [
            'status' => false,
            'data' => $data['State'],
        ];
    }

    public function deposit($amount)
    {
        return [
            'status' => true,
            'data' => [
                'main_wallet' => $amount,
            ],
        ];
    }

    public function withdraw($account, $agentId)
    {
        $token = $this->generateAccessToken('Account='.$account.'&AgentId='.$agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl.'/Withdraw', [
            'Account' => $account,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    public function refundCoin($account, $refundKey, $agentId)
    {
        $token = $this->generateAccessToken('Account='.$account.'&RefundKey='.$refundKey.'&AgentId='.$agentId, $agentId);
        $response = $this->httpPostRequest($this->baseUrl.'/RefundCoins', [
            'Account' => $account,
            'RefundKey' => $refundKey,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    public function getBetTransaction($account, $startTime, $endTime, $agentId)
    {
        $token = $this->generateAccessToken('GameId=101&Account='.$account.'&StartTime='.$startTime.'&EndTime='.$endTime.'&AgentId='.$agentId, $agentId);
        $response = $this->httpPostRequest(env('JK_BASE_URL').'/GetBetTransactions', [
            'GameId' => 101,
            'Account' => $account,
            'StartTime' => $startTime,
            'EndTime' => $endTime,
            'AgentId' => $agentId,
            'Key' => $token,
        ])->throw();

        return $response->json();
    }

    // public function getBetTransactionByAccount($account, $startTime, $endTime, $agentId)
    // {
    //     $token = $this->generateAccessToken('GameId=101&Account=' . $account . '&StartTime=' . $startTime . '&EndTime=' . $endTime . '&AgentId=' . $agentId, $agentId);
    //     $response = $this->httpPostRequest(env('JK_BASE_URL') . '/GetBetTransactions', [
    //         'GameId' => 101,
    //         'Account' => $account,
    //         'StartTime' => $startTime,
    //         'EndTime' => $endTime,
    //         'AgentId' => $agentId,
    //         'Key' => $token,
    //     ])->throw();

    //     return $response->json();
    // }

    protected function httpPostRequest(string $path, array $params): HttpResponse
    {
        $response = Http::asForm()->timeout(3)->post($path, $params);

        CurlLog::create([
            'endpoint' => $path ?? '',
            'request' => isset($params) ? (is_array($params) ? json_encode($params) : $params) : '',
            'response' => $response ?? '',
            'created_at' => now(),
        ]);

        if ($response->json('code') && $response->json('code') != 1000) {
            abort(Response::HTTP_UNPROCESSABLE_ENTITY, $response->json('message'));
        } elseif (! $response->ok()) {
            abort(Response::HTTP_FAILED_DEPENDENCY, __('Server error.'));
        }

        return $response;
    }
}
