<?php

namespace App\Services\ClickHouse;

use App\Facades\ClickHouse;
use App\Traits\DateTrait;
use Carbon\Carbon;

class GSCService
{
    use DateTrait;

    public function getGscTransactions($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y-m-d H:i:s');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y-m-d H:i:s');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y-m-d H:i:s');
            $queryParams['end'] = Carbon::now()->format('Y-m-d H:i:s');
        }

        if (isset($params['account'])) {
            $queryParams['account'] = $params['account'];
        }

        $baseQuery = 'SELECT
                players.username AS username,
                gpt.member AS account,
                MAX(gpt.site) AS site,
                MAX(gpt.product) AS product,
                SUM(gpt.turnover) AS total_turnover,
                SUM(gpt.bet) AS total_bet,
                SUM(gpt.payout) AS total_payout,
                SUM(gpt.payout) - SUM(gpt.bet) AS total_win_loss,
                MAX(gpt.match_time_mas) AS last_match_time
            FROM
                gsc_player_transactions gpt
            JOIN players
                ON LOWER(gpt.member) = LOWER(players.account)
            WHERE
                gpt.match_time_mas >= :start AND gpt.match_time_mas <= :end';

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        if (isset($params['account']) && ! empty($params['account'])) {
            $baseQuery .= ' AND gpt.member LIKE :account';
            $queryParams['account'] = '%'.$params['account'].'%';
        }

        if (isset($params['username']) && ! empty($params['username'])) {
            $baseQuery .= ' AND players.username LIKE :username';
            $queryParams['username'] = '%'.$params['username'].'%';
        }

        $baseQuery .= ' GROUP BY gpt.member, players.username';

        $countQuery = "SELECT COUNT() as count FROM ($baseQuery)";
        $totalCount = (int) ClickHouse::select($countQuery, $queryParams)->rows()[0]['count'];

        $currentPage = (int) ($params['page'] ?? 1);
        $perPage = (int) ($params['pageSize'] ?? 10);
        $offset = ($currentPage - 1) * $perPage;
        $lastPage = ceil($totalCount / $perPage);

        // Get sort parameters
        $sortField = $params['sort'] ?? 'last_match_time';
        $sortDirection = $params['direction'] ?? 'DESC';

        // Define mapping for sort fields if needed
        $sortFieldMap = [
            'username' => 'players.username',
            'account' => 'gpt.member',
            'site' => 'site',
            'product' => 'product',
            'total_turnover' => 'total_turnover',
            'total_bet' => 'total_bet',
            'total_payout' => 'total_payout',
            'total_win_loss' => 'total_win_loss',
            'last_match_time' => 'last_match_time'
        ];

        // Use mapped field name if available, otherwise use the provided field
        $dbSortField = $sortFieldMap[$sortField] ?? 'last_match_time';

        // Validate sort direction
        $sortDirection = strtolower($sortDirection) === 'asc' ? 'ASC' : 'DESC';

        $baseQuery .= " ORDER BY $dbSortField $sortDirection LIMIT $perPage OFFSET $offset";
        $results = ClickHouse::select($baseQuery, $queryParams)->rows();

        $formattedResults = array_map(function ($row) {
            $row['last_match_time'] = self::dateFormat($row['last_match_time']);

            return $row;
        }, $results);

        $links = [];

        $links[] = [
            'url' => '?page=1',
            'label' => '1',
            'active' => $currentPage === 1,
        ];

        if ($lastPage > 1) {
            if ($currentPage > 3) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            for ($i = max(2, $currentPage - 1); $i <= min($lastPage - 1, $currentPage + 1); $i++) {
                if ($i > 1 && $i < $lastPage) {
                    $links[] = [
                        'url' => '?page='.$i,
                        'label' => (string) $i,
                        'active' => $currentPage === $i,
                    ];
                }
            }

            if ($currentPage < ($lastPage - 2)) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            if ($lastPage > 1) {
                $links[] = [
                    'url' => '?page='.$lastPage,
                    'label' => (string) $lastPage,
                    'active' => $currentPage === $lastPage,
                ];
            }
        }

        $totalQuery = 'SELECT
                SUM(gpt.turnover) AS total_turnover,
                SUM(gpt.bet) AS total_bet,
                SUM(gpt.payout) AS total_payout,
                SUM(gpt.payout) - SUM(gpt.bet) AS total_win_loss
            FROM
                gsc_player_transactions gpt
            JOIN players
                ON LOWER(gpt.member) = LOWER(players.account)
            WHERE
                gpt.match_time_mas >= :start AND gpt.match_time_mas <= :end';

        // Handle store_id parameter for totals query
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $totalQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $totalQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $totalQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        if (isset($params['account']) && ! empty($params['account'])) {
            $totalQuery .= ' AND gpt.member LIKE :account';
        }

        if (isset($params['username']) && ! empty($params['username'])) {
            $totalQuery .= ' AND players.username LIKE :username';
        }

        $totals = ClickHouse::select($totalQuery, $queryParams)->rows()[0];

        return [
            'data' => $formattedResults,
            'grand_total' => [
                'total_turnover' => $totals['total_turnover'] ?? 0,
                'total_bet' => $totals['total_bet'] ?? 0,
                'total_payout' => $totals['total_payout'] ?? 0,
                'total_win_loss' => $totals['total_win_loss'] ?? 0,
            ],
            'pagination' => [
                'current_page' => $currentPage,
                'first_page_url' => '?page=1',
                'from' => $offset + 1,
                'last_page' => $lastPage,
                'last_page_url' => '?page='.$lastPage,
                'links' => $links,
                'next_page_url' => $currentPage < $lastPage ? '?page='.($currentPage + 1) : null,
                'path' => '',
                'per_page' => $perPage,
                'prev_page_url' => $currentPage > 1 ? '?page='.($currentPage - 1) : null,
                'to' => $offset + count($formattedResults),
                'total' => $totalCount,
            ],
        ];
    }

    public function getGscDetailTransactions($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y-m-d H:i:s');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y-m-d H:i:s');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y-m-d H:i:s');
            $queryParams['end'] = Carbon::now()->format('Y-m-d H:i:s');
        }

        if (isset($params['account'])) {
            $queryParams['account'] = $params['account'];
        }

        $baseQuery = 'SELECT
                gpt.member AS account,
                players.username,
                gpt.site,
                gpt.product,
                gpt.match_time_mas,
                gpt.turnover,
                gpt.bet,
                gpt.payout,
                gpt.payout - gpt.bet AS win_loss
            FROM
                gsc_player_transactions gpt
            LEFT JOIN players
                ON LOWER(gpt.member) = LOWER(players.account)
            WHERE
                gpt.match_time_mas >= :start AND gpt.match_time_mas <= :end';

        if (isset($params['account'])) {
            $baseQuery .= ' AND LOWER(member) = {account:String}';
        }

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        $countQuery = "SELECT COUNT() as count FROM ($baseQuery)";
        $totalCount = (int) ClickHouse::select($countQuery, $queryParams)->rows()[0]['count'];

        $currentPage = (int) ($params['page'] ?? 1);
        $perPage = (int) ($params['pageSize'] ?? 10);
        $offset = ($currentPage - 1) * $perPage;
        $lastPage = ceil($totalCount / $perPage);

        if (isset($params['sort']) && isset($params['direction'])) {
            $sortField = $params['sort'];
            $sortDirection = $params['direction'];
            $baseQuery .= " ORDER BY $sortField $sortDirection";
        } else {
            $baseQuery .= " ORDER BY match_time_mas DESC";
        }

        $baseQuery .= " LIMIT $perPage OFFSET $offset";
        $results = ClickHouse::select($baseQuery, $queryParams)->rows();

        $formattedResults = array_map(function ($row) {
            $row['match_time_mas'] = self::dateFormat($row['match_time_mas']);

            return $row;
        }, $results);

        $links = [];
        $links[] = [
            'url' => '?page=1',
            'label' => '1',
            'active' => $currentPage === 1,
        ];

        if ($lastPage > 1) {
            if ($currentPage > 3) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            for ($i = max(2, $currentPage - 1); $i <= min($lastPage - 1, $currentPage + 1); $i++) {
                if ($i > 1 && $i < $lastPage) {
                    $links[] = [
                        'url' => '?page='.$i,
                        'label' => (string) $i,
                        'active' => $currentPage === $i,
                    ];
                }
            }

            if ($currentPage < ($lastPage - 2)) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            if ($lastPage > 1) {
                $links[] = [
                    'url' => '?page='.$lastPage,
                    'label' => (string) $lastPage,
                    'active' => $currentPage === $lastPage,
                ];
            }
        }

        return [
            'data' => $formattedResults,
            'pagination' => [
                'current_page' => $currentPage,
                'first_page_url' => '?page=1',
                'from' => $offset + 1,
                'last_page' => $lastPage,
                'last_page_url' => '?page='.$lastPage,
                'links' => $links,
                'next_page_url' => $currentPage < $lastPage ? '?page='.($currentPage + 1) : null,
                'path' => '',
                'per_page' => $perPage,
                'prev_page_url' => $currentPage > 1 ? '?page='.($currentPage - 1) : null,
                'to' => $offset + count($formattedResults),
                'total' => $totalCount,
            ],
        ];
    }
}
