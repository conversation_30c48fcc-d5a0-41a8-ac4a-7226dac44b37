<?php

namespace App\Services\ClickHouse;

use App\Facades\ClickHouse;
use Carbon\Carbon;

class UserService
{
    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function getLastPlayerId($storeId)
    {
        ClickHouse::write('CREATE TABLE IF NOT EXISTS players (
            user_id UInt32,
            username Nullable(String),
            account Nullable(String),
            phone_no Nullable(String),
            store_id Nullable(String),
            created_at DateTime,
            updated_at DateTime
        ) ENGINE = MergeTree()
        ORDER BY created_at');

        return ClickHouse::select("SELECT MAX(user_id) AS last_id FROM players WHERE store_id = '".$storeId."'")->rows()[0]['last_id'] ?? 0;
    }

    public function insertPlayer($users)
    {
        ClickHouse::write('CREATE TABLE IF NOT EXISTS players (
            user_id UInt32,
            username Nullable(String),
            account Nullable(String),
            phone_no Nullable(String),
            store_id Nullable(String),
            created_at DateTime,
            updated_at DateTime
        ) ENGINE = MergeTree()
        ORDER BY created_at');

        $column = ['user_id', 'username', 'account', 'phone_no', 'store_id', 'created_at', 'updated_at'];

        foreach ($users as $user) {
            ClickHouse::insert(
                'players',
                [
                    [
                        'user_id' => $user['user_id'],
                        'username' => $user['username'],
                        'account' => $user['account'],
                        'phone_no' => $user['phone_no'],
                        'store_id' => $user['store_id'],
                        Carbon::now()->utc()->toString(),
                        Carbon::now()->utc()->toString(),
                    ],
                ],
                $column
            );
        }
    }
}
