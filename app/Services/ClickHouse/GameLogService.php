<?php

namespace App\Services\ClickHouse;

use App\Facades\ClickHouse;
use App\Traits\DateTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class GameLogService
{
    use DateTrait;

    /**
     * Get transaction data for a list of specific player accounts from both WWJ and GSC
     */
    public function getPlayersTransactions($params = [])
    {
        $result = [];

        // Format dates for WWJ and GSC queries
        if (isset($params['start']) && isset($params['end'])) {
            $wwjDateStart = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $wwjDateEnd = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
            $gscDateStart = Carbon::parse($params['start'])->format('Y-m-d H:i:s');
            $gscDateEnd = Carbon::parse($params['end'])->format('Y-m-d 23:59:59');
        } else {
            $wwjDateStart = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $wwjDateEnd = Carbon::now()->format('Y/m/d 23:59:59 +08:00');
            $gscDateStart = Carbon::now()->subDays(1)->format('Y-m-d H:i:s');
            $gscDateEnd = Carbon::now()->format('Y-m-d 23:59:59');
        }

        $playerAccounts = $params['accounts'] ?? [];

        if (empty($playerAccounts)) {
            return [];
        }

        // Get WWJ transactions
        $wwjQueryParams = [
            'start' => $wwjDateStart,
            'end' => $wwjDateEnd,
        ];

        $escapedAccounts = array_map(function ($acc) {
            return "'".str_replace("'", "\'", $acc)."'";
        }, $playerAccounts);
        $accountsList = implode(',', $escapedAccounts);

        $wwjQuery = "SELECT
                players.username AS username,
                SUM(pt.bet) AS total_bet
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT('fw_', players.account)
            WHERE
                pt.create_time >= :start AND pt.create_time <= :end
                AND players.username IN ($accountsList)";

        // Handle store filtering for WWJ query
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $wwjQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $wwjQueryParams['store_id'] = (string) $params['store_id'];
                $wwjQuery .= " AND players.store_id = :store_id";
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $wwjQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        $wwjQuery .= " GROUP BY players.username";

        try {
            $wwjResults = ClickHouse::select($wwjQuery, $wwjQueryParams)->rows();

            // Format WWJ results
            foreach ($wwjResults as $row) {
                $username = $row['username'];
                $result[$username] = [
                    'username' => $username,
                    'wwj_total_bet' => (float)($row['total_bet'] ?? 0),
                    'gsc_total_turnover' => 0,
                    'total_turnover' => (float)($row['total_bet'] ?? 0)
                ];
            }

            // Get GSC transactions
            $gscQueryParams = [
                'start' => $gscDateStart,
                'end' => $gscDateEnd,
            ];

            $gscQuery = "SELECT
                    players.username AS username,
                    SUM(gpt.turnover) AS total_turnover
                FROM
                    gsc_player_transactions gpt
                JOIN players
                    ON LOWER(gpt.member) = LOWER(players.account)
                WHERE
                    gpt.match_time_mas >= :start AND gpt.match_time_mas <= :end
                    AND players.username IN ($accountsList)";

            // Handle store filtering for GSC query
            if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
                // Check if it's a comma-separated list of store IDs
                if (strpos($params['store_id'], ',') !== false) {
                    $storeIdArray = explode(',', $params['store_id']);
                    $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                    $gscQuery .= " AND players.store_id IN ($storeIdsStr)";
                } else {
                    // Single store ID
                    $gscQueryParams['store_id'] = (string) $params['store_id'];
                    $gscQuery .= " AND players.store_id = :store_id";
                }
            }
            // Handle store_ids array parameter (used when store_id is 'all')
            elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
                $gscQuery .= " AND players.store_id IN ($storeIdsStr)";
            }

            $gscQuery .= " GROUP BY players.username";

            $gscResults = ClickHouse::select($gscQuery, $gscQueryParams)->rows();

            // Merge GSC results with WWJ results
            foreach ($gscResults as $row) {
                $username = $row['username'];
                if (isset($result[$username])) {
                    $result[$username]['gsc_total_turnover'] = (float)($row['total_turnover'] ?? 0);
                    $result[$username]['total_turnover'] += (float)($row['total_turnover'] ?? 0);
                } else {
                    $result[$username] = [
                        'username' => $username,
                        'wwj_total_bet' => 0,
                        'gsc_total_turnover' => (float)($row['total_turnover'] ?? 0),
                        'total_turnover' => (float)($row['total_turnover'] ?? 0)
                    ];
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error in getPlayersTransactions: ' . $e->getMessage());
            return [];
        }
    }

    public function getAllPlayerLogs($params = [])
    {
        if (isset($params['start']) && isset($params['end'])) {
            $gscDateStart = Carbon::parse($params['start'])->format('Y-m-d H:i:s');
            $gscDateEnd = Carbon::parse($params['end'])->format('Y-m-d H:i:s');
            $wwjDateStart = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $wwjDateEnd = Carbon::parse($params['end'])->format('Y/m/d H:i:s +08:00');
        } else {
            $gscDateStart = Carbon::now()->subDays(1)->format('Y-m-d H:i:s');
            $gscDateEnd = Carbon::now()->format('Y-m-d H:i:s');
            $wwjDateStart = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $wwjDateEnd = Carbon::now()->format('Y/m/d H:i:s +08:00');
        }

        $queryParams = [
            'start_gsc' => $gscDateStart,
            'end_gsc' => $gscDateEnd,
            'start_wwj' => $wwjDateStart,
            'end_wwj' => $wwjDateEnd,
        ];

        try {
            // Build GSC query
            $gscQuery = "SELECT
                    'GSC' AS source,
                    gpt.member AS account,
                    p.username AS username,
                    gpt.match_time_mas AS transaction_time,
                    gpt.site AS site,
                    gpt.product AS product,
                    gpt.turnover AS turnover,
                    gpt.bet AS bet,
                    gpt.payout AS payout,
                    gpt.payout - gpt.bet AS win_loss,
                    0 AS initial_balance,
                    0 AS final_balance
                FROM
                    gsc_player_transactions gpt
                JOIN players p
                    ON LOWER(gpt.member) = LOWER(p.account)
                WHERE
                    gpt.match_time_mas >= :start_gsc
                    AND gpt.match_time_mas <= :end_gsc";

            // Build WWJ query - check actual field names in player_transactions table
            $wwjQuery = "SELECT
                    'WWJ' AS source,
                    pt.account AS account,
                    p.username AS username,
                    pt.create_time AS transaction_time,
                    '' AS site,
                    '' AS product,
                    toFloat64(0) AS turnover,
                    COALESCE(pt.bet, toFloat64(0)) AS bet,
                    toFloat64(0) AS payout,  -- Field doesn't exist in player_transactions
                    COALESCE(pt.win_loss, toFloat64(0)) AS win_loss,
                    COALESCE(pt.initial_balance, toFloat64(0)) AS initial_balance,
                    COALESCE(pt.final_balance, toFloat64(0)) AS final_balance
                FROM
                    player_transactions AS pt
                JOIN players AS p
                    ON pt.account = CONCAT('fw_', p.account)
                WHERE
                    pt.create_time >= :start_wwj
                    AND pt.create_time <= :end_wwj";

            // Handle store_id parameter
            if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
                // Check if it's a comma-separated list of store IDs
                if (strpos($params['store_id'], ',') !== false) {
                    $storeIdArray = explode(',', $params['store_id']);
                    $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                    $gscQuery .= " AND p.store_id IN ($storeIdsStr)";
                    $wwjQuery .= " AND p.store_id IN ($storeIdsStr)";
                } else {
                    // Single store ID
                    $queryParams['store_id'] = (string) $params['store_id'];
                    $gscQuery .= ' AND p.store_id = :store_id';
                    $wwjQuery .= ' AND p.store_id = :store_id';
                }
            }
            // Handle store_ids array parameter (used when store_id is 'all')
            elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
                $gscQuery .= " AND p.store_id IN ($storeIdsStr)";
                $wwjQuery .= " AND p.store_id IN ($storeIdsStr)";
            }

            if (isset($params['account']) && ! empty($params['account'])) {
                $queryParams['account'] = '%'.$params['account'].'%';
                $gscQuery .= ' AND gpt.member LIKE :account';
                $wwjQuery .= ' AND pt.account LIKE :account';
            }

            if (isset($params['username']) && ! empty($params['username'])) {
                $queryParams['username'] = '%'.$params['username'].'%';
                $gscQuery .= ' AND p.username LIKE :username';
                $wwjQuery .= ' AND p.username LIKE :username';
            }

            $type = $params['type'] ?? 'all';

            if ($type === 'gsc') {
                $combinedQuery = $gscQuery;
            } elseif ($type === 'wwj') {
                $combinedQuery = $wwjQuery;
            } else {
                $combinedQuery = "($gscQuery) UNION ALL ($wwjQuery)";
            }

            // Count total records
            $countQuery = "SELECT COUNT() as count FROM ($combinedQuery)";
            $totalCount = (int) ClickHouse::select($countQuery, $queryParams)->rows()[0]['count'];

            // Pagination setup
            $currentPage = (int) ($params['page'] ?? 1);
            $perPage = (int) ($params['pageSize'] ?? 10);
            $offset = ($currentPage - 1) * $perPage;
            $lastPage = ceil($totalCount / $perPage);

            // Final query with pagination and sorting
            $finalQuery = "SELECT * FROM ($combinedQuery) ORDER BY transaction_time DESC LIMIT $perPage OFFSET $offset";
            $results = ClickHouse::select($finalQuery, $queryParams)->rows();

            // Format results
            $formattedResults = array_map(function ($row) {
                $row['transaction_time'] = self::dateFormat($row['transaction_time']);

                return $row;
            }, $results);

            // Calculate totals in a single query
            $totalsQuery = "SELECT
                    SUM(CASE WHEN source = 'GSC' THEN turnover ELSE 0 END) AS total_gsc_turnover,
                    SUM(CASE WHEN source = 'GSC' THEN bet ELSE 0 END) AS total_gsc_bet,
                    SUM(CASE WHEN source = 'GSC' THEN payout ELSE 0 END) AS total_gsc_payout,
                    SUM(CASE WHEN source = 'GSC' THEN win_loss ELSE 0 END) AS total_gsc_win_loss,
                    SUM(CASE WHEN source = 'WWJ' THEN turnover ELSE 0 END) AS total_wwj_turnover,
                    SUM(CASE WHEN source = 'WWJ' THEN bet ELSE 0 END) AS total_wwj_bet,
                    SUM(CASE WHEN source = 'WWJ' THEN payout ELSE 0 END) AS total_wwj_payout,
                    SUM(CASE WHEN source = 'WWJ' THEN win_loss ELSE 0 END) AS total_wwj_win_loss,
                    SUM(CASE WHEN source = 'WWJ' THEN initial_balance ELSE 0 END) AS total_wwj_initial_balance,
                    SUM(CASE WHEN source = 'WWJ' THEN final_balance ELSE 0 END) AS total_wwj_final_balance
                FROM ($combinedQuery)";

            $totals = ClickHouse::select($totalsQuery, $queryParams)->rows()[0];

            return [
                'data' => $formattedResults,
                'grand_total' => [
                    'total_gsc_turnover' => $totals['total_gsc_turnover'] ?? 0,
                    'total_gsc_bet' => $totals['total_gsc_bet'] ?? 0,
                    'total_gsc_payout' => $totals['total_gsc_payout'] ?? 0,
                    'total_gsc_win_loss' => $totals['total_gsc_win_loss'] ?? 0,
                    'total_wwj_turnover' => $totals['total_wwj_turnover'] ?? 0,
                    'total_wwj_bet' => $totals['total_wwj_bet'] ?? 0,
                    'total_wwj_payout' => $totals['total_wwj_payout'] ?? 0,
                    'total_wwj_win_loss' => $totals['total_wwj_win_loss'] ?? 0,
                    'total_wwj_initial_balance' => $totals['total_wwj_initial_balance'] ?? 0,
                    'total_wwj_final_balance' => $totals['total_wwj_final_balance'] ?? 0,
                    'total_turnover' => ($totals['total_gsc_turnover'] ?? 0) + ($totals['total_wwj_turnover'] ?? 0),
                    'total_bet' => ($totals['total_gsc_bet'] ?? 0) + ($totals['total_wwj_bet'] ?? 0),
                    'total_payout' => ($totals['total_gsc_payout'] ?? 0) + ($totals['total_wwj_payout'] ?? 0),
                    'total_win_loss' => ($totals['total_gsc_win_loss'] ?? 0) + ($totals['total_wwj_win_loss'] ?? 0),
                ],
                'pagination' => [
                    'current_page' => $currentPage,
                    'first_page_url' => '?page=1',
                    'from' => $offset + 1,
                    'last_page' => $lastPage,
                    'last_page_url' => '?page='.$lastPage,
                    'links' => $this->generatePaginationLinks($currentPage, $lastPage),
                    'next_page_url' => $currentPage < $lastPage ? '?page='.($currentPage + 1) : null,
                    'path' => '',
                    'per_page' => $perPage,
                    'prev_page_url' => $currentPage > 1 ? '?page='.($currentPage - 1) : null,
                    'to' => $offset + count($formattedResults),
                    'total' => $totalCount,
                ],
            ];

        } catch (\Exception $exception) {
            // Log the exception for debugging
            Log::error('Error in getAllPlayerLogs: ' . $exception->getMessage());
            return $this->emptyResponse();
        }
    }

    private function emptyResponse()
    {
        return [
            'data' => [],
            'grand_total' => [
                'total_gsc_turnover' => 0,
                'total_gsc_bet' => 0,
                'total_gsc_payout' => 0,
                'total_gsc_win_loss' => 0,
                'total_wwj_turnover' => 0,
                'total_wwj_bet' => 0,
                'total_wwj_payout' => 0,
                'total_wwj_win_loss' => 0,
                'total_wwj_initial_balance' => 0,
                'total_wwj_final_balance' => 0,
                'total_turnover' => 0,
                'total_bet' => 0,
                'total_payout' => 0,
                'total_win_loss' => 0,
            ],
            'pagination' => [
                'current_page' => 1,
                'first_page_url' => '?page=1',
                'from' => 0,
                'last_page' => 1,
                'last_page_url' => '?page=1',
                'links' => [
                    [
                        'url' => '?page=1',
                        'label' => '1',
                        'active' => true,
                    ],
                ],
                'next_page_url' => null,
                'path' => '',
                'per_page' => 10,
                'prev_page_url' => null,
                'to' => 0,
                'total' => 0,
            ],
        ];
    }

    private function generatePaginationLinks($currentPage, $lastPage)
    {
        $links = [];
        $links[] = [
            'url' => '?page=1',
            'label' => '1',
            'active' => $currentPage === 1,
        ];

        if ($lastPage > 1) {
            if ($currentPage > 3) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            for ($i = max(2, $currentPage - 1); $i <= min($lastPage - 1, $currentPage + 1); $i++) {
                if ($i > 1 && $i < $lastPage) {
                    $links[] = [
                        'url' => '?page='.$i,
                        'label' => (string) $i,
                        'active' => $currentPage === $i,
                    ];
                }
            }

            if ($currentPage < ($lastPage - 2)) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            if ($lastPage > 1) {
                $links[] = [
                    'url' => '?page='.$lastPage,
                    'label' => (string) $lastPage,
                    'active' => $currentPage === $lastPage,
                ];
            }
        }

        return $links;
    }
}
