<?php

namespace App\Services\ClickHouse;

use App\Facades\ClickHouse;
use App\Traits\DateTrait;
use Carbon\Carbon;

class WWJService
{
    use DateTrait;

    /**
     * Create a new class instance.
     */
    public function __construct()
    {
        //
    }

    public function getAgentIds()
    {
        $agentCodes = ClickHouse::select('SELECT * FROM agent_codes')->rows();

        return $agentCodes;
    }

    public function insertMerchantPlayer($account, $merchantId)
    {
        ClickHouse::write('CREATE TABLE IF NOT EXISTS merchant_player (
            account Nullable(String),
            merchant_code Nullable(String),
            created_at DateTime,
            updated_at DateTime
        ) ENGINE = MergeTree()
        ORDER BY created_at');

        $isExist = count(ClickHouse::select("SELECT * FROM merchant_player WHERE account = '".$account."'")->rows()) > 0;

        if (! $isExist) {
            $column = ['account', 'merchant_code', 'created_at', 'updated_at'];

            ClickHouse::insert(
                'merchant_player',
                [
                    [
                        'account' => $account,
                        'merchant_code' => $merchantId,
                        Carbon::now()->utc()->toString(),
                        Carbon::now()->utc()->toString(),
                    ],
                ],
                $column
            );
        }
    }

    public function insertBetTransactionLog($params, $agentId)
    {
        ClickHouse::write('CREATE TABLE IF NOT EXISTS player_transactions (
            sn UInt32,
            agent_id Nullable(String),
            account Nullable(String),
            create_time Nullable(String),
            initial_balance Nullable(Float64),
            bet Nullable(Float64),
            final_balance Nullable(Float64),
            win_loss Nullable(Float64),
            win_loss_status Nullable(String),
            created_at DateTime,
            updated_at DateTime
        ) ENGINE = MergeTree()
        ORDER BY sn');

        $lastRow = ClickHouse::select("SELECT MAX(sn) AS last_id FROM player_transactions WHERE agent_id = '".$agentId."'");
        $lastID = $lastRow->rows()[0]['last_id'] ?? 0;

        if ($params['Error'] == 0 && $params['State'] == 'Success') {
            $formatted = collect($params['Data']['List'])
                ->filter(function ($item) use ($lastID) {
                    return $item['Sn'] > $lastID;
                })
                ->map(function ($item) {
                    return [
                        $item['Sn'],
                        $item['AgentId'],
                        $item['Account'],
                        $item['CreateTime'],
                        $item['TurnOver'],
                        $item['Bet'],
                        $item['Return'],
                        $item['WinLoss'],
                        $item['Status'],
                        Carbon::createFromFormat('Y/m/d H:i:s P', $item['CreateTime'])->utc()->toString(),
                        Carbon::createFromFormat('Y/m/d H:i:s P', $item['CreateTime'])->utc()->toString(),
                    ];
                })->all();

            if (count($formatted) == 0) {
                echo 'Empty records, skipped.'."\n";

                return;
            }

            $column = ['sn', 'agent_id', 'account', 'create_time', 'initial_balance', 'bet', 'final_balance', 'win_loss', 'win_loss_status', 'created_at', 'updated_at'];

            ClickHouse::insert(
                'player_transactions',
                $formatted,
                $column
            );

            echo 'Done insert for '.count($formatted)."\n";
        }
    }

    public function getAllBetTransaction($params = [])
    {
        $query = 'SELECT
                pt.*, mp.merchant_code FROM player_transactions pt LEFT JOIN merchant_player mp
                ON pt.account = mp.account WHERE pt.create_time >= :start AND pt.create_time <= :end
                AND pt.agent_id = :agent_id AND mp.merchant_code = :merchant_code';
        $params = array_filter($params);

        if (isset($params['account'])) {
            $query .= ' AND account = :account';
            $params['account'] = $params['account'];
        }

        // TODO: Fix hardcode start end date time
        if (isset($params['start']) && isset($params['end'])) {
            $params['start'] = Carbon::parse($params['start'])->format('Y/m/d 00:00:00 +08:00');
            $params['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $params['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $params['end'] = Carbon::parse(now()->format('Y-m-d').' 23:59:59')->format('Y/m/d H:i:s +08:00');
        }
        $offset = $params['page'] ? ($params['page'] - 1) * $params['pageSize'] : 0;
        $totalCount = ClickHouse::select($query, $params)->count();

        $data = [
            'currentPage' => $params['page'],
            'pageSize' => $params['pageSize'],
            'lastPage' => (int) ceil($totalCount / $params['pageSize']),
            'total' => $totalCount,
            'data' => ClickHouse::select($query.' ORDER BY sn DESC LIMIT :pageSize'.($offset > 0 ? ' OFFSET '.$offset : ''), $params)->rows(),
        ];

        return $data;
    }

    public function getAllPlayer($params = [])
    {
        $query = 'SELECT * FROM merchant_player WHERE merchant_code = :merchant_code';
        $params = array_filter($params);

        if (isset($params['account'])) {
            $query .= ' AND account = :account';
            $params['account'] = $params['account'];
        }

        // TODO: Fix hardcode start end date time
        if (isset($params['start']) && isset($params['end'])) {
            $params['start'] = Carbon::parse($params['start'])->format('Y/m/d 00:00:00');
            $params['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59');
        } else {
            $params['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s');
            $params['end'] = Carbon::parse(now()->format('Y-m-d').' 23:59:59')->format('Y/m/d H:i:s');
        }
        $offset = $params['page'] ? ($params['page'] - 1) * $params['pageSize'] : 0;
        $totalCount = ClickHouse::select($query, $params)->count();

        $data = [
            'currentPage' => $params['page'],
            'pageSize' => $params['pageSize'],
            'lastPage' => (int) ceil($totalCount / $params['pageSize']),
            'total' => $totalCount,
            'data' => ClickHouse::select($query.' ORDER BY created_at DESC LIMIT :pageSize'.($offset > 0 ? ' OFFSET '.$offset : ''), $params)->rows(),
        ];

        return $data;
    }

    public function getMerchantSummary($params = [])
    {
        $query = 'SELECT
                SUM(pt.bet) AS total_bet,
                SUM(pt.win_loss) AS total_win_loss,
                SUMIf(pt.win_loss, pt.win_loss > 0) AS player_win,
                SUMIf(pt.win_loss, pt.win_loss < 0) AS player_loss,
                COUNT(pt.sn) AS total_bet_count,
                COUNT(DISTINCT pt.account) AS total_player_count,
                MIN(mp.merchant_code) AS merchant_code FROM player_transactions pt LEFT JOIN merchant_player mp
                ON pt.account = mp.account WHERE pt.create_time >= :start AND pt.create_time <= :end
                AND pt.agent_id = :agent_id AND mp.merchant_code = :merchant_code';
        $params = array_filter($params);

        if (isset($params['account'])) {
            $query .= ' AND account = :account';
            $params['account'] = $params['account'];
        }

        // TODO: Fix hardcode start end date time
        if (isset($params['start']) && isset($params['end'])) {
            $params['start'] = Carbon::parse($params['start'])->format('Y/m/d 00:00:00 +08:00');
            $params['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $params['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $params['end'] = Carbon::parse(now()->format('Y-m-d').' 23:59:59')->format('Y/m/d H:i:s +08:00');
        }

        $data = ClickHouse::select($query, $params)->rows()[0];

        return $data;
    }

    public function getAccountBetTransactionByDate($params = [])
    {
        $query = 'SELECT account, SUM(turn_over) AS total_turn_over, SUM(bet) AS total_bet, SUM(return) AS total_return, SUM(win_loss) AS total_win_loss FROM player_transactions WHERE create_time >= :start AND create_time <= :end GROUP BY account';
        $params = array_filter($params);

        if (isset($params['start']) && isset($params['end'])) {
            $params['start'] = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $params['end'] = Carbon::parse($params['end'])->format('Y/m/d H:i:s +08:00');
        } else {
            $params['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $params['end'] = Carbon::now()->format('Y/m/d H:i:s +08:00');
        }

        return ClickHouse::select($query, $params)->rows();
    }

    public function getAccountBetTransactions($params = [])
    {
        return ClickHouse::select('SELECT * FROM wwj_account_bet_transactions')->rows();
    }

    public function getTotalTurnover($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::now()->format('Y/m/d 23:59:59 +08:00');
        }

        $baseQuery = "SELECT
                SUM(pt.bet) AS total_bet,
                COUNT(pt.sn) AS total_bet_count
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT('fw_', players.account)
            WHERE
                pt.create_time >= :start AND pt.create_time <= :end";

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        return ClickHouse::select($baseQuery, $queryParams)->rows()[0];
    }

    public function getPlayerTransactions($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::now()->format('Y/m/d 23:59:59 +08:00');
        }

        if (isset($params['account'])) {
            $queryParams['account'] = $params['account'];
        }

        // Get sort parameters
        $sortField = $params['sort'] ?? 'last_transaction';
        $sortDirection = $params['direction'] ?? 'DESC';

        // Define mapping for sort fields if needed
        $sortFieldMap = [
            'username' => 'players.username',
            'account' => 'pt.account',
            'total_initial_balance' => 'total_initial_balance',
            'total_bet' => 'total_bet',
            'total_final_balance' => 'total_final_balance',
            'total_win_loss' => 'total_win_loss',
            'last_transaction' => 'last_transaction'
        ];

        // Use mapped field name if available, otherwise use the provided field
        $dbSortField = $sortFieldMap[$sortField] ?? 'last_transaction';

        // Validate sort direction
        $sortDirection = strtolower($sortDirection) === 'asc' ? 'ASC' : 'DESC';

        $baseQuery = "SELECT
                players.username AS username,
                pt.account AS account,
                SUM(pt.initial_balance) AS total_initial_balance,
                SUM(pt.bet) AS total_bet,
                SUM(pt.final_balance) AS total_final_balance,
                SUM(pt.win_loss) AS total_win_loss,
                MAX(pt.create_time) AS last_transaction
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT('fw_', players.account)
            WHERE
                pt.create_time >= :start AND pt.create_time <= :end";

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        if (isset($params['account']) && ! empty($params['account'])) {
            $baseQuery .= ' AND pt.account LIKE :account';
            $queryParams['account'] = '%'.$params['account'].'%';
        }

        if (isset($params['username']) && ! empty($params['username'])) {
            $baseQuery .= ' AND players.username LIKE :username';
            $queryParams['username'] = '%'.$params['username'].'%';
        }

        $baseQuery .= ' GROUP BY pt.account, players.username';

        $countQuery = "SELECT COUNT() as count FROM ($baseQuery)";
        $totalCount = (int) ClickHouse::select($countQuery, $queryParams)->rows()[0]['count'];

        $currentPage = (int) ($params['page'] ?? 1);
        $perPage = (int) ($params['pageSize'] ?? 10);
        $offset = ($currentPage - 1) * $perPage;
        $lastPage = ceil($totalCount / $perPage);

        $baseQuery .= " ORDER BY $dbSortField $sortDirection LIMIT $perPage OFFSET $offset";
        $results = ClickHouse::select($baseQuery, $queryParams)->rows();

        $formattedResults = array_map(function ($row) {
            $row['last_transaction'] = self::dateFormat($row['last_transaction']);

            return $row;
        }, $results);

        $links = [];

        $links[] = [
            'url' => '?page=1',
            'label' => '1',
            'active' => $currentPage === 1,
        ];

        if ($lastPage > 1) {
            if ($currentPage > 3) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            for ($i = max(2, $currentPage - 1); $i <= min($lastPage - 1, $currentPage + 1); $i++) {
                if ($i > 1 && $i < $lastPage) {
                    $links[] = [
                        'url' => '?page='.$i,
                        'label' => (string) $i,
                        'active' => $currentPage === $i,
                    ];
                }
            }

            if ($currentPage < ($lastPage - 2)) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            if ($lastPage > 1) {
                $links[] = [
                    'url' => '?page='.$lastPage,
                    'label' => (string) $lastPage,
                    'active' => $currentPage === $lastPage,
                ];
            }
        }

        $totalBetQuery = "SELECT
                SUM(pt.bet) AS total_bet
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT('fw_', players.account)
            WHERE
                pt.create_time >= :start AND pt.create_time <= :end";

        // Handle store_id parameter for totals query
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $totalBetQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $totalBetQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $totalBetQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        if (isset($params['account']) && ! empty($params['account'])) {
            $totalBetQuery .= ' AND pt.account LIKE :account';
        }

        if (isset($params['username']) && ! empty($params['username'])) {
            $totalBetQuery .= ' AND players.username LIKE :username';
        }

        $totalBetAmount = ClickHouse::select($totalBetQuery, $queryParams)->rows()[0]['total_bet'] ?? 0;

        return [
            'data' => $formattedResults,
            'grand_total' => [
                'total_bet' => $totalBetAmount,
                'total_initial_balance' => array_sum(array_column($results, 'total_initial_balance')),
                'total_final_balance' => array_sum(array_column($results, 'total_final_balance')),
                'total_win_loss' => array_sum(array_column($results, 'total_win_loss')),
            ],
            'pagination' => [
                'current_page' => $currentPage,
                'first_page_url' => '?page=1',
                'from' => $offset + 1,
                'last_page' => $lastPage,
                'last_page_url' => '?page='.$lastPage,
                'links' => $links,
                'next_page_url' => $currentPage < $lastPage ? '?page='.($currentPage + 1) : null,
                'path' => '',
                'per_page' => $perPage,
                'prev_page_url' => $currentPage > 1 ? '?page='.($currentPage - 1) : null,
                'to' => $offset + count($formattedResults),
                'total' => $totalCount,
            ],
        ];
    }

    public function getPlayerDetailTransactions($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::now()->format('Y/m/d 23:59:59 +08:00');
        }

        if (isset($params['account'])) {
            $queryParams['account'] = $params['account'];
        }

        $baseQuery = 'SELECT
                pt.sn,
                pt.account,
                players.username AS username,
                pt.create_time,
                pt.initial_balance,
                pt.bet,
                pt.final_balance,
                pt.win_loss,
                pt.win_loss_status
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT(\'fw_\', players.account)
            WHERE
            pt.create_time >= {start:String} AND pt.create_time <= {end:String}';

        if (isset($params['account'])) {
            $baseQuery .= ' AND pt.account = {account:String}';
        }

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        $countQuery = "SELECT COUNT() as count FROM ($baseQuery)";
        $totalCount = (int) ClickHouse::select($countQuery, $queryParams)->rows()[0]['count'];

        $currentPage = (int) ($params['page'] ?? 1);
        $perPage = (int) ($params['pageSize'] ?? 10);
        $offset = ($currentPage - 1) * $perPage;
        $lastPage = ceil($totalCount / $perPage);

        // Handle sorting
        $sortField = $params['sort'] ?? 'pt.create_time';
        $sortDirection = $params['direction'] ?? 'desc';

        // Map frontend field names to database field names
        $sortFieldMap = [
            'create_time' => 'pt.create_time',
            'win_loss' => 'pt.win_loss',
            'initial_balance' => 'pt.initial_balance',
            'bet' => 'pt.bet',
            'final_balance' => 'pt.final_balance',
            'sn' => 'pt.sn',
            'account' => 'pt.account',
            'username' => 'players.username',
            'win_loss_status' => 'pt.win_loss_status'
        ];

        // Use mapped field name if available, otherwise use the provided field
        $dbSortField = $sortFieldMap[$sortField] ?? 'pt.create_time';

        // Validate sort direction
        $sortDirection = strtolower($sortDirection) === 'asc' ? 'ASC' : 'DESC';

        $baseQuery .= " ORDER BY $dbSortField $sortDirection LIMIT $perPage OFFSET $offset";

        $results = ClickHouse::select($baseQuery, $queryParams)->rows();

        $formattedResults = array_map(function ($row) {
            $row['create_time'] = self::dateFormat($row['create_time']);

            return $row;
        }, $results);

        $links = [];

        $links[] = [
            'url' => '?page=1',
            'label' => '1',
            'active' => $currentPage === 1,
        ];

        if ($lastPage > 1) {
            if ($currentPage > 3) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            for ($i = max(2, $currentPage - 1); $i <= min($lastPage - 1, $currentPage + 1); $i++) {
                if ($i > 1 && $i < $lastPage) {
                    $links[] = [
                        'url' => '?page='.$i,
                        'label' => (string) $i,
                        'active' => $currentPage === $i,
                    ];
                }
            }

            if ($currentPage < ($lastPage - 2)) {
                $links[] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            if ($lastPage > 1) {
                $links[] = [
                    'url' => '?page='.$lastPage,
                    'label' => (string) $lastPage,
                    'active' => $currentPage === $lastPage,
                ];
            }
        }

        return [
            'data' => $formattedResults,
            'pagination' => [
                'current_page' => $currentPage,
                'first_page_url' => '?page=1',
                'from' => $offset + 1,
                'last_page' => $lastPage,
                'last_page_url' => '?page='.$lastPage,
                'links' => $links,
                'next_page_url' => $currentPage < $lastPage ? '?page='.($currentPage + 1) : null,
                'path' => '',
                'per_page' => $perPage,
                'prev_page_url' => $currentPage > 1 ? '?page='.($currentPage - 1) : null,
                'to' => $offset + count($formattedResults),
                'total' => $totalCount,
            ],
        ];
    }

    public function getTotalWinLoss($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::now()->format('Y/m/d 23:59:59 +08:00');
        }

        $baseQuery = "SELECT
                SUM(CASE WHEN pt.win_loss > 0 THEN pt.win_loss ELSE 0 END) AS total_wins,
                SUM(CASE WHEN pt.win_loss < 0 THEN pt.win_loss ELSE 0 END) AS total_losses
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT('fw_', players.account)
            WHERE
                pt.create_time >= :start AND pt.create_time <= :end";

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        return ClickHouse::select($baseQuery, $queryParams)->rows()[0];
    }

    /**
     * Get turnover and win/loss statistics for multiple store IDs at once
     */
    public function getBulkTurnoverAndWinLoss($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::now()->format('Y/m/d 23:59:59 +08:00');
        }

        $baseQuery = "SELECT
                players.store_id AS store_id,
                SUM(pt.bet) AS total_bet,
                COUNT(pt.sn) AS total_bet_count,
                SUM(CASE WHEN pt.win_loss > 0 THEN pt.win_loss ELSE 0 END) AS total_wins,
                SUM(CASE WHEN pt.win_loss < 0 THEN pt.win_loss ELSE 0 END) AS total_losses
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT('fw_', players.account)
            WHERE
                pt.create_time >= :start AND pt.create_time <= :end";

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        $baseQuery .= " GROUP BY players.store_id";

        $result = ClickHouse::select($baseQuery, $queryParams)->rows();

        // Convert to associative array with store_id as key for easier lookup
        $formattedResult = [];
        foreach ($result as $row) {
            $formattedResult[$row['store_id']] = [
                'total_bet' => $row['total_bet'] ?? 0,
                'total_bet_count' => $row['total_bet_count'] ?? 0,
                'total_wins' => $row['total_wins'] ?? 0,
                'total_losses' => $row['total_losses'] ?? 0,
            ];
        }

        return $formattedResult;
    }

    /**
     * Get transaction data for a list of specific player accounts
     */
    public function getPlayersTransactions($params = [])
    {
        $queryParams = [
            'start' => $params['start'] ?? '',
            'end' => $params['end'] ?? '',
        ];

        if (isset($params['start']) && isset($params['end'])) {
            $queryParams['start'] = Carbon::parse($params['start'])->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::parse($params['end'])->format('Y/m/d 23:59:59 +08:00');
        } else {
            $queryParams['start'] = Carbon::now()->subDays(1)->format('Y/m/d H:i:s +08:00');
            $queryParams['end'] = Carbon::now()->format('Y/m/d 23:59:59 +08:00');
        }

        $playerAccounts = $params['accounts'] ?? [];
        $accountCondition = '';

        if (! empty($playerAccounts)) {
            $escapedAccounts = array_map(function ($acc) {
                return "'".str_replace("'", "\'", $acc)."'";
            }, $playerAccounts);
            $accountsList = implode(',', $escapedAccounts);
            $accountCondition = " AND players.username IN ($accountsList)";
        }

        $baseQuery = "SELECT
                players.username AS username,
                SUM(pt.bet) AS total_bet
            FROM
                player_transactions pt
            JOIN players
                ON pt.account = CONCAT('fw_', players.account)
            WHERE
                pt.create_time >= :start AND pt.create_time <= :end";

        // Handle store_id parameter
        if (isset($params['store_id']) && !empty($params['store_id']) && $params['store_id'] !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($params['store_id'], ',') !== false) {
                $storeIdArray = explode(',', $params['store_id']);
                $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $storeIdArray));
                $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
            } else {
                // Single store ID
                $queryParams['store_id'] = (string) $params['store_id'];
                $baseQuery .= ' AND players.store_id = :store_id';
            }
        }
        // Handle store_ids array parameter (used when store_id is 'all')
        elseif (isset($params['store_ids']) && is_array($params['store_ids']) && !empty($params['store_ids'])) {
            $storeIdsStr = implode(',', array_map(function($id) { return "'".trim($id)."'"; }, $params['store_ids']));
            $baseQuery .= " AND players.store_id IN ($storeIdsStr)";
        }

        $baseQuery .= $accountCondition . " GROUP BY players.username";

        $results = ClickHouse::select($baseQuery, $queryParams)->rows();

        // Convert to associative array with username as key
        $formattedResults = [];
        foreach ($results as $row) {
            $formattedResults[$row['username']] = [
                'total_bet' => (float) $row['total_bet'],
            ];
        }

        return $formattedResults;
    }
}
