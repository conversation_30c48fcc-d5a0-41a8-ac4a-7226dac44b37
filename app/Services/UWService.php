<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class UWService
{
    public $baseUrl;

    public function __construct()
    {
        $this->baseUrl = env('UW_API_URL');
    }

    public function getStatistics($storeId, $startDate, $endDate)
    {
        $url = $this->baseUrl.'/reports/statistics';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeId,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

        return $response;
    }

    public function getDepositReport($storeId, $startDate, $endDate, $currentPage, $perPage, $currentUrl)
    {
        $url = $this->baseUrl.'/reports/deposit';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'current_page' => $currentPage,
                'per_page' => $perPage,
                'current_url' => $currentUrl,
            ]);

        return $response;
    }

    public function getWithdrawalReport($storeId, $startDate, $endDate, $currentPage, $perPage, $currentUrl)
    {
        $url = $this->baseUrl.'/reports/withdrawal';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'current_page' => $currentPage,
                'per_page' => $perPage,
                'current_url' => $currentUrl,
            ]);

        return $response;
    }

    public function getStores($storeId)
    {
        $url = $this->baseUrl.'/stores/all';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeId,
            ]);

        return $response;
    }

    public function getUserList($storeId, $lastUserId)
    {
        $url = $this->baseUrl.'/users/all';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_id' => $storeId,
                'last_user_id' => $lastUserId,
            ]);

        return $response;
    }

    public function getBulkStatistics($storeIds, $startDate, $endDate)
    {
        $url = $this->baseUrl.'/reports/bulk-statistics';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeIds,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

        return $response;
    }

    public function getTopPlayers($storeIds, $startDate, $endDate, $sortBy = 'deposits', $limit = 10)
    {
        $url = $this->baseUrl.'/reports/top-players';

        $reponse = Http::withHeaders([
            'Accept' => 'application/json',
        ])->get($url, [
            'store_ids' => $storeIds,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'sort_by' => $sortBy,
            'limit' => $limit,
        ]);

        return $reponse;
    }

    public function getUserReferrals($storeIds, $startDate, $endDate, $currentPage, $perPage, $currentUrl)
    {
        $url = $this->baseUrl.'/reports/user-referrals';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeIds,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'current_page' => $currentPage,
                'per_page' => $perPage,
                'current_url' => $currentUrl,
            ]);

        return $response;
    }

    public function getActiveUserStatistics($storeIds, $startDate, $endDate, $timeAggregation = 'hourly')
    {
        $url = $this->baseUrl.'/reports/active-user-statistics';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeIds,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'time_aggregation' => $timeAggregation,
            ]);

        return $response;
    }

    public function getEventStatistics($storeIds, $startDate, $endDate)
    {
        $url = $this->baseUrl.'/reports/event-statistics';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeIds,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

        return $response;
    }

    public function getNewMembers($storeIds, $startDate, $endDate)
    {
        $url = $this->baseUrl.'/reports/new-members';

        $response = Http::withHeaders([
            'Accept' => 'application/json',
        ])
            ->get($url, [
                'store_ids' => $storeIds,
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);

        return $response;
    }
}
