<?php

namespace App\Http\Controllers;

use App\Exports\AllPlayerLogsExport;
use App\Exports\GscDetailLogsExport;
use App\Exports\GscLogsExport;
use App\Services\ClickHouse\GameLogService;
use App\Services\ClickHouse\GSCService;
use App\Services\ClickHouse\UserService;
use App\Services\ClickHouse\WWJService;
use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class GameLogController extends Controller
{
    public function __construct(
        protected UWService $uwService,
        protected WWJService $wwjService,
        protected UserService $userService,
        protected GSCService $gscService,
        protected GameLogService $gameLogService
    ) {}

    public function playerLogs(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $perPage = (int) (request()->input('perPage', 10));
        $currentPage = (int) (request()->input('page', 1));
        $account = request()->input('account');
        $username = request()->input('username');
        $storeId = request()->store_id;

        // Add sorting parameters
        $sortField = request()->input('sort', 'last_transaction');
        $sortDirection = request()->input('direction', 'desc');

        // Get stores for the merchant
        $stores = [];
        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
            if (! $storeResponse->failed()) {
                $storesData = json_decode($storeResponse, true);
                $stores = $storesData['data'] ?? [];

                if (! $storeId && ! empty($stores)) {
                    // Default to 'all' stores
                    $storeId = 'all';
                }
            }
        }

        // If no store_id is set, default to 'all'
        if (empty($storeId)) {
            $storeId = 'all';
        }

        if ($storeId && $storeId !== 'all') {
            if (strpos($storeId, ',') !== false) {
                $storeIdArray = explode(',', $storeId);
                foreach ($storeIdArray as $singleStoreId) {
                    $lastPlayerId = $this->userService->getLastPlayerId($singleStoreId);
                    $response = $this->uwService->getUserList($singleStoreId, $lastPlayerId);

                    if ($response->failed()) {
                        throw new Exception('Failed to get user list.');
                    }

                    $response = json_decode($response, true);
                    $data = $response['data'];

                    if (count($data) > 0) {
                        $this->userService->insertPlayer($data);
                    }
                }
            } else {
                $lastPlayerId = $this->userService->getLastPlayerId($storeId);
                $response = $this->uwService->getUserList($storeId, $lastPlayerId);

                if ($response->failed()) {
                    throw new Exception('Failed to get user list.');
                }

                $response = json_decode($response, true);
                $data = $response['data'];

                if (count($data) > 0) {
                    $this->userService->insertPlayer($data);
                }
            }
        }

        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => $currentPage,
            'pageSize' => $perPage,
            'account' => $account,
            'username' => $username,
            'store_id' => $storeId,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ];

        // If store_id is 'all', pass the user's merchant store IDs
        if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
            $params['store_ids'] = request()->user()->merchant->store_ids;
        }

        $response = $this->wwjService->getPlayerTransactions($params);

        // Build query string for pagination URLs
        $queryParams = http_build_query([
            'store_id' => $storeId,
            'account' => $account,
            'username' => $username,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'perPage' => $perPage,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ]);

        $pagination = $response['pagination'];
        $baseUrl = url()->current();

        // Update pagination URLs with current filters
        foreach ($pagination['links'] as &$link) {
            if ($link['url'] !== null) {
                $link['url'] = $baseUrl.'?'.$queryParams.'&'.substr($link['url'], 1);
            }
        }

        $pagination['path'] = $baseUrl;
        $pagination['first_page_url'] = $baseUrl.'?'.$queryParams.'&page=1';
        $pagination['last_page_url'] = $baseUrl.'?'.$queryParams.'&page='.$pagination['last_page'];

        if ($pagination['next_page_url']) {
            $pagination['next_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage + 1);
        }

        if ($pagination['prev_page_url']) {
            $pagination['prev_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage - 1);
        }

        return Inertia::render('GameLogs/PlayerLog', [
            'filters' => [
                'search' => request()->search,
                'perPage' => (string) $perPage,
                'account' => $account,
                'username' => $username,
                'store_id' => $storeId,
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $response['data'],
                'grand_total' => $response['grand_total'],
            ],
            'page' => $pagination,
            'stores' => $stores,
        ]);
    }

    public function playerDetailLogs(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        // Ensure perPage is properly cast to integer
        $perPage = (int) (request()->input('perPage', 10));
        $currentPage = (int) (request()->input('page', 1));
        $account = request()->input('account');

        // Add sorting parameters
        $sortField = request()->input('sort', 'create_time');
        $sortDirection = request()->input('direction', 'desc');

        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => $currentPage,
            'pageSize' => $perPage,
            'account' => $account,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ];

        // Get the store_id from the request if available
        $storeId = request()->input('store_id', 'all');
        if ($storeId) {
            $params['store_id'] = $storeId;

            // If store_id is 'all', pass the user's merchant store IDs
            if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
                $params['store_ids'] = request()->user()->merchant->store_ids;
            }
        }

        $response = $this->wwjService->getPlayerDetailTransactions($params);

        // Build query string for pagination URLs
        $queryParams = http_build_query([
            'account' => $account,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'perPage' => $perPage,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ]);

        // Create pagination links
        $pagination = [
            'current_page' => $response['pagination']['current_page'],
            'first_page_url' => url()->current().'?'.$queryParams.'&page=1',
            'from' => $response['pagination']['from'],
            'last_page' => $response['pagination']['last_page'],
            'last_page_url' => url()->current().'?'.$queryParams.'&page='.$response['pagination']['last_page'],
            'links' => [],
            'next_page_url' => $response['pagination']['next_page_url'] ? url()->current().'?'.$queryParams.'&page='.($currentPage + 1) : null,
            'path' => url()->current(),
            'per_page' => $perPage,
            'prev_page_url' => $response['pagination']['prev_page_url'] ? url()->current().'?'.$queryParams.'&page='.($currentPage - 1) : null,
            'to' => $response['pagination']['to'],
            'total' => $response['pagination']['total'],
        ];

        // Generate pagination links
        foreach ($response['pagination']['links'] as $link) {
            if ($link['url'] === null) {
                $pagination['links'][] = [
                    'url' => null,
                    'label' => $link['label'],
                    'active' => $link['active'],
                ];
            } else {
                $pagination['links'][] = [
                    'url' => url()->current().'?'.$queryParams.'&'.substr($link['url'], strpos($link['url'], '?') + 1),
                    'label' => $link['label'],
                    'active' => $link['active'],
                ];
            }
        }

        return Inertia::render('GameLogs/PlayerDetailLog', [
            'filters' => [
                'search' => request()->search,
                'perPage' => (string) $perPage,
                'account' => $account,
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $response['data'],
            ],
            'page' => $pagination,
        ]);
    }

    public function exportPlayerLogs()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $account = request()->input('account');
        $username = request()->input('username');
        $storeId = request()->input('store_id');

        // For the filename, check if it's multiple stores
        $storeInfo = '';
        if ($storeId && $storeId !== 'all') {
            if (strpos($storeId, ',') !== false) {
                $storeInfo = '-multiple-stores';
            } else {
                $storeResponse = $this->uwService->getStores([$storeId]);
                if (!$storeResponse->failed()) {
                    $storeData = json_decode($storeResponse, true);
                    if (!empty($storeData['data'])) {
                        $storeName = $storeData['data'][0]['name'] ?? 'single-store';
                        $storeInfo = '-' . str_replace(' ', '-', $storeName);
                    }
                }
            }
        }

        // Add sorting parameters
        $sortField = request()->input('sort', 'last_transaction');
        $sortDirection = request()->input('direction', 'desc');

        // Get all records without pagination for export
        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => 1,
            'pageSize' => 10000, // Large number to get all records
            'account' => $account,
            'username' => $username,
            'store_id' => $storeId,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ];

        // If store_id is 'all', pass the user's merchant store IDs
        if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
            $params['store_ids'] = request()->user()->merchant->store_ids;
        }

        $response = $this->wwjService->getPlayerTransactions($params);

        $data = $response['data'];
        $grandTotal = $response['grand_total'];

        // Generate filename
        $filename = 'wwj_player_logs'.$storeInfo.'_'.date('Y-m-d_H-i-s').'.xlsx';

        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\PlayerLogsExport($data, $grandTotal),
            $filename
        );
    }

    public function exportPlayerDetailLogs()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $account = request()->input('account');

        $wwjService = new WWJService;

        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => 1,
            'pageSize' => 10000,
            'account' => $account,
        ];

        // Get the store_id from the request if available
        $storeId = request()->input('store_id', 'all');
        if ($storeId) {
            $params['store_id'] = $storeId;

            // If store_id is 'all', pass the user's merchant store IDs
            if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
                $params['store_ids'] = request()->user()->merchant->store_ids;
            }
        }

        $response = $wwjService->getPlayerDetailTransactions($params);

        $data = $response['data'];

        $accountInfo = $account ? '_'.$account : '';
        $filename = 'wwj_player_detail_logs'.$accountInfo.'_'.date('Y-m-d_H-i-s').'.xlsx';

        return \Maatwebsite\Excel\Facades\Excel::download(
            new \App\Exports\PlayerDetailLogsExport($data),
            $filename
        );
    }

    public function gscLogs(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $perPage = (int) (request()->input('perPage', 10));
        $currentPage = (int) (request()->input('page', 1));
        $account = request()->input('account');
        $username = request()->input('username');
        $storeId = request()->store_id;

        // Add sorting parameters
        $sortField = request()->input('sort', 'last_match_time');
        $sortDirection = request()->input('direction', 'desc');

        // Get stores for the merchant
        $stores = [];
        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
            if (! $storeResponse->failed()) {
                $storesData = json_decode($storeResponse, true);
                $stores = $storesData['data'] ?? [];

                if (! $storeId && ! empty($stores)) {
                    // Default to 'all' stores
                    $storeId = 'all';
                }
            }
        }

        // Sync player data before fetching GSC transactions
        if ($storeId && $storeId !== 'all') {
            if (strpos($storeId, ',') !== false) {
                $storeIdArray = explode(',', $storeId);
                foreach ($storeIdArray as $singleStoreId) {
                    $lastPlayerId = $this->userService->getLastPlayerId($singleStoreId);
                    $response = $this->uwService->getUserList($singleStoreId, $lastPlayerId);

                    if ($response->failed()) {
                        throw new Exception('Failed to get user list.');
                    }

                    $response = json_decode($response, true);
                    $data = $response['data'];

                    if (count($data) > 0) {
                        $this->userService->insertPlayer($data);
                    }
                }
            } else {
                $lastPlayerId = $this->userService->getLastPlayerId($storeId);
                $response = $this->uwService->getUserList($storeId, $lastPlayerId);

                if ($response->failed()) {
                    throw new Exception('Failed to get user list.');
                }

                $response = json_decode($response, true);
                $data = $response['data'];

                if (count($data) > 0) {
                    $this->userService->insertPlayer($data);
                }
            }
        }

        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => $currentPage,
            'pageSize' => $perPage,
            'account' => $account,
            'username' => $username,
            'store_id' => $storeId,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ];

        // If store_id is 'all', pass the user's merchant store IDs
        if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
            $params['store_ids'] = request()->user()->merchant->store_ids;
        }

        $response = $this->gscService->getGscTransactions($params);

        // Build query string for pagination URLs
        $queryParams = http_build_query([
            'store_id' => $storeId,
            'account' => $account,
            'username' => $username,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'perPage' => $perPage,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ]);

        $pagination = $response['pagination'];
        $baseUrl = url()->current();

        foreach ($pagination['links'] as &$link) {
            if ($link['url'] !== null) {
                $link['url'] = $baseUrl.'?'.$queryParams.'&'.substr($link['url'], 1);
            }
        }

        $pagination['path'] = $baseUrl;
        $pagination['first_page_url'] = $baseUrl.'?'.$queryParams.'&page=1';
        $pagination['last_page_url'] = $baseUrl.'?'.$queryParams.'&page='.$pagination['last_page'];

        if ($pagination['next_page_url']) {
            $pagination['next_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage + 1);
        }

        if ($pagination['prev_page_url']) {
            $pagination['prev_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage - 1);
        }

        return Inertia::render('GameLogs/GscLog', [
            'filters' => [
                'search' => request()->search,
                'perPage' => (string) $perPage,
                'account' => $account,
                'username' => $username,
                'store_id' => $storeId,
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $response['data'],
                'grand_total' => $response['grand_total'],
            ],
            'page' => $pagination,
            'stores' => $stores,
        ]);
    }

    public function gscDetailLogs(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $perPage = (int) (request()->input('perPage', 10));
        $currentPage = (int) (request()->input('page', 1));
        $account = request()->input('account');
        $sortField = request()->input('sort', '');
        $sortDirection = request()->input('direction', '');

        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => $currentPage,
            'pageSize' => $perPage,
            'account' => $account,
        ];

        if ($sortField && $sortDirection) {
            $params['sort'] = $sortField;
            $params['direction'] = $sortDirection;
        }

        // Get the store_id from the request if available
        $storeId = request()->input('store_id', 'all');
        if ($storeId) {
            $params['store_id'] = $storeId;

            // If store_id is 'all', pass the user's merchant store IDs
            if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
                $params['store_ids'] = request()->user()->merchant->store_ids;
            }
        }

        $response = $this->gscService->getGscDetailTransactions($params);

        $queryParams = http_build_query([
            'account' => $account,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'perPage' => $perPage,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ]);

        $pagination = $response['pagination'];
        $baseUrl = url()->current();

        foreach ($pagination['links'] as &$link) {
            if ($link['url'] !== null) {
                $link['url'] = $baseUrl.'?'.$queryParams.'&'.substr($link['url'], 1);
            }
        }

        $pagination['path'] = $baseUrl;
        $pagination['first_page_url'] = $baseUrl.'?'.$queryParams.'&page=1';
        $pagination['last_page_url'] = $baseUrl.'?'.$queryParams.'&page='.$pagination['last_page'];

        if ($pagination['next_page_url']) {
            $pagination['next_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage + 1);
        }

        if ($pagination['prev_page_url']) {
            $pagination['prev_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage - 1);
        }

        return Inertia::render('GameLogs/GscDetailLog', [
            'filters' => [
                'search' => request()->search,
                'perPage' => (string) $perPage,
                'account' => $account,
                'sort' => $sortField,
                'direction' => $sortDirection,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $response['data'],
            ],
            'page' => $pagination,
        ]);
    }

    public function exportGscLogs()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $account = request()->input('account');
        $username = request()->input('username');
        $storeId = request()->input('store_id');
        // If store_id is empty, set it to 'all' for consistency
        if (empty($storeId)) {
            $storeId = 'all';
        }

        $storeInfo = '';
        if ($storeId && $storeId !== 'all') {
            if (strpos($storeId, ',') !== false) {
                $storeInfo = '-multiple-stores';
            } else {
                $storeResponse = $this->uwService->getStores([$storeId]);
                if (!$storeResponse->failed()) {
                    $storeData = json_decode($storeResponse, true);
                    if (!empty($storeData['data'])) {
                        $storeName = $storeData['data'][0]['name'] ?? 'single-store';
                        $storeInfo = '-' . str_replace(' ', '-', $storeName);
                    }
                }
            }
        }

        // Add sorting parameters
        $sortField = request()->input('sort', 'last_match_time');
        $sortDirection = request()->input('direction', 'desc');

        // Get all records without pagination for export
        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => 1,
            'pageSize' => 10000, // Large number to get all records
            'account' => $account,
            'username' => $username,
            'store_id' => $storeId,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ];

        // If store_id is 'all', pass the user's merchant store IDs
        if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
            $params['store_ids'] = request()->user()->merchant->store_ids;
        }

        $response = $this->gscService->getGscTransactions($params);

        return Excel::download(
            new GscLogsExport($response['data'], $response['grand_total']),
            'gsc_player_logs'.$storeInfo.'_'.date('Y-m-d_H-i-s').'.xlsx'
        );
    }

    public function exportGscDetailLogs()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $account = request()->input('account');
        // Add sorting parameters
        $sortField = request()->input('sort', 'win_loss');
        $sortDirection = request()->input('direction', 'desc');

        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => 1,
            'pageSize' => 10000,
            'account' => $account,
            'sort' => $sortField,
            'direction' => $sortDirection,
        ];

        // Get the store_id from the request if available
        $storeId = request()->input('store_id', 'all');
        if ($storeId) {
            $params['store_id'] = $storeId;

            // If store_id is 'all', pass the user's merchant store IDs
            if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
                $params['store_ids'] = request()->user()->merchant->store_ids;
            }
        }

        $response = $this->gscService->getGscDetailTransactions($params);

        $filename = 'gsc_player_detail_logs'.($account ? '_'.$account : '').'_'.date('Y-m-d_H-i-s').'.xlsx';

        return Excel::download(
            new GscDetailLogsExport($response['data']),
            $filename
        );
    }

    public function allPlayerLogs(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $perPage = (int) (request()->input('perPage', 10));
        $currentPage = (int) (request()->input('page', 1));
        $account = request()->input('account');
        $username = request()->input('username');
        $storeId = request()->store_id;
        $type = request()->input('type', 'all');

        // Get stores for the merchant
        $stores = [];
        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
            if (! $storeResponse->failed()) {
                $storesData = json_decode($storeResponse, true);
                $stores = $storesData['data'] ?? [];

                if (! $storeId && ! empty($stores)) {
                    // Default to 'all' stores
                    $storeId = 'all';
                }
            }
        }

        if ($storeId && $storeId !== 'all') {
            if (strpos($storeId, ',') !== false) {
                $storeIdArray = explode(',', $storeId);
                foreach ($storeIdArray as $singleStoreId) {
                    $lastPlayerId = $this->userService->getLastPlayerId($singleStoreId);
                    $response = $this->uwService->getUserList($singleStoreId, $lastPlayerId);

                    if ($response->failed()) {
                        throw new Exception('Failed to get user list.');
                    }

                    $response = json_decode($response, true);
                    $data = $response['data'];

                    if (count($data) > 0) {
                        $this->userService->insertPlayer($data);
                    }
                }
            } else {
                $lastPlayerId = $this->userService->getLastPlayerId($storeId);
                $response = $this->uwService->getUserList($storeId, $lastPlayerId);

                if ($response->failed()) {
                    throw new Exception('Failed to get user list.');
                }

                $response = json_decode($response, true);
                $data = $response['data'];

                if (count($data) > 0) {
                    $this->userService->insertPlayer($data);
                }
            }
        }

        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => $currentPage,
            'pageSize' => $perPage,
            'account' => $account,
            'username' => $username,
            'store_id' => $storeId,
            'type' => $type,
        ];

        if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
            $params['store_ids'] = request()->user()->merchant->store_ids;
        }

        $response = $this->gameLogService->getAllPlayerLogs($params);

        // Build query string for pagination URLs
        $queryParams = http_build_query([
            'store_id' => $storeId,
            'account' => $account,
            'username' => $username,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'perPage' => $perPage,
        ]);

        $pagination = $response['pagination'];
        $baseUrl = url()->current();

        // Update pagination URLs with current filters
        foreach ($pagination['links'] as &$link) {
            if ($link['url'] !== null) {
                $link['url'] = $baseUrl.'?'.$queryParams.'&'.substr($link['url'], 1);
            }
        }

        $pagination['path'] = $baseUrl;
        $pagination['first_page_url'] = $baseUrl.'?'.$queryParams.'&page=1';
        $pagination['last_page_url'] = $baseUrl.'?'.$queryParams.'&page='.$pagination['last_page'];

        if ($pagination['next_page_url']) {
            $pagination['next_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage + 1);
        }

        if ($pagination['prev_page_url']) {
            $pagination['prev_page_url'] = $baseUrl.'?'.$queryParams.'&page='.($currentPage - 1);
        }

        return Inertia::render('GameLogs/AllPlayerLog', [
            'filters' => [
                'search' => request()->search,
                'perPage' => (string) $perPage,
                'account' => $account,
                'username' => $username,
                'store_id' => $storeId,
                'type' => $type,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $response['data'],
                'grand_total' => $response['grand_total'],
            ],
            'page' => $pagination,
            'stores' => $stores,
        ]);
    }

    public function exportAllPlayerLogs()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y/m/d H:i:s')
            : Carbon::now()->startOfDay()->format('Y/m/d H:i:s');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y/m/d H:i:59')
            : Carbon::now()->endOfDay()->format('Y/m/d H:i:59');

        $account = request()->input('account');
        $username = request()->input('username');
        $storeId = request()->input('store_id');

        if (empty($storeId)) {
            $storeId = 'all';
        }

        $storeInfo = '';
        if ($storeId && $storeId !== 'all') {
            if (strpos($storeId, ',') !== false) {
                $storeInfo = '-multiple-stores';
            } else {
                $storeResponse = $this->uwService->getStores([$storeId]);
                if (!$storeResponse->failed()) {
                    $storeData = json_decode($storeResponse, true);
                    if (!empty($storeData['data'])) {
                        $storeName = $storeData['data'][0]['name'] ?? 'single-store';
                        $storeInfo = '-' . str_replace(' ', '-', $storeName);
                    }
                }
            }
        }
        $type = request()->input('type', 'all');

        // Get all records without pagination for export
        $params = [
            'start' => $startDate,
            'end' => $endDate,
            'page' => 1,
            'pageSize' => 10000,
            'account' => $account,
            'username' => $username,
            'store_id' => $storeId,
            'type' => $type,
        ];

        if ($storeId === 'all' && request()->user()->merchant?->store_ids) {
            $params['store_ids'] = request()->user()->merchant->store_ids;
        }

        $response = $this->gameLogService->getAllPlayerLogs($params);

        $filename = 'all_player_logs'.$storeInfo.'_'.date('Y-m-d_H-i-s').'.xlsx';

        return Excel::download(
            new AllPlayerLogsExport($response['data'], $response['grand_total']),
            $filename
        );
    }
}
