<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\MerchantPlayer;
use App\Services\ClickHouse\WWJService as ClickHouseWWJService;
use App\Services\JKService;
use Carbon\Carbon;
use Illuminate\Http\Request;

/**
 * @tags
 */
class MerchantController extends Controller
{
    public function __construct(public $jkService = new JKService) {}

    /**
     * Login Game
     *
     * Login User to the game with account and deposit amount, and get the Login Link. </br></br>
     * Set `deposit` to `0` or `null` to get the Login Link only without overwriting the current Game Balance.</br></br>
     * Account will be created automatically when the User does not exist. </br>
     * > The User Game Balance is All In and All Out and flow in single direction. Game Balance will be overwritten everytime a new deposit happen. To prevent the Game Balance from being overwritten, call the `api/v1/withdraw` endpoint first to withdraw from the game.
     */
    public function getLoginUrl(Request $request)
    {
        $this->validate($request, [
            /**
             * @example uwg_0001
             */
            'account' => 'required|string|min:8|max:20',
            /**
             * @example 0
             */
            'deposit' => 'nullable|numeric|min:0',
            /**
             * @example buaya
             */
            'game_code' => 'required|string',
        ]);

        if ($request->game_code != 'buaya') {
            return response()->json(
                status: 400,
                data: [
                    'message' => 'Invalid game code',
                ]
            );
        }

        // TODO: Hardcoded, fix into dynamic in future.
        $user = $request->user();
        $agentId = $user->merchant->merchantProvider->provider_code;
        $merchantCode = $user->merchant?->code;

        // Check if merchant player already exists for the same merchant code before creating a new one.
        $merchantPlayer = MerchantPlayer::where('account', $request->account)->first();
        if ($merchantPlayer && $merchantPlayer->merchant_code != $merchantCode) {
            return response()->json(
                status: 500,
                data: [
                    'message' => 'Account already exists, please change the account name.',
                ]
            );
        } else {
            $merchantPlayer = MerchantPlayer::firstOrCreate([
                'account' => $request->account,
                'merchant_code' => $user->merchant?->code,
            ]);
        }

        (new ClickHouseWWJService)->insertMerchantPlayer($request->account, $user->merchant?->code);

        $cashIn = 0;
        $useDbCash = true;
        if ($request->deposit && $request->deposit > 0) {
            $cashIn = $request->deposit;
            $useDbCash = false;
        }

        $response = $this->jkService->getLoginUrl($request->account, $cashIn, $agentId, $useDbCash);

        if ($response['status']) {
            return response()->json(
                status: 200,
                data: [
                    'message' => 'Login success.',
                    'deposit_amount' => $cashIn,
                    'url' => $response['data'],
                ],
            );
        } else {
            return response()->json(
                status: 500,
                data: [
                    'message' => 'Something went wrong! Please contact the administrator.',
                ]
            );
        }
    }

    /**
     * Logout Game
     *
     * Logout User from the game.
     * > Must log out User before request the new Login Link at `api/v1/logingame`, or else the new Login Link would not work.
     */
    public function logout(Request $request)
    {
        $this->validate($request, [
            /**
             * @example uwg_0001
             */
            'account' => 'required|string|min:8|max:20',
            /**
             * @example buaya
             */
            'game_code' => 'required|string',
        ]);

        if ($request->game_code != 'buaya') {
            return response()->json(
                status: 400,
                data: [
                    'message' => 'Invalid game code',
                ]
            );
        }

        // TODO: Hardcoded, fix into dynamic in future.
        $agentId = $request->user()->merchant->merchantProvider->provider_code;
        $response = $this->jkService->logout($request->account, $agentId);

        if ($response['status']) {
            return response()->json(
                status: 200,
                data: [
                    'message' => 'Logout success.',
                ],
            );
        } elseif (! $response['status']) {
            return response()->json(
                status: 400,
                data: [
                    'message' => $response['data'],
                ],
            );
        } else {
            return response()->json(
                status: 500,
                data: [
                    'message' => 'Something went wrong! Please contact the administrator.',
                ]
            );
        }
    }

    /**
     * Check Balance
     *
     * Check User Game Balance
     */
    public function checkGameBalance(Request $request)
    {
        $this->validate($request, [
            /**
             * @example uwg_0001
             */
            'account' => 'required|string|min:8|max:20',
            /**
             * @example buaya
             */
            'game_code' => 'required|string',
        ]);

        if ($request->game_code != 'buaya') {
            return response()->json(
                status: 400,
                data: [
                    'message' => 'Invalid game code',
                ]
            );
        }

        // TODO: Hardcoded, fix into dynamic in future.
        $agentId = $request->user()->merchant->merchantProvider->provider_code;
        $response = $this->jkService->withdraw($request->account, $agentId);

        if ($response['Result'] == 1) {
            return response()->json(
                status: 200,
                data: [
                    'message' => 'Check balance success.',
                    'amount' => $response['Data']['AllCashIn'],
                    'withdraw_key' => $response['Data']['RefundKey'],
                ],
            );
        } elseif ($response['Result'] == 2) {
            return response()->json(
                status: 200,
                data: [
                    'message' => 'The account balance is empty.',
                ],
            );
        } else {
            return response()->json(
                status: 500,
                data: [
                    'message' => 'Something wrong, please contact administrator.',
                ],
            );
        }
    }

    /**
     * Withdraw
     *
     * Withdraw User Game Balance
     * > Get the `withdrawKey` via the `api/v1/checkBalance` endpoint. User will get logged out once the withdraw is confirmed.
     */
    public function withdraw(Request $request)
    {
        $this->validate($request, [
            /**
             * @example uwg_0001
             */
            'account' => 'required|string|min:8|max:20',
            'withdraw_key' => 'required|string',
            /**
             * @example buaya
             */
            'game_code' => 'required|string',
        ]);

        if ($request->game_code != 'buaya') {
            return response()->json(
                status: 400,
                data: [
                    'message' => 'Invalid game code',
                ]
            );
        }

        // TODO: Hardcoded, fix into dynamic in future.
        $agentId = $request->user()->merchant->merchantProvider->provider_code;

        $response = $this->jkService->refundCoin($request->account, $request->withdraw_key, $agentId);

        if ($response['Result'] == 1) {
            return response()->json(
                status: 200,
                data: [
                    'message' => 'Withdrawal done.',
                ],
            );
        } elseif ($response['Result'] == 2 && $response['State'] == 'RefundKey Invalid') {
            return response()->json(
                status: 400,
                data: [
                    'message' => 'Withdraw key invalid.',
                ],
            );
        } elseif ($response['Result'] == 2 && $response['State'] == 'Time out or no account') {
            return response()->json(
                status: 400,
                data: [
                    'message' => 'Account not found.',
                ],
            );
        } else {
            return response()->json(
                status: 500,
                data: [
                    'message' => 'Something went wrong! Please contact the administrator.',
                ]
            );
        }
    }

    /**
     * Get Bet Transactions
     *
     * Get All Bet Transactions in between the specified `startTime` and `endTime`.
     * > Specify the `account` to filter by only specific account.
     */
    public function getBetTransactions(Request $request)
    {
        $this->validate($request, [
            /**
             * @example uwg_0001
             */
            'account' => 'nullable|string|min:8|max:20',
            /**
             * @example 2025-01-01T00:00:00+08:00
             */
            'startTime' => 'required|date',
            /**
             * @example 2025-01-01T23:59:59+08:00
             */
            'endTime' => 'required|date',
            /**
             * @example buaya
             */
            'game_code' => 'required|string',
        ]);

        if ($request->game_code != 'buaya') {
            return response()->json(
                status: 400,
                data: [
                    'message' => 'Invalid game code',
                ]
            );
        }

        // TODO: Hardcoded, fix into dynamic in future.
        $agentId = $request->user()->merchant->merchantProvider->provider_code;
        // $agentId = "funwallet_01";

        $startTime = Carbon::parse($request->startTime)->utc()->format('Y-m-d\TH:i:s.00\Z');
        $endTime = Carbon::parse($request->endTime)->utc()->format('Y-m-d\TH:i:s.00\Z');

        // Get List of Merchant Accounts
        // TODO: To use Clickhouse for matching.
        $user = $request->user();
        $accountIds = collect(MerchantPlayer::where('merchant_code', $user->merchant?->code)->pluck('account'));
        $response = $this->jkService->getBetTransaction($request->account ?? '', $startTime, $endTime, $agentId);

        if ($response['Error'] == 0 && $response['State'] == 'Success') {
            $formatted = collect($response['Data']['List'])
                ->filter(function ($item) use ($user) {
                    return MerchantPlayer::where('merchant_code', $user->merchant?->code)
                        ->where('account', $item['Account'])
                        ->exists();
                })
                ->map(function ($item) {
                    return [
                        'id' => $item['Sn'],
                        'account' => $item['Account'],
                        'created_at' => Carbon::createFromFormat('Y/m/d H:i:s P', $item['CreateTime'])->toIso8601String(),
                        'initial_balance' => $item['TurnOver'],
                        'bet' => $item['Bet'],
                        'final_balance' => $item['Return'],
                        'win_loss' => $item['WinLoss'],
                        'win_loss_status' => $item['Status'],
                    ];
                })->values()->all();

            return response()->json(
                status: 200,
                data: [
                    'data' => $formatted,
                ]
            );
        }
    }

    /**
     * Get Bet Transactions by Account
     *
     * Get All Bet Transactions , for a particular `account` in between the specified `startTime` and `endTime`.
     * * Bet Transactions only will reflect immediately right after the User is logged out via `api/v1/logoutgame`.
     *
     * @param  Account  $account  Example: `uwg_0001`
     */
    // public function getBetTransactionsByAccount(Request $request, string $account)
    // {
    //     $this->validate($request, [
    //         /** @ignoreParam */
    //         'account' => 'nullable|string|min:8|max:20',
    //         /**
    //          *
    //          * @example 2025-01-01T00:00:00.007Z
    //          */
    //         'startTime' => 'required|date',
    //         /**
    //          *
    //          * @example 2025-01-02T00:00:00.007Z
    //          */
    //         'endTime' => 'required|date',
    //         /**
    //          *
    //          * @example buaya
    //          */
    //         'game_code' => 'required|string',
    //     ]);

    //     if ($request->game_code != 'buaya') {
    //         return response()->json(
    //             status: 400,
    //             data: [
    //                 'message' => 'Invalid game code'
    //             ]
    //         );
    //     }

    //     // TODO: Hardcoded, fix into dynamic in future.
    //     $agentId = $request->user()->merchant->merchantProvider->provider_code;

    //     $response = $this->jkService->getBetTransactionByAccount($account, $request->startTime, $request->endTime, $agentId);

    //     return $response;
    // }
}
