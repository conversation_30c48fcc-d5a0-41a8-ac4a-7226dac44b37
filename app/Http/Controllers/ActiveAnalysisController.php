<?php

namespace App\Http\Controllers;

use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Inertia\Inertia;
use Inertia\Response;

class ActiveAnalysisController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function index(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->subDays(7)->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeId = request()->store_id;
        $timeAggregation = request()->time_aggregation ?? 'hourly';
        if (!in_array($timeAggregation, ['hourly', 'daily', 'monthly'])) {
            $timeAggregation = 'hourly';
        }

        $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
        $stores = [];
        $chartData = null;

        if (! $storeResponse->failed()) {
            $storesData = json_decode($storeResponse, true);
            $stores = $storesData['data'] ?? [];

            if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
                // Only fetch data if there's an explicit search (store_id or dates in request)
                if (request()->has('store_id') || request()->has('startDate')) {
                    // Handle multiple store IDs (comma-separated)
                    $storeIds = request()->user()->merchant?->store_ids;

                    if (request()->store_id && request()->store_id !== 'all') {
                        if (strpos(request()->store_id, ',') !== false) {
                            $storeIds = explode(',', request()->store_id);
                        } else {
                            $storeIds = [request()->store_id];
                        }
                    }

                    $response = $this->uwService->getActiveUserStatistics($storeIds, $startDate, $endDate, $timeAggregation);

                    if ($response->failed()) {
                        throw new Exception('Failed to get active user statistics');
                    }

                    $responseData = json_decode($response, true);
                    $chartData = $responseData['data'] ?? null;
                }
            }
        }

        return Inertia::render('ActiveAnalysis/Index', [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'chartData' => $chartData,
            'filters' => request()->only(['store_id', 'time_aggregation']),
            'stores' => $stores,
            'timeAggregation' => $timeAggregation,
        ]);
    }
}
