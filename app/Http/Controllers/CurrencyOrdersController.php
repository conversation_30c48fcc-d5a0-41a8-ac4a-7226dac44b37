<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use App\Models\CurrencyOrder;
use App\Models\CurrencyOrderStatus;
use App\Models\Customer;
use App\Models\ExchangeRate;
use App\Services\CurrencyOrderService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Inertia\Response;

class CurrencyOrdersController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('CurrencyOrders/Index', [
            'filters' => request()->only('search', 'trashed'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'currencyOrders' => CurrencyOrder::query()
                ->with(['customer', 'inCurrency', 'outCurrency'])
                ->orderBy($sort, $direction)
                ->filter(request()->only('search', 'trashed'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($order) => [
                    'id' => $order->id,
                    'payable_amount' => $order->payable_amount,
                    'receivable_amount' => $order->receivable_amount,
                    'reference' => $order->reference,
                    'commission' => $order->commission,
                    'customer' => $order->customer ? $order->customer->name : null,
                    'status' => $order->currencyOrderStatus->name,
                    'created_at' => $order->created_at->format('Y-m-d H:i:s'),
                ]),
        ]);
    }

    public function all()
    {
        return response()->json(
            CurrencyOrder::whereHas('currencyOrderStatus', function ($query) {
                $query->where('value', 'pending');
            })->get()
        );
    }

    public function create(): Response
    {
        return Inertia::render('CurrencyOrders/Create', [
            'customers' => Customer::all(['id', 'name']),
            'currencies' => Currency::all(['id', 'name']),
            'exchangeRates' => ExchangeRate::all(['id', 'rate']),
        ]);
    }

    public function store(): RedirectResponse
    {
        $currencyOrderType = \App\Models\CurrencyOrderType::where('value', Request::input('currency_order_type_id'))->first();

        if (! $currencyOrderType) {
            return back()->withErrors(['currency_order_type_id' => 'Invalid order type value.'])->withInput();
        }

        $typeSpecificRules = [
            'e' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'out_currency_id' => ['required', 'exists:currencies,id'],
                'payable_amount' => ['required', 'numeric'],
            ],
            'po' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'in_currency_id' => ['required', 'exists:currencies,id'],
                'out_currency_id' => ['required', 'exists:currencies,id'],
                'exchange_rate' => ['required', 'numeric'],
                'receivable_amount' => ['required', 'numeric'],
                'payable_amount' => ['required', 'numeric'],
                'processing_fee' => ['required', 'numeric'],
            ],
            'r' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'in_currency_id' => ['required', 'exists:currencies,id'],
                'exchange_rate' => ['required', 'numeric'],
                'receivable_amount' => ['required', 'numeric'],
            ],
            'tpp' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'out_currency_id' => ['required', 'exists:currencies,id'],
                'payable_amount' => ['required', 'numeric'],
            ],
            'tpr' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'in_currency_id' => ['required', 'exists:currencies,id'],
                'exchange_rate' => ['required', 'numeric'],
                'receivable_amount' => ['required', 'numeric'],
                'processing_fee' => ['required', 'numeric'],
            ],
        ];

        $validationRules = array_merge(
            [
                'currency_order_type_id' => ['required', 'exists:currency_order_types,value'],
            ],
            $typeSpecificRules[$currencyOrderType->value] ?? []
        );

        $validatedData = Request::validate($validationRules);

        $currencyOrder = new CurrencyOrder;
        $currencyOrder->fill(array_merge($validatedData, [
            'currency_order_type_id' => $currencyOrderType->id,
        ]));

        $currencyOrder->reference = CurrencyOrderService::generateReference($currencyOrderType);

        $currencyOrder->customer()->associate($validatedData['customer_id']);
        $currencyOrder->currencyOrderType()->associate($currencyOrderType->id);
        $currencyOrder->currencyOrderStatus()->associate(CurrencyOrderStatus::where('name', 'pending')->first()->id);
        $currencyOrder->createdBy()->associate(Auth::user()->id);

        if (Request::filled('in_currency_id')) {
            $currencyOrder->inCurrency()->associate($validatedData['in_currency_id']);
        }
        if (Request::filled('out_currency_id')) {
            $currencyOrder->outCurrency()->associate($validatedData['out_currency_id']);
        }

        $currencyOrder->save();

        return Redirect::route('currency-orders')->with('success', 'Currency order created successfully.');
    }

    public function edit(CurrencyOrder $currencyOrder): Response
    {
        return Inertia::render('CurrencyOrders/Edit', [
            'currencyOrder' => $currencyOrder->load(['customer', 'currencyOrderType', 'inCurrency', 'outCurrency']),
        ]);
    }

    public function update(CurrencyOrder $currencyOrder): RedirectResponse
    {
        $validatedData = Request::validate([
            'remarks' => ['required', 'string'],
        ]);

        $currencyOrder->remarks = $validatedData['remarks'];
        $currencyOrder->save();

        return Redirect::back()->with('success', 'Currency order updated successfully.');
    }
}
