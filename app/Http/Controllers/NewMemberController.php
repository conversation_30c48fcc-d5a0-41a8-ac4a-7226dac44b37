<?php

namespace App\Http\Controllers;

use App\Exports\NewMembersExport;
use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class NewMemberController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function index(Request $request): Response
    {
        // Get filter parameters from the request
        $startDate = $request->startDate
            ? Carbon::parse($request->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = $request->endDate
            ? Carbon::parse($request->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeIds = $request->user()->merchant?->store_ids;

        if ($request->store_id && $request->store_id !== 'all') {
            if (strpos($request->store_id, ',') !== false) {
                $storeIds = explode(',', $request->store_id);
            } else {
                $storeIds = [$request->store_id];
            }
        }

        // Pagination parameters
        $currentPage = $request->get('page', 1);
        $perPage = $request->get('perPage', 10);

        $newMembers = [];
        $pagination = null;

        try {
            $response = $this->uwService->getNewMembers($storeIds, $startDate, $endDate, $currentPage, $perPage, $request->url());

            if (!$response->failed()) {
                $responseData = json_decode($response, true);
                $newMembers = $responseData['data'] ?? [];
                $pagination = $responseData['pagination'] ?? null;
            }
        } catch (Exception $e) {
            // Handle API error gracefully
            $newMembers = [];
        }

        return Inertia::render('NewMembers/Index', [
            'newMembers' => $newMembers,
            'pagination' => $pagination,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'store_id' => $request->store_id,
            'perPage' => $perPage,
        ]);
    }

    public function export(Request $request)
    {
        $startDate = $request->startDate
            ? Carbon::parse($request->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = $request->endDate
            ? Carbon::parse($request->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeIds = $request->user()->merchant?->store_ids;

        if ($request->store_id && $request->store_id !== 'all') {
            if (strpos($request->store_id, ',') !== false) {
                $storeIds = explode(',', $request->store_id);
            } else {
                $storeIds = [$request->store_id];
            }
        }

        try {
            // For export, get all data without pagination
            $response = $this->uwService->getNewMembers($storeIds, $startDate, $endDate, 1, 999999);

            if ($response->failed()) {
                throw new Exception('Failed to get new members data');
            }

            $responseData = json_decode($response, true);
            $newMembers = $responseData['data'] ?? [];

            $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
            $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');
            $fileName = "new-members-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";

            return Excel::download(new NewMembersExport($newMembers), $fileName);
        } catch (Exception $e) {
            return back()->with('error', 'Failed to export new members data');
        }
    }
}
