<?php

namespace App\Http\Controllers;

use App\Exports\NewMembersExport;
use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class NewMemberController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function index(Request $request): Response
    {
        // Get filter parameters from the request
        $startDate = $request->startDate
            ? Carbon::parse($request->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = $request->endDate
            ? Carbon::parse($request->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeIds = $request->user()->merchant?->store_ids;

        if ($request->store_id && $request->store_id !== 'all') {
            if (strpos($request->store_id, ',') !== false) {
                $storeIds = explode(',', $request->store_id);
            } else {
                $storeIds = [$request->store_id];
            }
        }

        // Pagination parameters
        $currentPage = $request->get('page', 1);
        $perPage = $request->get('perPage', 10);

        $newMembers = [];
        $pagination = null;

        try {
            $response = $this->uwService->getNewMembers($storeIds, $startDate, $endDate, $currentPage, $perPage, $request->url());

            if (!$response->failed()) {
                $responseData = json_decode($response, true);
                $newMembers = $responseData['data']['items'] ?? [];

                // Transform the pagination structure to match Laravel's pagination format
                $pageData = $responseData['data']['page'] ?? null;
                $pagination = null;

                if ($pageData) {
                    $total = $pageData['total'] ?? 0;
                    $perPageValue = $pageData['per_page'] ?? $perPage;
                    $currentPageValue = $pageData['current_page'] ?? $currentPage;
                    $lastPage = ceil($total / $perPageValue);

                    // Build links array for page numbers only (Previous/Next handled separately by component)
                    $links = [];

                    // Determine page range to show
                    $start = max(1, $currentPageValue - 2);
                    $end = min($lastPage, $currentPageValue + 2);

                    // Adjust range to show up to 5 pages when possible
                    if ($end - $start < 4) {
                        if ($start == 1) {
                            $end = min($lastPage, $start + 4);
                        } else {
                            $start = max(1, $end - 4);
                        }
                    }

                    // Add first page and ellipsis if needed
                    if ($start > 1) {
                        $links[] = [
                            'url' => $request->fullUrlWithQuery(['page' => 1]),
                            'label' => '1',
                            'active' => 1 == $currentPageValue
                        ];

                        // Only add ellipsis if there's a gap (start > 2 means there are pages between 1 and start)
                        if ($start > 2) {
                            $links[] = [
                                'url' => null,
                                'label' => '...',
                                'active' => false
                            ];
                        }
                    }

                    // Add page numbers in the calculated range
                    for ($i = $start; $i <= $end; $i++) {
                        $links[] = [
                            'url' => $request->fullUrlWithQuery(['page' => $i]),
                            'label' => (string) $i,
                            'active' => $i == $currentPageValue
                        ];
                    }

                    // Add ellipsis and last page if needed
                    if ($end < $lastPage) {
                        // Only add ellipsis if there's a gap (end < lastPage - 1 means there are pages between end and lastPage)
                        if ($end < $lastPage - 1) {
                            $links[] = [
                                'url' => null,
                                'label' => '...',
                                'active' => false
                            ];
                        }

                        $links[] = [
                            'url' => $request->fullUrlWithQuery(['page' => $lastPage]),
                            'label' => (string) $lastPage,
                            'active' => $lastPage == $currentPageValue
                        ];
                    }

                    $pagination = [
                        'total' => $total,
                        'per_page' => $perPageValue,
                        'current_page' => $currentPageValue,
                        'last_page' => $lastPage,
                        'first_page_url' => $request->fullUrlWithQuery(['page' => 1]),
                        'last_page_url' => $request->fullUrlWithQuery(['page' => $lastPage]),
                        'next_page_url' => $currentPageValue < $lastPage ? $request->fullUrlWithQuery(['page' => $currentPageValue + 1]) : null,
                        'prev_page_url' => $currentPageValue > 1 ? $request->fullUrlWithQuery(['page' => $currentPageValue - 1]) : null,
                        'links' => $links,
                    ];
                }
            }
        } catch (Exception $e) {
            // Handle API error gracefully
            $newMembers = [];
        }

        return Inertia::render('NewMembers/Index', [
            'newMembers' => $newMembers,
            'pagination' => $pagination,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'store_id' => $request->store_id,
            'perPage' => $perPage,
        ]);
    }

    public function export(Request $request)
    {
        $startDate = $request->startDate
            ? Carbon::parse($request->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = $request->endDate
            ? Carbon::parse($request->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeIds = $request->user()->merchant?->store_ids;

        if ($request->store_id && $request->store_id !== 'all') {
            if (strpos($request->store_id, ',') !== false) {
                $storeIds = explode(',', $request->store_id);
            } else {
                $storeIds = [$request->store_id];
            }
        }

        try {
            // For export, get all data without pagination
            $response = $this->uwService->getNewMembers($storeIds, $startDate, $endDate, 1, 999999);

            if ($response->failed()) {
                throw new Exception('Failed to get new members data');
            }

            $responseData = json_decode($response, true);
            $newMembers = $responseData['data']['items'] ?? [];

            $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
            $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');
            $fileName = "new-members-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";

            return Excel::download(new NewMembersExport($newMembers), $fileName);
        } catch (Exception $e) {
            return back()->with('error', 'Failed to export new members data');
        }
    }
}
