<?php

namespace App\Http\Controllers;

use App\Exports\CurrencyDetailExport;
use App\Exports\MachineSummaryExport;
use App\Exports\PosReportExport;
use App\Models\UW\Store;
use App\Services\SonicService;
use App\Services\UWService;
use App\Traits\DateTrait;
use App\Traits\DecimalTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function deposit(): Response
    {
        $startDate = request()->startDate ?? Carbon::now()->startOfDay();
        $endDate = request()->endDate ?? Carbon::now()->endOfDay();
        $currentPage = request()->page ?? 1;
        $perPage = request()->input('perPage', 10);
        $currentUrl = url()->current();

        if (request()->user()->hasRole('merchant')) {
            $storeIds = request()->store_id
                ? [request()->store_id]
                : request()->user()->merchant?->store_ids;

            $response = $this->uwService->getDepositReport(
                $storeIds,
                $startDate,
                $endDate,
                $currentPage,
                $perPage,
                $currentUrl
            );

            if ($response->failed()) {
                throw new Exception('Failed to get deposit report.');
            }

            $response = json_decode($response, true);
            $data = $response['data'];
        }

        return Inertia::render('Reports/Deposit', [
            'filters' => request()->only('search', 'perPage'),
            // 'sort' => [
            //     'field' => $sort,
            //     'direction' => $direction
            // ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $data['items'] ?? [],
            ],
            'page' => $data['page'] ?? [],
        ]);
    }

    public function withdrawal(): Response
    {
        $startDate = request()->startDate ?? Carbon::now()->startOfDay();
        $endDate = request()->endDate ?? Carbon::now()->endOfDay();
        $searchQuery = '&search=' . request()->search . '&startDate=' . $startDate . '&endDate=' . $endDate;
        $currentPage = request()->page ?? 1;
        $perPage = request()->input('perPage', 10);
        $currentUrl = url()->current();

        if (request()->user()->hasRole('merchant')) {
            $storeIds = request()->store_id
                ? [request()->store_id]
                : request()->user()->merchant?->store_ids;

            $response = $this->uwService->getWithdrawalReport(
                $storeIds,
                $startDate,
                $endDate,
                $currentPage,
                $perPage,
                $currentUrl
            );

            if ($response->failed()) {
                throw new Exception('Failed to get deposit report.');
            }

            $response = json_decode($response, true);
            $data = $response['data'];
        }

        return Inertia::render('Reports/Withdrawal', [
            'filters' => request()->only('search', 'perPage'),
            // 'sort' => [
            //     'field' => $sort,
            //     'direction' => $direction
            // ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $data['items'] ?? [],
            ],
            'page' => $data['page'] ?? [],
        ]);
    }

    public function machine(): Response
    {
        $sonicService = app(SonicService::class);

        $startDate = request()->startDate ?? Carbon::now()->startOfDay();
        $endDate = request()->endDate ?? Carbon::now()->endOfDay();
        $searchQuery = '&search=' . request()->search . '&startDate=' . $startDate . '&endDate=' . $endDate;
        $currentPage = request()->page ?? 1;
        $perPage = request()->input('perPage', 10);

        $data = [];
        $stores = Store::whereIn('id', request()->user()->merchant?->store_ids)->get();

        foreach ($stores as $store) {
            $response = $sonicService->getMachineSummary(
                $store->id,
                $startDate,
                $endDate,

                // Test Params
                // 3,
                // '2025-01-06T08:23:47.644Z',
                // '2025-01-06T09:23:47.644Z',
            );

            $totalTokenIn = 0;
            $totalTokenOut = 0;
            $totalProfit = 0;
            $items = json_decode($response->body(), true);

            if ($items) {
                foreach ($items as $item) {
                    $totalTokenIn += $item['tokenIn'] ?? 0;
                    $totalTokenOut += $item['tokenOut'] ?? 0;
                    $totalProfit += $item['totalprofit'] ?? 0;
                }
            }

            $data[$store->id] = [
                'store' => $store->name,
                'total_token_in' => $totalTokenIn,
                'total_token_out' => $totalTokenOut,
                'total_profit' => $totalProfit,
            ];
        }

        $items = array_slice($data, ($currentPage - 1) * $perPage, $perPage);
        $paginator = new LengthAwarePaginator($items, count($data), $perPage, $currentPage);
        $links = [];

        if ($paginator->lastPage() != 1) {
            $links[] = [
                'url' => url()->current() . '?perPage=' . $perPage . '&page=' . $paginator->lastPage() . $searchQuery,
                'label' => $paginator->lastPage(),
                'active' => false,
            ];
        }

        return Inertia::render('Reports/Machine', [
            'filters' => request()->only('search', 'perPage'),
            // 'sort' => [
            //     'field' => $sort,
            //     'direction' => $direction
            // ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => $paginator->items(),
            ],
            'page' => [
                'links' => $links,
                'first_page_url' => url()->current() . '?perPage=' . $perPage . '&page=1' . $searchQuery,
                'last_page_url' => url()->current() . '?perPage=' . $perPage . '&page=' . $paginator->lastPage() . $searchQuery,
                'prev_page_url' => $paginator->previousPageUrl(),
                'next_page_url' => $paginator->nextPageUrl(),
                'total' => $paginator->total(),
                'per_page' => $perPage,
                'current_page' => $currentPage,
            ],
        ]);
    }

    public function posReport(string $type = 'recharge'): Response
    {
        $sonicService = app(SonicService::class);

        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y-m-d\TH:i:s.000\Z')
            : Carbon::now()->startOfDay()->format('Y-m-d\TH:i:s.000\Z');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->setTime(Carbon::parse(request()->endDate)->hour, Carbon::parse(request()->endDate)->minute, 59)->format('Y-m-d\TH:i:s.999\Z')
            : Carbon::now()->endOfDay()->setTime(Carbon::now()->hour, Carbon::now()->minute, 59)->format('Y-m-d\TH:i:s.999\Z');

        $currentPage = (int) (request()->page ?? 1);
        $perPage = (int) (request()->input('perPage', 10));
        $isIncludeOnline = request()->isIncludeOnline !== 'false';
        $storeId = request()->store_id;

        // Get stores for the merchant
        $stores = [];
        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
            if (! $storeResponse->failed()) {
                $storesData = json_decode($storeResponse, true);
                $stores = $storesData['data'] ?? [];

                if (! $storeId && ! empty($stores)) {
                    $storeId = $stores[0]['id'];
                }
            }
        }

        $data = [
            'items' => [],
            'grand_total' => [
                'total_initial' => '0.00',
                'total_amount' => '0.00',
                'total_balance' => '0.00',
                'total_all_amount' => '0.00',
            ],
        ];

        $pagination = [
            'links' => [],
            'current_page' => $currentPage,
            'from' => 0,
            'last_page' => 1,
            'per_page' => $perPage,
            'to' => 0,
            'total' => 0,
        ];

        if ((request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) &&
            (request()->has('store_id') || request()->has('startDate'))
        ) {

            $response = $sonicService->getPosReport(
                $storeId,
                $startDate,
                $endDate,
                $currentPage,
                $perPage,
                $isIncludeOnline,
                $type === 'elimination' ? 2 : 1
            );

            if ($response->successful()) {
                $responseData = json_decode($response->body(), true);

                // Format list data
                $formattedList = [];
                foreach ($responseData['list'] ?? [] as $eachData) {
                    $formattedList[] = [
                        'terminal_serial' => $eachData['terminal_serial'] ?? null,
                        'terminal_address' => $eachData['terminal_address'] ?? null,
                        'member_card_id' => $eachData['member_card_id'] ?? null,
                        'member_card_serial_no' => $eachData['member_card_no'] ?? null,
                        'phone_no' => $eachData['member_phone'] ?? null,
                        'transaction_type' => $eachData['operation_type'] ?? null,
                        'transaction_date' => DateTrait::dateFormat($eachData['date_time']),
                        'initial_balance' => DecimalTrait::setDecimal($eachData['member_balance'] ?? 0),
                        'amount_used' => DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0),
                        'balance' => DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0),
                    ];
                }

                // Format grand total data
                $grandTotal = [
                    'total_initial' => '0.00',
                    'total_amount' => '0.00',
                    'total_balance' => '0.00',
                    'total_all_amount' => '0.00',
                ];

                foreach ($responseData['sum'] ?? [] as $eachData) {
                    $grandTotal = [
                        'total_initial' => DecimalTrait::setDecimal($eachData['total_initial'] ?? 0),
                        'total_amount' => DecimalTrait::setDecimal($eachData['total_amount'] ?? 0),
                        'total_balance' => DecimalTrait::setDecimal($eachData['total_balance'] ?? 0),
                        'total_all_amount' => DecimalTrait::setDecimal($eachData['total_all_amount'] ?? 0),
                    ];
                }

                // Manual pagination logic
                $hasMoreData = ! empty($formattedList); // If we have data, assume there might be more
                $searchQuery = http_build_query(request()->only(['store_id', 'startDate', 'endDate', 'isIncludeOnline', 'perPage']));

                $links = [];

                // If we're on page > 1, we know previous pages exist
                if ($currentPage > 1) {
                    // Previous page link
                    $links[] = [
                        'url' => url()->current() . '?' . $searchQuery . '&page=' . ($currentPage - 1),
                        'label' => ($currentPage - 1),
                        'active' => false,
                    ];
                }

                // Current page
                $links[] = [
                    'url' => url()->current() . '?' . $searchQuery . '&page=' . $currentPage,
                    'label' => (string) $currentPage,
                    'active' => true,
                ];

                // If we have data in current page, show next page link
                if ($hasMoreData && count($formattedList) >= $perPage) {
                    $links[] = [
                        'url' => url()->current() . '?' . $searchQuery . '&page=' . ($currentPage + 1),
                        'label' => ($currentPage + 1),
                        'active' => false,
                    ];
                }

                // Calculate the from/to values based on current page and perPage
                $from = ($currentPage - 1) * $perPage + 1;
                $to = $from + count($formattedList) - 1;

                $pagination = [
                    'current_page' => $currentPage,
                    'from' => $from,
                    'last_page' => $hasMoreData && count($formattedList) >= $perPage ? $currentPage + 1 : $currentPage,
                    'per_page' => (int) $perPage,
                    'to' => $to,
                    'total' => $to, // Since we don't have total, use 'to' as minimum total
                    'links' => $links,
                    'first_page_url' => url()->current() . '?' . $searchQuery . '&page=1',
                    'last_page_url' => url()->current() . '?' . $searchQuery . '&page=' . ($hasMoreData && count($formattedList) >= $perPage ? $currentPage + 1 : $currentPage),
                    'next_page_url' => $hasMoreData && count($formattedList) >= $perPage ?
                        url()->current() . '?' . $searchQuery . '&page=' . ($currentPage + 1) : null,
                    'prev_page_url' => $currentPage > 1 ?
                        url()->current() . '?' . $searchQuery . '&page=' . ($currentPage - 1) : null,
                ];

                $data = [
                    'items' => $formattedList,
                    'grand_total' => $grandTotal,
                ];
            }
        }

        return Inertia::render('Reports/Pos' . ucfirst($type), [
            'filters' => [
                'search' => request()->search,
                'perPage' => request()->input('perPage', 10),
                'isIncludeOnline' => request()->input('isIncludeOnline', 'true'),
                'store_id' => $storeId, // Explicitly include store_id in filters
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => $data,
            'page' => $pagination,
            'stores' => $stores,
        ]);
    }

    public function machineSummary(): Response
    {
        $sonicService = app(SonicService::class);

        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y-m-d\TH:i:s.000\Z')
            : Carbon::now()->startOfDay()->format('Y-m-d\TH:i:s.000\Z');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->setTime(Carbon::parse(request()->endDate)->hour, Carbon::parse(request()->endDate)->minute, 59)->format('Y-m-d\TH:i:s.999\Z')
            : Carbon::now()->endOfDay()->setTime(Carbon::now()->hour, Carbon::now()->minute, 59)->format('Y-m-d\TH:i:s.999\Z');

        $storeId = request()->store_id;

        // Get stores for the merchant
        $stores = [];
        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
            if (! $storeResponse->failed()) {
                $storesData = json_decode($storeResponse, true);
                $stores = $storesData['data'] ?? [];

                if (! $storeId && ! empty($stores)) {
                    $storeId = $stores[0]['id'];
                }
            }
        }

        $data = [
            'items' => [],
            'grand_total' => [
                'token_in' => 0,
                'token_out' => 0,
                'total_profit' => 0,
            ],
        ];

        if ((request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) &&
            (request()->has('store_id') || request()->has('startDate'))
        ) {

            $response = $sonicService->getMachineSummary(
                $storeId,
                $startDate,
                $endDate
            );

            if ($response->successful()) {
                $responseData = json_decode($response->body(), true);

                // Format list data and calculate grand total
                $formattedList = [];
                $grandTotal = [
                    'token_in' => 0,
                    'token_out' => 0,
                    'total_profit' => 0,
                ];

                // Directly iterate over response data since it's the list
                foreach ($responseData ?? [] as $eachData) {
                    $formattedList[] = [
                        'pos_id' => $eachData['终端编号'] ?? null,
                        'machine_name' => $eachData['机台名称'] ?? null,
                        'machine_no' => $eachData['机台编号'] ?? null,
                        'token_in' => DecimalTrait::setDecimal($eachData['tokenIn'] ?? 0),
                        'token_out' => DecimalTrait::setDecimal($eachData['tokenOut'] ?? 0),
                        'total_profit' => DecimalTrait::setDecimal($eachData['totalprofit'] ?? 0),
                    ];

                    $grandTotal['token_in'] += DecimalTrait::setDecimal($eachData['tokenIn'] ?? 0);
                    $grandTotal['token_out'] += DecimalTrait::setDecimal($eachData['tokenOut'] ?? 0);
                    $grandTotal['total_profit'] += DecimalTrait::setDecimal($eachData['totalprofit'] ?? 0);
                }

                $data = [
                    'items' => $formattedList,
                    'grand_total' => $grandTotal,
                ];
            }
        }

        return Inertia::render('Reports/MachineSummary', [
            'filters' => [
                'search' => request()->search,
                'store_id' => $storeId,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => $data,
            'stores' => $stores,
        ]);
    }

    public function currencyDetail(): Response
    {
        $sonicService = app(SonicService::class);

        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y-m-d\TH:i:s.000\Z')
            : Carbon::now()->startOfDay()->format('Y-m-d\TH:i:s.000\Z');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->setTime(Carbon::parse(request()->endDate)->hour, Carbon::parse(request()->endDate)->minute, 59)->format('Y-m-d\TH:i:s.999\Z')
            : Carbon::now()->endOfDay()->setTime(Carbon::now()->hour, Carbon::now()->minute, 59)->format('Y-m-d\TH:i:s.999\Z');

        $currentPage = (int) (request()->page ?? 1);
        $perPage = (int) (request()->input('perPage', 10));
        $isIncludeOnline = request()->isIncludeOnline !== 'false';
        $storeId = request()->store_id;

        // Get stores for the merchant
        $stores = [];
        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
            if (! $storeResponse->failed()) {
                $storesData = json_decode($storeResponse, true);
                $stores = $storesData['data'] ?? [];

                if (! $storeId && ! empty($stores)) {
                    $storeId = $stores[0]['id'];
                }
            }
        }

        $data = [
            'items' => [],
            'grand_total' => [
                'total_amount' => 0,
                'total_member_balance' => 0,
                'total_latest_member_balance' => 0,
            ],
        ];

        $pagination = [
            'links' => [],
            'current_page' => $currentPage,
            'from' => 0,
            'last_page' => 1,
            'per_page' => $perPage,
            'to' => 0,
            'total' => 0,
        ];

        if ((request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) &&
            (request()->has('store_id') || request()->has('startDate'))
        ) {

            $response = $sonicService->getCurrencyDetail(
                $storeId,
                $startDate,
                $endDate,
                $currentPage,
                $perPage,
                $isIncludeOnline,
            );

            if ($response->successful()) {
                $responseData = json_decode($response->body(), true);

                // Format list data and calculate grand total
                $formattedList = [];
                $grandTotal = [
                    'total_amount' => 0,
                    'total_member_balance' => 0,
                    'total_latest_member_balance' => 0,
                ];

                foreach ($responseData ?? [] as $eachData) {
                    $formattedList[] = [
                        'row_number' => $eachData['rowNum'] ?? null,
                        'amount' => $eachData['operation_qty'] ?? null,
                        'terminal_serial' => $eachData['terminal_serial'] ?? null,
                        'member_card_id' => $eachData['member_card_id'] ?? null,
                        'member_card_no' => $eachData['member_card_no'] ?? null,
                        'phone_no' => $eachData['member_phone'] ?? null,
                        'operation_type' => $eachData['operation_type'] ?? null,
                        'date_time' => DateTrait::dateFormat($eachData['date_time']) ?? null,
                        'member_balance' => $eachData['member_balance'] ?? null,
                        'latest_member_balance' => $eachData['latest_member_balance'] ?? null,
                    ];

                    // Calculate grand totals
                    $grandTotal['total_amount'] += DecimalTrait::setDecimal($eachData['operation_qty'] ?? 0);
                    $grandTotal['total_member_balance'] += DecimalTrait::setDecimal($eachData['member_balance'] ?? 0);
                    $grandTotal['total_latest_member_balance'] += DecimalTrait::setDecimal($eachData['latest_member_balance'] ?? 0);
                }

                // Manual pagination logic
                $hasMoreData = ! empty($formattedList);
                $searchQuery = http_build_query(request()->only(['store_id', 'startDate', 'endDate', 'perPage']));

                $from = ($currentPage - 1) * $perPage + 1;
                $to = $from + count($formattedList) - 1;

                $pagination = [
                    'current_page' => $currentPage,
                    'from' => $from,
                    'last_page' => $hasMoreData && count($formattedList) >= $perPage ? $currentPage + 1 : $currentPage,
                    'per_page' => (int) $perPage,
                    'to' => $to,
                    'total' => $to,
                    'links' => [],
                    'first_page_url' => url()->current() . '?' . $searchQuery . '&page=1',
                    'last_page_url' => url()->current() . '?' . $searchQuery . '&page=' . ($hasMoreData && count($formattedList) >= $perPage ? $currentPage + 1 : $currentPage),
                    'next_page_url' => $hasMoreData && count($formattedList) >= $perPage ?
                        url()->current() . '?' . $searchQuery . '&page=' . ($currentPage + 1) : null,
                    'prev_page_url' => $currentPage > 1 ?
                        url()->current() . '?' . $searchQuery . '&page=' . ($currentPage - 1) : null,
                ];

                // Generate pagination links
                if ($currentPage > 1) {
                    $pagination['links'][] = [
                        'url' => url()->current() . '?' . $searchQuery . '&page=' . ($currentPage - 1),
                        'label' => ($currentPage - 1),
                        'active' => false,
                    ];
                }

                $pagination['links'][] = [
                    'url' => url()->current() . '?' . $searchQuery . '&page=' . $currentPage,
                    'label' => (string) $currentPage,
                    'active' => true,
                ];

                if ($hasMoreData && count($formattedList) >= $perPage) {
                    $pagination['links'][] = [
                        'url' => url()->current() . '?' . $searchQuery . '&page=' . ($currentPage + 1),
                        'label' => ($currentPage + 1),
                        'active' => false,
                    ];
                }

                $data = [
                    'items' => $formattedList,
                    'grand_total' => $grandTotal,
                ];
            }
        }

        return Inertia::render('Reports/CurrencyDetail', [
            'filters' => [
                'search' => request()->search,
                'perPage' => request()->input('perPage', 10),
                'store_id' => $storeId,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => $data,
            'page' => $pagination,
            'stores' => $stores,
        ]);
    }

    public function exportPosReport(string $type = 'recharge')
    {
        $sonicService = app(SonicService::class);
        $allData = [];
        $currentPage = 1;
        $perPage = 100;
        $hasMoreData = true;

        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y-m-d\TH:i:s.000\Z')
            : Carbon::now()->startOfDay()->format('Y-m-d\TH:i:s.000\Z');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->setTime(Carbon::parse(request()->endDate)->hour, Carbon::parse(request()->endDate)->minute, 59)->format('Y-m-d\TH:i:s.999\Z')
            : Carbon::now()->endOfDay()->setTime(Carbon::now()->hour, Carbon::now()->minute, 59)->format('Y-m-d\TH:i:s.999\Z');

        $isIncludeOnline = request()->isIncludeOnline !== 'false';
        $storeId = request()->store_id;

        while ($hasMoreData) {
            $response = $sonicService->getPosReport(
                $storeId,
                $startDate,
                $endDate,
                $currentPage,
                $perPage,
                $isIncludeOnline,
                $type === 'elimination' ? 2 : 1
            );

            if ($response->successful()) {
                $responseData = json_decode($response->body(), true);
                $items = $responseData['list'] ?? [];

                if (empty($items) || count($items) < $perPage) {
                    $hasMoreData = false;
                }

                foreach ($items as $item) {
                    // Format amounts with explicit zero handling
                    $initialBalance = $item['member_balance'] ?? 0;
                    $amountUsed = $item['operation_qty'] ?? 0;
                    $balance = $item['latest_member_balance'] ?? 0;

                    // Convert to float and format with 2 decimal places
                    $initialBalance = number_format((float) $initialBalance, 2, '.', '');
                    $amountUsed = number_format((float) $amountUsed, 2, '.', '');
                    $balance = number_format((float) $balance, 2, '.', '');

                    $allData[] = [
                        DateTrait::dateFormat($item['date_time']),
                        $item['terminal_address'] ?? '',
                        $item['member_card_no'] ?? '',
                        $item['operation_type'] ?? '',
                        $initialBalance,
                        $amountUsed,
                        $balance,
                    ];
                }

                $currentPage++;
            } else {
                $hasMoreData = false;
            }
        }

        $fileName = sprintf(
            'pos_%s_%s_%s.xlsx',
            $type,
            Carbon::parse($startDate)->format('Ymd'),
            Carbon::parse($endDate)->format('Ymd')
        );

        return Excel::download(new PosReportExport($allData), $fileName);
    }

    public function exportMachineSummary(Request $request)
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y-m-d\TH:i:s.000\Z')
            : Carbon::now()->startOfDay()->format('Y-m-d\TH:i:s.000\Z');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->setTime(Carbon::parse(request()->endDate)->hour, Carbon::parse(request()->endDate)->minute, 59)->format('Y-m-d\TH:i:s.999\Z')
            : Carbon::now()->endOfDay()->setTime(Carbon::now()->hour, Carbon::now()->minute, 59)->format('Y-m-d\TH:i:s.999\Z');

        $storeId = $request->input('store_id');

        $sonicService = new SonicService;
        $response = $sonicService->getMachineSummary(
            $storeId,
            $startDate,
            $endDate
        );

        if ($response->successful()) {
            $responseData = json_decode($response->body(), true);

            // Format list data and calculate grand total
            $formattedList = [];
            $grandTotal = [
                'token_in' => 0,
                'token_out' => 0,
                'total_profit' => 0,
            ];

            // Directly iterate over response data since it's the list
            foreach ($responseData ?? [] as $eachData) {
                $formattedList[] = [
                    'pos_id' => $eachData['终端编号'] ?? null,
                    'machine_name' => $eachData['机台名称'] ?? null,
                    'machine_no' => $eachData['机台编号'] ?? null,
                    'token_in' => DecimalTrait::setDecimal($eachData['tokenIn'] ?? 0),
                    'token_out' => DecimalTrait::setDecimal($eachData['tokenOut'] ?? 0),
                    'total_profit' => DecimalTrait::setDecimal($eachData['totalprofit'] ?? 0),
                ];

                $grandTotal['token_in'] += DecimalTrait::setDecimal($eachData['tokenIn'] ?? 0);
                $grandTotal['token_out'] += DecimalTrait::setDecimal($eachData['tokenOut'] ?? 0);
                $grandTotal['total_profit'] += DecimalTrait::setDecimal($eachData['totalprofit'] ?? 0);
            }

            $data = [
                'items' => $formattedList,
                'grand_total' => $grandTotal,
            ];

            $fileName = sprintf(
                'machine_summary_%s_%s.xlsx',
                Carbon::parse($startDate)->format('Ymd'),
                Carbon::parse($endDate)->format('Ymd')
            );

            return Excel::download(new MachineSummaryExport($data), $fileName);
        }

        return back()->with('error', 'Failed to export data');
    }

    public function exportCurrencyDetail(Request $request)
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->format('Y-m-d\TH:i:s.000\Z')
            : Carbon::now()->startOfDay()->format('Y-m-d\TH:i:s.000\Z');

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->setTime(Carbon::parse(request()->endDate)->hour, Carbon::parse(request()->endDate)->minute, 59)->format('Y-m-d\TH:i:s.999\Z')
            : Carbon::now()->endOfDay()->setTime(Carbon::now()->hour, Carbon::now()->minute, 59)->format('Y-m-d\TH:i:s.999\Z');

        $storeId = $request->input('store_id');
        $allData = [];

        // Increase items per page for faster fetching
        $perPage = 1000;
        $currentPage = 1;
        $hasMoreData = true;

        $sonicService = new SonicService;

        try {
            while ($hasMoreData) {
                $response = $sonicService->getCurrencyDetail(
                    $storeId,
                    $startDate,
                    $endDate,
                    $currentPage,
                    $perPage,
                    true
                );

                if (! $response->successful()) {
                    throw new \Exception('Failed to fetch data from service');
                }

                $responseData = json_decode($response->body(), true);

                if (empty($responseData) || count($responseData) < $perPage) {
                    $hasMoreData = false;
                }

                foreach ($responseData as $item) {
                    $allData[] = [
                        'date_time' => DateTrait::dateFormat($item['date_time']),
                        'terminal_serial' => $item['terminal_serial'] ?? '',
                        'member_card_id' => $item['member_card_id'] ?? '',
                        'member_card_no' => $item['member_card_no'] ?? '',
                        'phone_no' => $item['member_phone'] ?? '', // Will be formatted in export class
                        'operation_type' => $item['operation_type'] ?? '',
                        'amount' => number_format((float) ($item['operation_qty'] ?? 0), 2, '.', ''),
                        'member_balance' => number_format((float) ($item['member_balance'] ?? 0), 2, '.', ''),
                        'latest_member_balance' => number_format((float) ($item['latest_member_balance'] ?? 0), 2, '.', ''),
                    ];
                }

                $currentPage++;
            }

            $fileName = sprintf(
                'currency_detail_%s_%s.xlsx',
                Carbon::parse($startDate)->format('Ymd'),
                Carbon::parse($endDate)->format('Ymd')
            );

            return Excel::download(new CurrencyDetailExport($allData), $fileName);
        } catch (\Exception $e) {
            Log::error('Currency Detail Export Error: ' . $e->getMessage());

            return back()->with('error', 'Failed to generate export. Please try again.');
        }
    }

    public function posStore(): Response
    {
        // Check if user has admin role
        if (!request()->user()->hasRole('admin')) {
            abort(403, 'Unauthorized action.');
        }

        $stores = [];
        $isFetched = false;

        if (request()->has('fetch') && request()->fetch === 'true') {
            $sonicService = app(SonicService::class);

            $response = $sonicService->getStore();

            if ($response->successful()) {
                $stores = json_decode($response->body(), true) ?? [];

                foreach ($stores as &$store) {
                    $store['id'] = $store['id'] ?? null;
                    $store['name'] = $store['name'] ?? null;
                    $store['onlineStatus'] = $store['onlineStatus'] ?? false;
                }

                $isFetched = true;
            }
        }

        return Inertia::render('Reports/PosStore', [
            'stores' => $stores,
            'isFetched' => $isFetched,
        ]);
    }
}
