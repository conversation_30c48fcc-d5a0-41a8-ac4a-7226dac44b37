<?php

namespace App\Http\Controllers;

use App\Exports\StatisticsExport;
use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class StatisticController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function index(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $data = [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'store_id' => request()->store_id,
        ];

        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            if (request()->has('store_id') || request()->has('startDate')) {
                $storeIds = request()->user()->merchant?->store_ids;

                if (request()->store_id && request()->store_id !== 'all') {
                    if (strpos(request()->store_id, ',') !== false) {
                        $storeIds = explode(',', request()->store_id);
                    } else {
                        $storeIds = [request()->store_id];
                    }
                }

                $response = $this->uwService->getStatistics($storeIds, $startDate, $endDate);

                if ($response->failed()) {
                    throw new Exception('Failed to get statistics');
                }

                $response = json_decode($response, true);
                $statistics = $response['data'];

                $data['statistics'] = [
                    'totalRegistrations' => $statistics['totalRegistrations'] ?? 0,
                    'totalActiveUsers' => $statistics['totalActiveUsers'] ?? 0,
                    'totalDeposits' => [
                        'count' => $statistics['totalDeposits']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalDeposits']['sum'], 2, '.', ','),
                    ],
                    'totalWithdrawals' => [
                        'count' => $statistics['totalWithdrawals']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalWithdrawals']['sum'], 2, '.', ','),
                    ],
                    'totalFTD' => [
                        'count' => $statistics['totalFTD']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalFTD']['sum'], 2, '.', ','),
                    ],
                    'totalTurnover' => [
                        'count' => $statistics['totalTurnover']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalTurnover']['sum'], 2, '.', ','),
                    ],
                    'totalTransfer' => [
                        'sum' => number_format((float) $statistics['totalTransfer']['sum'], 2, '.', ','),
                    ],
                    'retentionRate' => number_format((float) $statistics['retentionRate'], 2, '.', ','),
                    'totalSTD' => [
                        'count' => $statistics['totalSTD']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalSTD']['sum'], 2, '.', ','),
                    ],
                    'totalSTDRate' => number_format((float) $statistics['totalSTDRate'], 2, '.', ','),
                    'totalTTD' => [
                        'count' => $statistics['totalTTD']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalTTD']['sum'], 2, '.', ','),
                    ],
                    'totalTTDRate' => number_format((float) $statistics['totalTTDRate'], 2, '.', ','),
                ];

                if (request()->user()->hasRole('admin')) {
                    $data['statistics']['totalPromotion'] = [
                        'count' => $statistics['totalPromotion']['count'] ?? 0,
                        'in' => number_format((float) $statistics['totalPromotion']['promotion_in'], 2, '.', ','),
                        'in_count' => $statistics['totalPromotion']['promotion_in_count'] ?? 0,
                        'out' => number_format((float) $statistics['totalPromotion']['promotion_out'], 2, '.', ','),
                        'out_count' => $statistics['totalPromotion']['promotion_out_count'] ?? 0,
                        'burnt' => number_format((float) $statistics['totalPromotion']['promotion_burnt'], 2, '.', ','),
                    ];

                    $data['statistics']['totalAdjustment'] = [
                        'in' => [
                            'count' => $statistics['totalAdjustment']['adjustment_in']['count'] ?? 0,
                            'sum' => number_format((float) $statistics['totalAdjustment']['adjustment_in']['sum'], 2, '.', ','),
                        ],
                        'out' => [
                            'count' => $statistics['totalAdjustment']['adjustment_out']['count'] ?? 0,
                            'sum' => number_format((float) $statistics['totalAdjustment']['adjustment_out']['sum'], 2, '.', ','),
                        ],
                        'nett' => number_format((float) $statistics['totalAdjustment']['adjustment_nett'], 2, '.', ','),
                    ];

                    $data['statistics']['totalAdvanceCredit'] = [
                        'count' => $statistics['totalAdvanceCredit']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalAdvanceCredit']['sum'], 2, '.', ','),
                    ];
                }
            } else {
                // Return empty statistics structure when no explicit search
                $data['statistics'] = [
                    'totalRegistrations' => 0,
                    'totalActiveUsers' => 0,
                    'totalDeposits' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                    'totalWithdrawals' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                    'totalFTD' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                    'totalTurnover' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                    'totalTransfer' => [
                        'sum' => '0.00',
                    ],
                    'retentionRate' => '0.00',
                    'totalSTD' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                    'totalSTDRate' => '0.00',
                    'totalTTD' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                    'totalTTDRate' => '0.00',
                ];

                // Only include promotion and adjustment data for admin users
                if (request()->user()->hasRole('admin')) {
                    $data['statistics']['totalPromotion'] = [
                        'count' => 0,
                        'in' => '0.00',
                        'in_count' => 0,
                        'out' => '0.00',
                        'out_count' => 0,
                        'burnt' => '0.00',
                    ];

                    $data['statistics']['totalAdjustment'] = [
                        'in' => [
                            'count' => 0,
                            'sum' => '0.00',
                        ],
                        'out' => [
                            'count' => 0,
                            'sum' => '0.00',
                        ],
                        'nett' => '0.00',
                    ];

                    $data['statistics']['totalAdvanceCredit'] = [
                        'count' => 0,
                        'sum' => '0.00',
                    ];
                }
            }
        }

        return Inertia::render('Statistic/Index', $data);
    }

    public function export()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeIds = request()->user()->merchant?->store_ids;

        if (request()->store_id && request()->store_id !== 'all') {
            if (strpos(request()->store_id, ',') !== false) {
                $storeIds = explode(',', request()->store_id);
            } else {
                $storeIds = [request()->store_id];
            }
        }

        // Get all available stores
        $storeResponse = $this->uwService->getStores($storeIds);
        $stores = [];

        if (! $storeResponse->failed()) {
            $storesData = json_decode($storeResponse, true);
            $stores = $storesData['data'] ?? [];
        }

        $storeDescription = '-All-Stores';
        $storeTitle = 'Statistics';
        $isAllStores = true;

        if (request()->has('store_id') && request()->store_id !== null && request()->store_id !== '' && request()->store_id !== 'all' && strtolower(request()->store_id) !== 'all') {
            $isAllStores = false;
            if (strpos(request()->store_id, ',') !== false) {
                $storeDescription = '-Multiple-Stores';
                $storeTitle = 'Statistics - Multiple Stores';

                $requestedStoreIds = explode(',', request()->store_id);
                $stores = array_filter($stores, function ($store) use ($requestedStoreIds) {
                    return in_array($store['id'], $requestedStoreIds);
                });
            } else {
                $storeResponse = $this->uwService->getStores([request()->store_id]);
                if (!$storeResponse->failed()) {
                    $storeData = json_decode($storeResponse, true);
                    if (!empty($storeData['data'])) {
                        $storeName = $storeData['data'][0]['name'] ?? 'Single-Store';
                        $storeDescription = '-' . str_replace(' ', '-', $storeName);
                        $storeTitle = 'Statistics - ' . $storeName;

                        $stores = array_filter($stores, function ($store) use ($storeIds) {
                            return $store['id'] == $storeIds[0];
                        });
                    } else {
                        $storeDescription = '-Single-Store';
                        $storeTitle = 'Statistics - Single Store';
                    }
                } else {
                    $storeDescription = '-Single-Store';
                    $storeTitle = 'Statistics - Single Store';
                }
            }
        }

        if (empty($stores)) {
            $response = $this->uwService->getStatistics($storeIds, $startDate, $endDate);

            if ($response->failed()) {
                throw new Exception('Failed to get statistics');
            }

            $response = json_decode($response, true);
            $statistics = $response['data'];

            // Format the statistics to ensure all fields expected by the export sheet are available
            $statistics = $this->formatStatisticsForExport($statistics);

            $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
            $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');

            if ($isAllStores || request()->store_id === 'all' || strtolower(request()->store_id) === 'all') {
                $fileName = "statistics-All-Stores-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
            } else {
                $fileName = "statistics{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
            }

            return Excel::download(new StatisticsExport($statistics, $storeTitle), $fileName);
        }

        $allStoreStats = [];
        foreach ($stores as $store) {
            $storeId = $store['id'];
            $storeName = $store['name'];

            $response = $this->uwService->getStatistics([$storeId], $startDate, $endDate);

            if (! $response->failed()) {
                $storeStats = json_decode($response, true);
                $storeData = $storeStats['data'];

                $storeData = $this->formatStatisticsForExport($storeData);

                $allStoreStats[$storeName] = $storeData;
            }
        }

        if (empty($allStoreStats)) {
            $response = $this->uwService->getStatistics($storeIds, $startDate, $endDate);

            if ($response->failed()) {
                throw new Exception('Failed to get statistics for all stores');
            }

            $response = json_decode($response, true);
            $statistics = $response['data'];
            $statistics = $this->formatStatisticsForExport($statistics);

            $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
            $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');

            if ($isAllStores || request()->store_id === 'all' || strtolower(request()->store_id) === 'all') {
                $fileName = "statistics-All-Stores-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
            } else {
                $fileName = "statistics{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
            }

            return Excel::download(new StatisticsExport($statistics, $storeTitle), $fileName);
        }

        $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
        $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');
        
        if ($isAllStores || request()->store_id === 'all' || strtolower(request()->store_id) === 'all') {
            $fileName = "statistics-All-Stores-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
        } else {
            $fileName = "statistics{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
        }

        return Excel::download(
            new StatisticsExport($allStoreStats, $storeTitle, true),
            $fileName
        );
    }

    /**
     * Format statistics data to ensure all required fields are available for the export
     *
     * @param  array  $data  The raw statistics data
     * @return array The formatted data with all required fields
     */
    private function formatStatisticsForExport($data)
    {
        // Basic counts
        $data['totalRegistrations'] = $data['totalRegistrations'] ?? 0;
        $data['totalActiveUsers'] = $data['totalActiveUsers'] ?? 0;

        // Rates - store as decimal numbers (not as percentage strings)
        // For example, 50% should be stored as 0.5 so Excel's percentage format can correctly display it
        $data['retentionRate'] = (float) ($data['retentionRate'] ?? 0) / 100;
        $data['totalSTDRate'] = (float) ($data['totalSTDRate'] ?? 0) / 100;
        $data['totalTTDRate'] = (float) ($data['totalTTDRate'] ?? 0) / 100;

        // Initialize totalDeposits
        if (! isset($data['totalDeposits']) || ! is_array($data['totalDeposits'])) {
            $data['totalDeposits'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['totalDeposits']['count'] = $data['totalDeposits']['count'] ?? 0;
            $data['totalDeposits']['sum'] = $data['totalDeposits']['sum'] ?? 0;
        }

        // Initialize totalWithdrawals
        if (! isset($data['totalWithdrawals']) || ! is_array($data['totalWithdrawals'])) {
            $data['totalWithdrawals'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['totalWithdrawals']['count'] = $data['totalWithdrawals']['count'] ?? 0;
            $data['totalWithdrawals']['sum'] = $data['totalWithdrawals']['sum'] ?? 0;
        }

        // Initialize totalFTD
        if (! isset($data['totalFTD']) || ! is_array($data['totalFTD'])) {
            $data['totalFTD'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['totalFTD']['count'] = $data['totalFTD']['count'] ?? 0;
            $data['totalFTD']['sum'] = $data['totalFTD']['sum'] ?? 0;
        }

        // Initialize totalSTD
        if (! isset($data['totalSTD']) || ! is_array($data['totalSTD'])) {
            $data['totalSTD'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['totalSTD']['count'] = $data['totalSTD']['count'] ?? 0;
            $data['totalSTD']['sum'] = $data['totalSTD']['sum'] ?? 0;
        }

        // Initialize totalTTD
        if (! isset($data['totalTTD']) || ! is_array($data['totalTTD'])) {
            $data['totalTTD'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['totalTTD']['count'] = $data['totalTTD']['count'] ?? 0;
            $data['totalTTD']['sum'] = $data['totalTTD']['sum'] ?? 0;
        }

        // Initialize totalTransfer
        if (! isset($data['totalTransfer']) || ! is_array($data['totalTransfer'])) {
            $data['totalTransfer'] = ['sum' => 0];
        } else {
            $data['totalTransfer']['sum'] = $data['totalTransfer']['sum'] ?? 0;
        }

        // Initialize totalAdvanceCredit
        if (! isset($data['totalAdvanceCredit']) || ! is_array($data['totalAdvanceCredit'])) {
            $data['totalAdvanceCredit'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['totalAdvanceCredit']['count'] = $data['totalAdvanceCredit']['count'] ?? 0;
            $data['totalAdvanceCredit']['sum'] = $data['totalAdvanceCredit']['sum'] ?? 0;
        }

        // Initialize totalPromotion - make sure to use the exact keys from the index method
        if (! isset($data['totalPromotion']) || ! is_array($data['totalPromotion'])) {
            $data['totalPromotion'] = [
                'count' => 0,
                'promotion_in' => 0,
                'promotion_in_count' => 0,
                'promotion_out' => 0,
                'promotion_out_count' => 0,
                'promotion_burnt' => 0,
            ];
        } else {
            $data['totalPromotion']['count'] = $data['totalPromotion']['count'] ?? 0;
            $data['totalPromotion']['promotion_in'] = $data['totalPromotion']['promotion_in'] ?? 0;
            $data['totalPromotion']['promotion_in_count'] = $data['totalPromotion']['promotion_in_count'] ?? 0;
            $data['totalPromotion']['promotion_out'] = $data['totalPromotion']['promotion_out'] ?? 0;
            $data['totalPromotion']['promotion_out_count'] = $data['totalPromotion']['promotion_out_count'] ?? 0;
            $data['totalPromotion']['promotion_burnt'] = $data['totalPromotion']['promotion_burnt'] ?? 0;
        }

        // Initialize totalAdjustment - make sure to use the exact keys from the index method
        if (! isset($data['totalAdjustment']) || ! is_array($data['totalAdjustment'])) {
            $data['totalAdjustment'] = [
                'adjustment_in' => ['count' => 0, 'sum' => 0],
                'adjustment_out' => ['count' => 0, 'sum' => 0],
                'adjustment_nett' => 0,
            ];
        } else {
            if (! isset($data['totalAdjustment']['adjustment_in']) || ! is_array($data['totalAdjustment']['adjustment_in'])) {
                $data['totalAdjustment']['adjustment_in'] = ['count' => 0, 'sum' => 0];
            } else {
                $data['totalAdjustment']['adjustment_in']['count'] = $data['totalAdjustment']['adjustment_in']['count'] ?? 0;
                $data['totalAdjustment']['adjustment_in']['sum'] = $data['totalAdjustment']['adjustment_in']['sum'] ?? 0;
            }

            if (! isset($data['totalAdjustment']['adjustment_out']) || ! is_array($data['totalAdjustment']['adjustment_out'])) {
                $data['totalAdjustment']['adjustment_out'] = ['count' => 0, 'sum' => 0];
            } else {
                $data['totalAdjustment']['adjustment_out']['count'] = $data['totalAdjustment']['adjustment_out']['count'] ?? 0;
                $data['totalAdjustment']['adjustment_out']['sum'] = $data['totalAdjustment']['adjustment_out']['sum'] ?? 0;
            }

            $data['totalAdjustment']['adjustment_nett'] = $data['totalAdjustment']['adjustment_nett'] ?? 0;
        }

        return $data;
    }
}
