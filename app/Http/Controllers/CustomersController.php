<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class CustomersController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Customers/Index', [
            'filters' => request()->only('search', 'trashed'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'customers' => Customer::query()
                ->with(['agent', 'referral'])
                ->orderBy($sort, $direction)
                ->filter(request()->only('search', 'trashed'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($customer) => [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'code' => $customer->code,
                    'display_rate' => $customer->display_rate,
                    'credit_limit' => $customer->credit_limit,
                    'credit_amount' => $customer->credit_amount,
                    'total_balance' => $customer->total_balance,
                    'is_active' => $customer->is_active,
                    'remarks' => $customer->remarks,
                    'agent' => $customer->agent ? [
                        'name' => $customer->agent->name,
                    ] : null,
                    'referral' => $customer->referral ? [
                        'name' => $customer->referral->name,
                    ] : null,
                    'deleted_at' => $customer->deleted_at,
                    'created_at' => $customer->created_at->format('Y-m-d H:i:s'),
                ]),
        ]);
    }

    public function all()
    {
        return response()->json(Customer::where('is_active', true)->get());
    }

    public function create(): Response
    {
        return Inertia::render('Customers/Create');
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', 'unique:customers'],
            'display_rate' => ['required', 'numeric', 'between:0,99999999.99'],
            'credit_limit' => ['required', 'numeric', 'between:0,99999999.99'],
            'is_active' => ['required', Rule::in([true, false])],
            'remarks' => ['nullable', 'string'],
            'agent_id' => ['required', 'exists:users,id'],
            'referral_id' => ['nullable', 'exists:customers,id'],
        ]);

        $customer = new Customer;
        $customer->fill(Request::only(
            'name',
            'code',
            'display_rate',
            'credit_limit',
            'is_active',
            'remarks'
        ));
        $customer->agent()->associate(Request::get('agent_id'));
        $customer->referral()->associate(Request::get('referral_id'));
        $customer->save();

        return Redirect::route('customers')->with('success', 'Customer created successfully.');
    }

    public function edit(Customer $customer): Response
    {
        return Inertia::render('Customers/Edit', [
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'code' => $customer->code,
                'display_rate' => $customer->display_rate,
                'credit_limit' => $customer->credit_limit,
                'credit_amount' => $customer->credit_amount,
                'total_balance' => $customer->total_balance,
                'is_active' => $customer->is_active,
                'remarks' => $customer->remarks,
                'agent_id' => $customer->agent_id,
                'referral_id' => $customer->referral_id,
                'deleted_at' => $customer->deleted_at,
            ],
        ]);
    }

    public function update(Customer $customer): RedirectResponse
    {
        Request::validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', Rule::unique('customers')->ignore($customer->id)],
            'display_rate' => ['required', 'numeric', 'between:0,99999999.99'],
            'credit_limit' => ['required', 'numeric', 'between:0,99999999.99'],
            'is_active' => ['required', Rule::in([true, false])],
            'remarks' => ['nullable', 'string'],
            'agent_id' => ['required', 'exists:users,id'],
            'referral_id' => ['nullable', 'exists:customers,id'],
        ]);

        $customer->fill(Request::only(
            'name',
            'code',
            'display_rate',
            'credit_limit',
            'is_active',
            'remarks'
        ));
        $customer->agent()->associate(Request::get('agent_id'));
        $customer->referral()->associate(Request::get('referral_id'));
        $customer->save();

        return Redirect::back()->with('success', 'Customer updated successfully.');
    }

    public function destroy(Customer $customer): RedirectResponse
    {
        $customer->delete();

        return Redirect::route('customers')->with('success', 'Customer deleted successfully.');
    }
}
