<?php

namespace App\Http\Controllers;

use App\Services\UWService;
use Exception;

class StoreController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function all()
    {
        $storeIds = request()->store_id
            ? [request()->store_id]
            : request()->user()->merchant?->store_ids;

        $response = $this->uwService->getStores($storeIds);

        if ($response->failed()) {
            throw new Exception('Failed to get stores.');
        }

        $response = json_decode($response, true);
        $data = $response['data'];

        return response()->json($data);
    }
}
