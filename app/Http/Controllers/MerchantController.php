<?php

namespace App\Http\Controllers;

use App\Models\Merchant;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class MerchantController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Merchants/Index', [
            'filters' => request()->only('search', 'trashed', 'perPage'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'merchants' => Merchant::query()
                ->orderBy($sort, $direction)
                ->filter(request()->only('search', 'trashed'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($merchant) => [
                    'id' => $merchant->id,
                    'name' => $merchant->name,
                    'code' => $merchant->code,
                    'store_ids' => implode(', ', $merchant->store_ids),
                    'created_at' => $merchant->created_at->format('Y-m-d H:i:s'),
                ]),
        ]);
    }

    public function all()
    {
        return response()->json(Merchant::all());
    }

    public function create(): Response
    {
        return Inertia::render('Merchants/Create');
    }

    public function store(Request $request): RedirectResponse
    {
        $data = $request->validate([
            'name' => ['required'],
            'code' => ['required', 'unique:merchants,code'],
            'store_ids' => ['nullable', 'string'],
        ]);

        if ($request->store_ids) {
            $data['store_ids'] = explode(',', $request->store_ids);
        }

        Merchant::create($data);

        return Redirect::route('merchants')->with('success', 'Merchant created.');
    }

    public function edit(Merchant $merchant): Response
    {
        return Inertia::render('Merchants/Edit', [
            'merchant' => [
                'id' => $merchant->id,
                'name' => $merchant->name,
                'code' => $merchant->code,
                'store_ids' => implode(', ', $merchant->store_ids),
            ],
        ]);
    }

    public function update(Request $request, Merchant $merchant): RedirectResponse
    {
        $data = $request->validate([
            'name' => ['required', 'string'],
            'code' => ['required', 'string', Rule::unique('merchants')->ignore($merchant)],
            'store_ids' => ['nullable', 'string'],
        ]);

        if ($request->store_ids) {
            $data['store_ids'] = explode(',', $request->store_ids);
        }

        $merchant->update($data);

        return Redirect::back()->with('success', 'Merchant updated successfully.');
    }

    public function destroy(Merchant $merchant): RedirectResponse
    {
        $merchant->delete();

        return Redirect::route('merchants')->with('success', 'Merchant deleted successfully.');
    }
}
