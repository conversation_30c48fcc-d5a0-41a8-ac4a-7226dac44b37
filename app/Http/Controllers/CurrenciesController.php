<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class CurrenciesController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Currencies/Index', [
            'filters' => request()->only('search', 'trashed'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'currencies' => Currency::query()
                ->orderBy($sort, $direction)
                ->filter(request()->only('search', 'trashed'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($currency) => [
                    'id' => $currency->id,
                    'code' => $currency->code,
                    'name' => $currency->name,
                    'symbol' => $currency->symbol,
                    'photo' => $currency->photo,
                    'is_active' => $currency->is_active,
                    'deleted_at' => $currency->deleted_at,
                    'created_at' => $currency->created_at->format('Y-m-d H:i:s'),
                ]),
        ]);
    }

    public function all()
    {
        return response()->json(Currency::where('is_active', true)->get());
    }

    public function create(): Response
    {
        return Inertia::render('Currencies/Create');
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'code' => ['required', 'max:10', 'unique:currencies'],
            'name' => ['required', 'max:255'],
            'symbol' => ['nullable', 'max:10'],
            'photo' => ['nullable', 'image', 'max:2048'],
            'is_active' => ['required', Rule::in([true, false])],
        ]);

        $currency = new Currency;
        $currency->fill([
            'code' => Request::get('code'),
            'name' => Request::get('name'),
            'symbol' => Request::get('symbol'),
            'is_active' => Request::get('is_active'),
            'photo_path' => Request::file('photo') ? Request::file('photo')->store('currencies') : null,
        ]);
        $currency->save();

        return Redirect::route('currencies')->with('success', 'Currency created successfully.');
    }

    public function edit(Currency $currency): Response
    {
        return Inertia::render('Currencies/Edit', [
            'currency' => [
                'id' => $currency->id,
                'code' => $currency->code,
                'name' => $currency->name,
                'symbol' => $currency->symbol,
                'is_active' => $currency->is_active,
                'photo' => $currency->photo,
                'deleted_at' => $currency->deleted_at,
            ],
        ]);
    }

    public function update(Currency $currency): RedirectResponse
    {
        Request::validate([
            'code' => ['required', 'max:10', Rule::unique('currencies')->ignore($currency->id)],
            'name' => ['required', 'max:255'],
            'symbol' => ['nullable', 'max:10'],
            'photo' => ['nullable', 'image', 'max:2048'],
            'is_active' => ['required', Rule::in([true, false])],
        ]);

        $currency->fill([
            'code' => Request::get('code'),
            'name' => Request::get('name'),
            'symbol' => Request::get('symbol'),
            'is_active' => Request::get('is_active'),
        ]);

        if (Request::file('photo')) {
            if ($currency->photo_path) {
                Storage::delete($currency->photo_path);
            }
            $currency->fill([
                'photo_path' => Request::file('photo')->store('currencies'),
            ]);
        }

        $currency->save();

        return Redirect::back()->with('success', 'Currency updated.');
    }

    public function destroy(Currency $currency): RedirectResponse
    {
        if ($currency->photo_path) {
            Storage::delete($currency->photo_path);
        }

        $currency->delete();

        return Redirect::route('currencies')->with('success', 'Currency deleted successfully.');
    }
}
