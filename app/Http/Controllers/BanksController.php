<?php

namespace App\Http\Controllers;

use App\Models\Bank;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class BanksController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Banks/Index', [
            'filters' => request()->only('search', 'trashed'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'banks' => Bank::query()
                ->with('currency')
                ->orderBy($sort, $direction)
                ->filter(request()->only('search', 'trashed'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($bank) => [
                    'id' => $bank->id,
                    'account_number' => $bank->account_number,
                    'name' => $bank->name,
                    'holder_name' => $bank->holder_name,
                    'total_balance' => $bank->total_balance,
                    'is_active' => $bank->is_active,
                    'currency' => $bank->currency ? [
                        'name' => $bank->currency->name,
                        'photo' => $bank->currency->photo,
                        'code' => $bank->currency->code,
                    ] : null,
                    'deleted_at' => $bank->deleted_at,
                    'created_at' => $bank->created_at->format('Y-m-d H:i:s'),
                ]),
        ]);
    }

    public function all()
    {
        return response()->json(Bank::where('is_active', true)->get());
    }

    public function create(): Response
    {
        return Inertia::render('Banks/Create');
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'account_number' => ['required', 'string', 'unique:banks'],
            'name' => ['required', 'string', 'max:255'],
            'holder_name' => ['required', 'string', 'max:255'],
            'is_active' => ['required', Rule::in([true, false])],
            'currency' => ['required', 'exists:currencies,id'],
        ]);

        $bank = new Bank;
        $bank->fill(Request::only(
            'account_number',
            'name',
            'holder_name',
            'is_active',
        ));
        $bank->currency()->associate(Request::get('currency'));
        $bank->save();

        return Redirect::route('banks')->with('success', 'Bank created successfully.');
    }

    public function edit(Bank $bank): Response
    {
        return Inertia::render('Banks/Edit', [
            'bank' => [
                'id' => $bank->id,
                'account_number' => $bank->account_number,
                'name' => $bank->name,
                'holder_name' => $bank->holder_name,
                'is_active' => $bank->is_active,
                'currency_id' => $bank->currency_id,
                'deleted_at' => $bank->deleted_at,
            ],
        ]);
    }

    public function update(Bank $bank): RedirectResponse
    {
        Request::validate([
            'account_number' => ['required', 'string', Rule::unique('banks')->ignore($bank->id)],
            'name' => ['required', 'string', 'max:255'],
            'holder_name' => ['required', 'string', 'max:255'],
            'is_active' => ['required', Rule::in([true, false])],
            'currency' => ['required', 'exists:currencies,id'],
        ]);

        $bank->fill(Request::only(
            'account_number',
            'name',
            'holder_name',
            'is_active'
        ));
        $bank->currency()->associate(Request::get('currency'));
        $bank->save();

        return Redirect::back()->with('success', 'Bank updated successfully.');
    }

    public function destroy(Bank $bank): RedirectResponse
    {
        $bank->delete();

        return Redirect::route('banks')->with('success', 'Bank deleted successfully.');
    }
}
