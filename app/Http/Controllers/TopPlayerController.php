<?php

namespace App\Http\Controllers;

use App\Exports\TopPlayersExport;
use App\Services\ClickHouse\GameLogService;
use App\Services\ClickHouse\WWJService;
use App\Services\UWService;
use Carbon\Carbon;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class TopPlayerController extends Controller
{
    public function __construct(
        protected UWService $uwService,
        protected WWJService $wwjService,
        protected GameLogService $gameLogService
    ) {}

    public function topPlayers()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeId = request()->store_id;
        $sortBy = request()->sortBy ?? 'deposits';
        $limit = request()->limit ?? 10;

        $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
        $stores = [];
        $topPlayers = [];

        if (! $storeResponse->failed()) {
            $storesData = json_decode($storeResponse, true);
            $stores = $storesData['data'] ?? [];

            $storeIds = request()->user()->merchant?->store_ids;
            if ($storeId && $storeId !== 'all') {
                if (strpos($storeId, ',') !== false) {
                    $storeIds = explode(',', $storeId);
                    $stores = array_filter($stores, function ($store) use ($storeIds) {
                        return in_array($store['id'], $storeIds);
                    });
                } else {
                    $storeIds = [$storeId];
                    $stores = array_filter($stores, function ($store) use ($storeId) {
                        return $store['id'] == $storeId;
                    });
                }
            }

            if ($sortBy == 'turnover' || $sortBy == 'p2p') {
                $initialLimit = $limit * 3;
                $response = $this->uwService->getTopPlayers($storeIds, $startDate, $endDate, 'deposits', $initialLimit);
            } else {
                // For deposits, withdrawals, or any other sort option, use the original sort parameter
                $response = $this->uwService->getTopPlayers($storeIds, $startDate, $endDate, $sortBy, $limit);
            }

            if (! $response->failed()) {
                $responseData = json_decode($response, true);
                $initialTopPlayers = $responseData['data'] ?? [];

                $playerAccounts = [];
                foreach ($initialTopPlayers as $player) {
                    if (isset($player['username'])) {
                        $playerAccounts[] = $player['username'];
                    }
                }

                $turnoverData = [];
                if (! empty($playerAccounts)) {
                    $params = [
                        'start' => $startDate,
                        'end' => $endDate,
                        'store_ids' => $storeIds,
                        'accounts' => $playerAccounts,
                    ];

                    $turnoverData = $this->gameLogService->getPlayersTransactions($params);
                }

                // Enrich player data with turnover information
                foreach ($initialTopPlayers as &$player) {
                    $username = $player['username'] ?? '';
                    $player['turnover_amount'] = 0; // Default value

                    if (isset($turnoverData[$username])) {
                        $player['turnover_amount'] = $turnoverData[$username]['total_turnover'] ?? 0;
                    }

                    // Ensure p2p_amount exists and is a number
                    if (!isset($player['p2p_amount'])) {
                        $player['p2p_amount'] = 0;
                    }
                }
                unset($player);

                if ($sortBy == 'turnover') {
                    usort($initialTopPlayers, function ($a, $b) {
                        return $b['turnover_amount'] <=> $a['turnover_amount'];
                    });

                    $topPlayers = array_slice($initialTopPlayers, 0, $limit);
                } else if ($sortBy == 'p2p') {
                    usort($initialTopPlayers, function ($a, $b) {
                        return $b['p2p_amount'] <=> $a['p2p_amount'];
                    });

                    $topPlayers = array_slice($initialTopPlayers, 0, $limit);
                } else {
                    $topPlayers = $initialTopPlayers;
                }
            }
        }

        return Inertia::render('Reports/TopPlayers', [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'topPlayers' => $topPlayers,
            'filters' => request()->only(['store_id', 'sortBy', 'limit']),
            'stores' => $stores,
            'sortBy' => $sortBy,
        ]);
    }

    public function exportTopPlayers()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeId = request()->store_id;
        $sortBy = request()->sortBy ?? 'deposits';
        $limit = request()->limit ?? 10;

        $storeIds = request()->user()->merchant?->store_ids;
        if ($storeId && $storeId !== 'all') {
            // Check if it's a comma-separated list of store IDs
            if (strpos($storeId, ',') !== false) {
                $storeIds = explode(',', $storeId);
            } else {
                $storeIds = [$storeId];
            }
        }

        $topPlayers = [];

        if ($sortBy == 'turnover' || $sortBy == 'p2p') {
            $initialLimit = $limit * 3;
            $response = $this->uwService->getTopPlayers($storeIds, $startDate, $endDate, 'deposits', $initialLimit);
        } else {
            // For deposits, withdrawals, or any other sort option, use the original sort parameter
            $response = $this->uwService->getTopPlayers($storeIds, $startDate, $endDate, $sortBy, $limit);
        }

        if (! $response->failed()) {
            $responseData = json_decode($response, true);
            $initialTopPlayers = $responseData['data'] ?? [];

            $playerAccounts = [];
            foreach ($initialTopPlayers as $player) {
                if (isset($player['username'])) {
                    $playerAccounts[] = $player['username'];
                }
            }

            $turnoverData = [];
            if (! empty($playerAccounts)) {
                $params = [
                    'start' => $startDate,
                    'end' => $endDate,
                    'store_ids' => $storeIds,
                    'accounts' => $playerAccounts,
                ];

                $turnoverData = $this->gameLogService->getPlayersTransactions($params);
            }

            foreach ($initialTopPlayers as &$player) {
                $username = $player['username'] ?? '';
                $player['turnover_amount'] = 0;

                if (isset($turnoverData[$username])) {
                    $player['turnover_amount'] = $turnoverData[$username]['total_turnover'] ?? 0;
                }

                // Ensure p2p_amount exists and is a number
                if (!isset($player['p2p_amount'])) {
                    $player['p2p_amount'] = 0;
                }
            }
            unset($player);

            if ($sortBy == 'turnover') {
                usort($initialTopPlayers, function ($a, $b) {
                    return $b['turnover_amount'] <=> $a['turnover_amount'];
                });

                $topPlayers = array_slice($initialTopPlayers, 0, $limit);
            } else if ($sortBy == 'p2p') {
                usort($initialTopPlayers, function ($a, $b) {
                    return $b['p2p_amount'] <=> $a['p2p_amount'];
                });

                $topPlayers = array_slice($initialTopPlayers, 0, $limit);
            } else {
                $topPlayers = $initialTopPlayers;
            }
        }

        $startDateLabel = $startDate ? Carbon::parse($startDate)->format('Y-m-d') : 'all-time';
        $endDateLabel = $endDate ? Carbon::parse($endDate)->format('Y-m-d') : 'present';

        $sortTypeLabel = ucfirst($sortBy);

        // Get store name if specific stores are selected
        $storeName = '';
        $storeNameForFile = '';
        if ($storeId && $storeId !== 'all') {
            // For multiple stores
            if (strpos($storeId, ',') !== false) {
                $storeName = ' for Multiple Stores';
                $storeNameForFile = '-multiple_stores';
            } else {
                // For a single store
                $storeResponse = $this->uwService->getStores([$storeId]);
                if (!$storeResponse->failed()) {
                    $storesData = json_decode($storeResponse, true);
                    $stores = $storesData['data'] ?? [];
                    if (!empty($stores)) {
                        $storeName = ' for ' . ($stores[0]['name'] ?? 'Selected Store');
                        $storeNameForFile = '-' . str_replace(' ', '_', $stores[0]['name'] ?? 'selected_store');
                    }
                }
            }
        }

        $title = "Top {$limit} Players by {$sortTypeLabel}{$storeName}";
        $fileName = "top-{$limit}-players-by-{$sortBy}{$storeNameForFile}-{$startDateLabel}-to-{$endDateLabel}.xlsx";

        return Excel::download(
            new TopPlayersExport($topPlayers, $title),
            $fileName
        );
    }
}
