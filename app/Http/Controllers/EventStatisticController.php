<?php

namespace App\Http\Controllers;

use App\Exports\EventStatisticsExport;
use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class EventStatisticController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function index(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $data = [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'store_id' => request()->store_id,
        ];

        if (request()->user()->hasRole('admin')) {
            if (request()->has('store_id') || request()->has('startDate')) {
                $storeIds = request()->user()->merchant?->store_ids;

                if (request()->store_id && request()->store_id !== 'all') {
                    if (strpos(request()->store_id, ',') !== false) {
                        $storeIds = explode(',', request()->store_id);
                    } else {
                        $storeIds = [request()->store_id];
                    }
                }

                $response = $this->uwService->getEventStatistics($storeIds, $startDate, $endDate);

                if ($response->failed()) {
                    throw new Exception('Failed to get event statistics');
                }

                $response = json_decode($response, true);
                $eventStatistics = $response['data'];

                $data['eventStatistics'] = [
                    'fudai' => [
                        'count' => $eventStatistics['fudai']['count'] ?? 0,
                        'sum' => number_format((float) $eventStatistics['fudai']['sum'], 2, '.', ','),
                        'user_count' => $eventStatistics['fudai']['user_count'] ?? 0,
                    ],
                    'luckySpin' => [
                        'count' => $eventStatistics['luckySpin']['count'] ?? 0,
                        'sum' => number_format((float) $eventStatistics['luckySpin']['sum'], 2, '.', ','),
                        'user_count' => $eventStatistics['luckySpin']['user_count'] ?? 0,
                    ],
                    'rebate' => [
                        'count' => $eventStatistics['rebate']['count'] ?? 0,
                        'sum' => number_format((float) $eventStatistics['rebate']['sum'], 2, '.', ','),
                    ],
                    'referral' => [
                        'count' => $eventStatistics['referral']['count'] ?? 0,
                        'sum' => number_format((float) $eventStatistics['referral']['sum'], 2, '.', ','),
                    ],
                ];
            } else {
                // Return empty event statistics structure when no explicit search
                $data['eventStatistics'] = [
                    'fudai' => [
                        'count' => 0,
                        'sum' => '0.00',
                        'user_count' => 0,
                    ],
                    'luckySpin' => [
                        'count' => 0,
                        'sum' => '0.00',
                        'user_count' => 0,
                    ],
                    'rebate' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                    'referral' => [
                        'count' => 0,
                        'sum' => '0.00',
                    ],
                ];
            }
        }

        return Inertia::render('EventStatistic/Index', $data);
    }

    public function export()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        // Get store IDs to query
        $storeIds = request()->user()->merchant?->store_ids;

        // Get all available stores
        $storeResponse = $this->uwService->getStores($storeIds);
        $stores = [];

        if (! $storeResponse->failed()) {
            $storesData = json_decode($storeResponse, true);
            $stores = $storesData['data'] ?? [];
        }

        // Determine if we're exporting for specific stores or all stores
        // Default to All Stores
        $storeDescription = '-All-Stores';
        $storeTitle = 'Event Statistics';
        $isAllStores = true; // Flag to track if we're exporting all stores

        // Debug the request parameters
        // Debug log the store_id parameter
        Log::info('Export store_id parameter:', [
            'store_id' => request()->store_id,
            'request_has_store_id' => request()->has('store_id'),
        ]);
        Log::info('Export request parameters:', [
            'store_id' => request()->store_id,
            'is_null' => request()->store_id === null,
            'is_empty' => request()->store_id === '',
            'is_all' => request()->store_id === 'all',
        ]);

        // Filter stores based on selection
        if (request()->has('store_id') && request()->store_id !== null && request()->store_id !== '' && request()->store_id !== 'all' && strtolower(request()->store_id) !== 'all') {
            $isAllStores = false; // Not exporting all stores
            if (strpos(request()->store_id, ',') !== false) {
                $storeDescription = '-Multiple-Stores';
                $storeTitle = 'Event Statistics - Multiple Stores';

                // Filter stores to only include the selected ones
                $requestedStoreIds = explode(',', request()->store_id);
                $storeIds = $requestedStoreIds;
                $stores = array_filter($stores, function ($store) use ($requestedStoreIds) {
                    return in_array($store['id'], $requestedStoreIds);
                });
            } else {
                $storeResponse = $this->uwService->getStores([request()->store_id]);
                if (!$storeResponse->failed()) {
                    $storeData = json_decode($storeResponse, true);
                    if (!empty($storeData['data'])) {
                        $storeName = $storeData['data'][0]['name'] ?? 'Single-Store';
                        $storeDescription = '-' . str_replace(' ', '-', $storeName);
                        $storeTitle = 'Event Statistics - ' . $storeName;

                        $storeIds = [request()->store_id];
                        $stores = array_filter($stores, function ($store) use ($storeIds) {
                            return $store['id'] == $storeIds[0];
                        });
                    } else {
                        $storeDescription = '-Single-Store';
                        $storeTitle = 'Event Statistics - Single Store';
                    }
                } else {
                    $storeDescription = '-Single-Store';
                    $storeTitle = 'Event Statistics - Single Store';
                }
            }
        }

        if (empty($stores)) {
            $response = $this->uwService->getEventStatistics($storeIds, $startDate, $endDate);

            if ($response->failed()) {
                throw new Exception('Failed to get event statistics');
            }

            $response = json_decode($response, true);
            $eventStatistics = $response['data'];

            // Format the event statistics to ensure all fields expected by the export sheet are available
            $eventStatistics = $this->formatEventStatisticsForExport($eventStatistics);

            $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
            $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');

            if ($isAllStores || request()->store_id === 'all' || strtolower(request()->store_id) === 'all') {
                $fileName = "event-statistics-All-Stores-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
                Log::info('Using All-Stores filename (empty stores)', ['filename' => $fileName]);
            } else {
                $fileName = "event-statistics{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
                Log::info('Using specific store filename (empty stores)', ['filename' => $fileName, 'storeDescription' => $storeDescription]);
            }

            return Excel::download(new EventStatisticsExport($eventStatistics, $storeTitle), $fileName);
        }

        $allStoreStats = [];
        foreach ($stores as $store) {
            $storeId = $store['id'];
            $storeName = $store['name'];

            $response = $this->uwService->getEventStatistics([$storeId], $startDate, $endDate);

            if (! $response->failed()) {
                $storeStats = json_decode($response, true);
                $storeData = $storeStats['data'];
                $storeData = $this->formatEventStatisticsForExport($storeData);

                $allStoreStats[$storeName] = $storeData;
            }
        }
        if (empty($allStoreStats)) {
            $response = $this->uwService->getEventStatistics($storeIds, $startDate, $endDate);

            if ($response->failed()) {
                throw new Exception('Failed to get event statistics for all stores');
            }

            $response = json_decode($response, true);
            $eventStatistics = $response['data'];

            $eventStatistics = $this->formatEventStatisticsForExport($eventStatistics);

            $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
            $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');

            if ($isAllStores || request()->store_id === 'all' || strtolower(request()->store_id) === 'all') {
                $fileName = "event-statistics-All-Stores-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
                Log::info('Using All-Stores filename (empty allStoreStats)', ['filename' => $fileName]);
            } else {
                $fileName = "event-statistics{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
                Log::info('Using specific store filename (empty allStoreStats)', ['filename' => $fileName, 'storeDescription' => $storeDescription]);
            }

            return Excel::download(new EventStatisticsExport($eventStatistics, $storeTitle), $fileName);
        }

        $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
        $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');

        if ($isAllStores || request()->store_id === 'all' || strtolower(request()->store_id) === 'all') {
            $fileName = "event-statistics-All-Stores-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
            Log::info('Using All-Stores filename', ['filename' => $fileName]);
        } else {
            $fileName = "event-statistics{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";
            Log::info('Using specific store filename', ['filename' => $fileName, 'storeDescription' => $storeDescription]);
        }

        return Excel::download(
            new EventStatisticsExport($allStoreStats, $storeTitle, true),
            $fileName
        );
    }

    /**
     * Format event statistics data to ensure all required fields are available for the export
     *
     * @param  array  $data  The raw event statistics data
     * @return array The formatted data with all required fields
     */
    private function formatEventStatisticsForExport($data)
    {
        // Initialize fudai
        if (! isset($data['fudai']) || ! is_array($data['fudai'])) {
            $data['fudai'] = ['count' => 0, 'sum' => 0, 'user_count' => 0];
        } else {
            $data['fudai']['count'] = $data['fudai']['count'] ?? 0;
            $data['fudai']['sum'] = $data['fudai']['sum'] ?? 0;
            $data['fudai']['user_count'] = $data['fudai']['user_count'] ?? 0;
        }

        // Initialize luckySpin
        if (! isset($data['luckySpin']) || ! is_array($data['luckySpin'])) {
            $data['luckySpin'] = ['count' => 0, 'sum' => 0, 'user_count' => 0];
        } else {
            $data['luckySpin']['count'] = $data['luckySpin']['count'] ?? 0;
            $data['luckySpin']['sum'] = $data['luckySpin']['sum'] ?? 0;
            $data['luckySpin']['user_count'] = $data['luckySpin']['user_count'] ?? 0;
        }

        // Initialize rebate
        if (! isset($data['rebate']) || ! is_array($data['rebate'])) {
            $data['rebate'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['rebate']['count'] = $data['rebate']['count'] ?? 0;
            $data['rebate']['sum'] = $data['rebate']['sum'] ?? 0;
        }

        // Initialize referral
        if (! isset($data['referral']) || ! is_array($data['referral'])) {
            $data['referral'] = ['count' => 0, 'sum' => 0];
        } else {
            $data['referral']['count'] = $data['referral']['count'] ?? 0;
            $data['referral']['sum'] = $data['referral']['sum'] ?? 0;
        }

        return $data;
    }
}
