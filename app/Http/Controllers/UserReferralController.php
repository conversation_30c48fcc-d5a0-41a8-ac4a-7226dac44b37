<?php

namespace App\Http\Controllers;

use App\Exports\UserReferralsExport;
use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class UserReferralController extends Controller
{
    public function __construct(
        protected UWService $uwService
    ) {}

    public function index(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->startOfDay()->toDateTimeString()
            : null;

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->endOfDay()->toDateTimeString()
            : null;

        $perPage = (int) (request()->input('perPage', 10));
        $currentPage = (int) (request()->input('page', 1));

        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeIds = request()->user()->merchant?->store_ids;
            if (request()->store_id && request()->store_id !== 'all') {
                if (strpos(request()->store_id, ',') !== false) {
                    $storeIds = explode(',', request()->store_id);
                } else {
                    $storeIds = [request()->store_id];
                }
            }

            $response = $this->uwService->getUserReferrals(
                $storeIds,
                $startDate,
                $endDate,
                $currentPage,
                $perPage,
                url()->current()
            );

            if ($response->failed()) {
                return Inertia::render('UserReferrals/Index', [
                    'error' => 'Failed to fetch user referrals data',
                ]);
            }

            $responseData = json_decode($response, true);

            // Build query string for pagination URLs
            $queryParams = http_build_query([
                'store_id' => request()->store_id,
                'startDate' => $startDate,
                'endDate' => $endDate,
                'perPage' => $perPage,
            ]);

            $pagination = $responseData['data']['page'];
            $baseUrl = url()->current();

            // Build pagination links array with page numbers
            $lastPage = ceil($pagination['total'] / $perPage);
            $pagination['links'] = [];

            // Add first pages
            for ($i = 1; $i <= min(3, $lastPage); $i++) {
                $pagination['links'][] = [
                    'url' => $baseUrl.'?'.$queryParams.'&page='.$i,
                    'label' => (string) $i,
                    'active' => $i === $currentPage,
                ];
            }

            // Add ellipsis if needed
            if ($lastPage > 3) {
                $pagination['links'][] = [
                    'url' => null,
                    'label' => '...',
                    'active' => false,
                ];
            }

            // Add last pages
            for ($i = max($lastPage - 2, 4); $i <= $lastPage; $i++) {
                $pagination['links'][] = [
                    'url' => $baseUrl.'?'.$queryParams.'&page='.$i,
                    'label' => (string) $i,
                    'active' => $i === $currentPage,
                ];
            }

            $pagination['path'] = $baseUrl;
            $pagination['first_page_url'] = $baseUrl.'?'.$queryParams.'&page=1';
            $pagination['last_page_url'] = $baseUrl.'?'.$queryParams.'&page='.$lastPage;
            $pagination['next_page_url'] = $currentPage < $lastPage ? $baseUrl.'?'.$queryParams.'&page='.($currentPage + 1) : null;
            $pagination['prev_page_url'] = $currentPage > 1 ? $baseUrl.'?'.$queryParams.'&page='.($currentPage - 1) : null;
            $pagination['current_page'] = $currentPage;
            $pagination['last_page'] = $lastPage;

            return Inertia::render('UserReferrals/Index', [
                'filters' => [
                    'search' => request()->search,
                    'perPage' => (string) $perPage,
                    'store_id' => request()->store_id,
                ],
                'startDate' => $startDate,
                'endDate' => $endDate,
                'data' => [
                    'items' => $responseData['data']['items'] ?? [],
                ],
                'page' => $pagination,
            ]);
        }

        return Inertia::render('UserReferrals/Index', [
            'filters' => [
                'search' => request()->search,
                'perPage' => (string) $perPage,
                'store_id' => request()->store_id,
            ],
            'startDate' => $startDate,
            'endDate' => $endDate,
            'data' => [
                'items' => [],
            ],
            'page' => null,
        ]);
    }

    public function export()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->startOfDay()->toDateTimeString()
            : null;

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->endOfDay()->toDateTimeString()
            : null;

        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            $storeIds = request()->user()->merchant?->store_ids;
            if (request()->store_id && request()->store_id !== 'all') {
                // Check if it's a comma-separated list of store IDs
                if (strpos(request()->store_id, ',') !== false) {
                    $storeIds = explode(',', request()->store_id);
                } else {
                    $storeIds = [request()->store_id];
                }
            }

            $response = $this->uwService->getUserReferrals(
                $storeIds,
                $startDate,
                $endDate,
                1,  // page
                10000, // large number to get all records
                url()->current()
            );

            if ($response->failed()) {
                throw new Exception('Failed to fetch user referrals data');
            }

            $responseData = json_decode($response, true);
            $data = $responseData['data']['items'] ?? [];

            $startDateFormatted = $startDate ? Carbon::parse($startDate)->format('Y-m-d') : 'all';
            $endDateFormatted = $endDate ? Carbon::parse($endDate)->format('Y-m-d') : 'all';

            // Add store information to the filename
            $storeInfo = '';
            if (request()->store_id && request()->store_id !== 'all') {
                if (strpos(request()->store_id, ',') !== false) {
                    $storeInfo = '-multiple-stores';
                } else {
                    $storeResponse = $this->uwService->getStores([request()->store_id]);
                    if (!$storeResponse->failed()) {
                        $storeData = json_decode($storeResponse, true);
                        if (!empty($storeData['data'])) {
                            $storeName = $storeData['data'][0]['name'] ?? 'single-store';
                            $storeInfo = '-' . str_replace(' ', '-', $storeName);
                        }
                    }
                }
            }

            $fileName = "user-referrals{$storeInfo}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";

            return Excel::download(
                new UserReferralsExport($data),
                $fileName
            );
        }

        return response()->json(['error' => 'Unauthorized'], 403);
    }
}
