<?php

namespace App\Http\Controllers;

use App\Exports\DashboardStatisticsExport;
use App\Services\ClickHouse\WWJService;
use App\Services\UWService;
use Carbon\Carbon;
use Exception;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class DashboardController extends Controller
{
    public function __construct(
        protected UWService $uwService,
        protected WWJService $wwjService
    ) {}

    public function index(): Response
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $data = [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'store_id' => request()->store_id,
        ];

        if (request()->user()->hasRole('merchant') || request()->user()->hasRole('admin')) {
            // Only fetch data if there's an explicit search (store_id or dates in request)
            if (request()->has('store_id') || request()->has('startDate')) {
                // Handle multiple store IDs (comma-separated)
                $storeIds = request()->user()->merchant?->store_ids;

                if (request()->store_id && request()->store_id !== 'all') {
                    if (strpos(request()->store_id, ',') !== false) {
                        $storeIds = explode(',', request()->store_id);
                    } else {
                        $storeIds = [request()->store_id];
                    }
                }

                $response = $this->uwService->getStatistics($storeIds, $startDate, $endDate);

                if ($response->failed()) {
                    throw new Exception('Failed to get statistics');
                }

                $response = json_decode($response, true);
                $statistics = $response['data'];

                // Turnover
                $turnover = $this->wwjService->getTotalTurnover([
                    'start' => $startDate,
                    'end' => $endDate,
                    'store_ids' => $storeIds,
                ]);

                $totalWinLoss = $this->wwjService->getTotalWinLoss([
                    'start' => $startDate,
                    'end' => $endDate,
                    'store_ids' => $storeIds,
                ]);

                $data['statistics'] = [
                    'totalRegistrations' => $statistics['totalRegistrations'] ?? 0,
                    'totalActiveUsers' => $statistics['totalActiveUsers'] ?? 0,
                    'totalDeposits' => [
                        'count' => $statistics['totalDeposits']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalDeposits']['sum'], 2, '.', ','),
                    ],
                    'totalWithdrawals' => [
                        'count' => $statistics['totalWithdrawals']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalWithdrawals']['sum'], 2, '.', ','),
                    ],
                    'totalNettDeposits' => number_format((float) $statistics['totalNettDeposits'], 2, '.', ','),
                    'totalFTD' => [
                        'count' => $statistics['totalFTD']['count'] ?? 0,
                        'sum' => number_format((float) $statistics['totalFTD']['sum'], 2, '.', ','),
                    ],
                    'totalTurnover' => [
                        'count' => $turnover['total_bet_count'] ?? 0,
                        'sum' => number_format((float) $turnover['total_bet'], 2, '.', ','),
                    ],
                    'transferDetails' => [
                        'in' => [
                            'count' => $statistics['transferDetails']['transfer_in']['count'] ?? 0,
                            'sum' => number_format((float) $statistics['transferDetails']['transfer_in']['sum'], 2, '.', ','),
                        ],
                        'out' => [
                            'count' => $statistics['transferDetails']['transfer_out']['count'] ?? 0,
                            'sum' => number_format((float) $statistics['transferDetails']['transfer_out']['sum'], 2, '.', ','),
                        ],
                    ],
                    'totalTransfer' => [
                        'sum' => number_format((float) $statistics['totalTransfer']['sum'], 2, '.', ','),
                    ],
                    'totalWinLoss' => [
                        'win' => number_format((float) $totalWinLoss['total_wins'], 2, '.', ','),
                        'loss' => number_format((float) $totalWinLoss['total_losses'], 2, '.', ','),
                    ],
                    'walletToCard' => [
                        'count' => $statistics['walletToCard']['count'] ?? 0,
                        'sum' => number_format((float) ($statistics['walletToCard']['sum'] ?? 0), 2, '.', ','),
                    ],
                    'cardToWallet' => [
                        'count' => $statistics['cardToWallet']['count'] ?? 0,
                        'sum' => number_format((float) ($statistics['cardToWallet']['sum'] ?? 0), 2, '.', ','),
                    ],
                    'p2pTransferIn' => [
                        'count' => $statistics['p2pTransferIn']['count'] ?? 0,
                        'sum' => number_format((float) ($statistics['p2pTransferIn']['sum'] ?? 0), 2, '.', ','),
                    ],
                ];

                // Only add promotion and adjustment data for admin users
                if (request()->user()->hasRole('admin')) {
                    $data['statistics']['totalPromotion'] = [
                        'in' => number_format((float) ($statistics['totalPromotion']['in'] ?? 0), 2, '.', ','),
                        'out' => number_format((float) ($statistics['totalPromotion']['out'] ?? 0), 2, '.', ','),
                        'burnt' => number_format((float) ($statistics['totalPromotion']['burnt'] ?? 0), 2, '.', ','),
                        'count' => $statistics['totalPromotion']['count'] ?? 0,
                    ];

                    $data['statistics']['totalAdjustment'] = [
                        'in' => [
                            'count' => $statistics['totalAdjustment']['in']['count'] ?? 0,
                            'sum' => number_format((float) ($statistics['totalAdjustment']['in']['sum'] ?? 0), 2, '.', ','),
                        ],
                        'out' => [
                            'count' => $statistics['totalAdjustment']['out']['count'] ?? 0,
                            'sum' => number_format((float) ($statistics['totalAdjustment']['out']['sum'] ?? 0), 2, '.', ','),
                        ],
                        'nett' => number_format((float) ($statistics['totalAdjustment']['nett'] ?? 0), 2, '.', ','),
                    ];

                    $data['statistics']['totalAdvanceCredit'] = [
                        'count' => $statistics['totalAdvanceCredit']['count'] ?? 0,
                        'sum' => number_format((float) ($statistics['totalAdvanceCredit']['sum'] ?? 0), 2, '.', ','),
                    ];
                }
            } else {
                // Return empty statistics when no explicit search
                $data['statistics'] = [
                    'totalRegistrations' => 0,
                    'totalActiveUsers' => 0,
                    'totalDeposits' => ['count' => 0, 'sum' => '0.00'],
                    'totalWithdrawals' => ['count' => 0, 'sum' => '0.00'],
                    'totalNettDeposits' => '0.00',
                    'totalFTD' => ['count' => 0, 'sum' => '0.00'],
                    'totalTurnover' => ['count' => 0, 'sum' => '0.00'],
                    'transferDetails' => [
                        'in' => ['count' => 0, 'sum' => '0.00'],
                        'out' => ['count' => 0, 'sum' => '0.00'],
                    ],
                    'totalTransfer' => ['sum' => '0.00'],
                    'totalWinLoss' => ['win' => '0.00', 'loss' => '0.00'],
                    'walletToCard' => ['count' => 0, 'sum' => '0.00'],
                    'cardToWallet' => ['count' => 0, 'sum' => '0.00'],
                    'p2pTransferIn' => ['count' => 0, 'sum' => '0.00'],
                ];

                // Only add empty promotion and adjustment data for admin users
                if (request()->user()->hasRole('admin')) {
                    $data['statistics']['totalPromotion'] = [
                        'in' => '0.00',
                        'out' => '0.00',
                        'burnt' => '0.00',
                        'count' => 0,
                    ];

                    $data['statistics']['totalAdjustment'] = [
                        'in' => ['count' => 0, 'sum' => '0.00'],
                        'out' => ['count' => 0, 'sum' => '0.00'],
                        'nett' => '0.00',
                    ];

                    $data['statistics']['totalAdvanceCredit'] = [
                        'count' => 0,
                        'sum' => '0.00',
                    ];
                }
            }
        }

        return Inertia::render('Dashboard/Index', $data);
    }

    public function export()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        // Get store IDs to query
        $storeIds = request()->user()->merchant?->store_ids;

        if (request()->store_id && request()->store_id !== 'all') {
            if (strpos(request()->store_id, ',') !== false) {
                $storeIds = explode(',', request()->store_id);
            } else {
                $storeIds = [request()->store_id];
            }
        }

        $response = $this->uwService->getBulkStatistics($storeIds, $startDate, $endDate);
        $allStoreStats = [];

        if (! $response->failed()) {
            $responseData = json_decode($response, true);
            $allStoreData = $responseData['data'] ?? [];

            $turnoverAndWinLossData = $this->wwjService->getBulkTurnoverAndWinLoss([
                'start' => $startDate,
                'end' => $endDate,
                'store_ids' => $storeIds,
            ]);

            foreach ($allStoreData as $storeStat) {
                $merchantName = $storeStat['merchantName'];
                $storeId = $storeStat['storeId'];

                $turnoverData = $turnoverAndWinLossData[$storeId] ?? [
                    'total_bet_count' => 0,
                    'total_bet' => 0,
                    'total_wins' => 0,
                    'total_losses' => 0,
                ];

                $allStoreStats[$merchantName] = [
                    'totalRegistrations' => $storeStat['totalRegistrations'],
                    'totalActiveUsers' => $storeStat['totalActiveUsers'],
                    'totalDeposits' => [
                        'count' => $storeStat['totalDepositsCount'],
                        'sum' => $storeStat['totalDeposits'],
                    ],
                    'totalWithdrawals' => [
                        'count' => $storeStat['totalWithdrawalsCount'],
                        'sum' => $storeStat['totalWithdrawals'],
                    ],
                    'totalNettDeposits' => $storeStat['totalNettDeposits'],
                    'totalFTD' => [
                        'count' => $storeStat['totalFTDCount'],
                        'sum' => $storeStat['totalFTD'],
                    ],
                    'transferDetails' => [
                        'transfer_in' => [
                            'count' => $storeStat['transferInCount'],
                            'sum' => $storeStat['transferInSum'],
                        ],
                        'transfer_out' => [
                            'count' => $storeStat['transferOutCount'],
                            'sum' => $storeStat['transferOutSum'],
                        ],
                    ],
                    'totalTransfer' => [
                        'sum' => $storeStat['totalTransfer'],
                    ],
                    'totalTurnover' => [
                        'count' => $turnoverData['total_bet_count'],
                        'sum' => $turnoverData['total_bet'],
                    ],
                    'totalWinLoss' => [
                        'win' => $turnoverData['total_wins'],
                        'loss' => $turnoverData['total_losses'],
                    ],
                    'retentionRate' => $storeStat['retentionRate'],
                    'walletToCard' => [
                        'count' => $storeStat['walletToCardCount'],
                        'sum' => $storeStat['walletToCardSum'],
                    ],
                    'cardToWallet' => [
                        'count' => $storeStat['cardToWalletCount'],
                        'sum' => $storeStat['cardToWalletSum'],
                    ],
                    'p2pTransferIn' => [
                        'count' => $storeStat['p2pTransferInCount'],
                        'sum' => $storeStat['p2pTransferInSum'],
                    ],
                    'totalSTD' => [
                        'count' => $storeStat['totalSTDCount'] ?? 0,
                        'sum' => $storeStat['totalSTD'] ?? 0,
                        'rate' => $storeStat['totalSTDRate'] ?? 0,
                    ],
                    'totalTTD' => [
                        'count' => $storeStat['totalTTDCount'] ?? 0,
                        'sum' => $storeStat['totalTTD'] ?? 0,
                        'rate' => $storeStat['totalTTDRate'] ?? 0,
                    ],
                    'promotion' => [
                        'count' => $storeStat['promotionCount'] ?? 0,
                        'in' => $storeStat['promotionIn'] ?? 0,
                        'out' => $storeStat['promotionOut'] ?? 0,
                        'burnt' => $storeStat['promotionBurnt'] ?? 0,
                    ],
                    'adjustment' => [
                        'in' => [
                            'count' => $storeStat['adjustmentInCount'] ?? 0,
                            'sum' => $storeStat['adjustmentInSum'] ?? 0,
                        ],
                        'out' => [
                            'count' => $storeStat['adjustmentOutCount'] ?? 0,
                            'sum' => $storeStat['adjustmentOutSum'] ?? 0,
                        ],
                        'nett' => $storeStat['adjustmentNett'] ?? 0,
                    ],
                    'advanceCredit' => [
                        'count' => $storeStat['advanceCreditCount'] ?? 0,
                        'sum' => $storeStat['advanceCreditSum'] ?? 0,
                    ],
                ];
            }
        }

        $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
        $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');

        // Determine if we're exporting for specific stores or all stores
        $storeDescription = '-All-Stores';
        $storeTitle = 'Dashboard Statistics';

        if (request()->store_id && request()->store_id !== 'all') {
            if (strpos(request()->store_id, ',') !== false) {
                $storeDescription = '-Multiple-Stores';
                $storeTitle = 'Dashboard Statistics - Multiple Stores';
            } else {
                $storeResponse = $this->uwService->getStores([request()->store_id]);
                if (!$storeResponse->failed()) {
                    $storeData = json_decode($storeResponse, true);
                    if (!empty($storeData['data'])) {
                        $storeName = $storeData['data'][0]['name'] ?? 'Single-Store';
                        $storeDescription = '-' . str_replace(' ', '-', $storeName);
                        $storeTitle = 'Dashboard Statistics - ' . $storeName;
                    } else {
                        $storeDescription = '-Single-Store';
                        $storeTitle = 'Dashboard Statistics - Single Store';
                    }
                } else {
                    $storeDescription = '-Single-Store';
                    $storeTitle = 'Dashboard Statistics - Single Store';
                }
            }
        }

        $fileName = "dashboard-statistics{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";

        return Excel::download(
            new DashboardStatisticsExport($allStoreStats, $storeTitle, true),
            $fileName
        );
    }

    public function reportDetails()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeId = request()->store_id;
        $storeResponse = $this->uwService->getStores(request()->user()->merchant?->store_ids);
        $stores = [];
        $statistics = [];

        if (! $storeResponse->failed()) {
            $storesData = json_decode($storeResponse, true);
            $stores = $storesData['data'] ?? [];

            if (request()->has('store_id') || request()->has('startDate')) {
                $storeIds = request()->user()->merchant?->store_ids;

                if ($storeId && $storeId !== 'all') {
                    if (strpos($storeId, ',') !== false) {
                        $storeIds = explode(',', $storeId);

                        // Filter stores to only include the selected ones
                        $stores = array_filter($stores, function ($store) use ($storeIds) {
                            return in_array($store['id'], $storeIds);
                        });
                    } else {
                        $storeIds = [$storeId];

                        // Filter stores to only include the selected one
                        $stores = array_filter($stores, function ($store) use ($storeId) {
                            return $store['id'] == $storeId;
                        });
                    }
                }

                $response = $this->uwService->getBulkStatistics($storeIds, $startDate, $endDate);

                if (! $response->failed()) {
                    $responseData = json_decode($response, true);
                    $statistics = $responseData['data'] ?? [];

                    $turnoverAndWinLossData = $this->wwjService->getBulkTurnoverAndWinLoss([
                        'start' => $startDate,
                        'end' => $endDate,
                        'store_ids' => $storeIds,
                    ]);

                    foreach ($statistics as $key => $stat) {
                        $storeId = $stat['storeId'];
                        if (isset($turnoverAndWinLossData[$storeId])) {
                            $statistics[$key]['totalTurnoverCount'] = $turnoverAndWinLossData[$storeId]['total_bet_count'];
                            $statistics[$key]['totalTurnover'] = $turnoverAndWinLossData[$storeId]['total_bet'];
                            $statistics[$key]['totalWin'] = $turnoverAndWinLossData[$storeId]['total_wins'];
                            $statistics[$key]['totalLoss'] = $turnoverAndWinLossData[$storeId]['total_losses'];
                        } else {
                            $statistics[$key]['totalTurnoverCount'] = 0;
                            $statistics[$key]['totalTurnover'] = 0;
                            $statistics[$key]['totalWin'] = 0;
                            $statistics[$key]['totalLoss'] = 0;
                        }
                    }
                }
            }
        }

        return Inertia::render('Reports/ReportDetails', [
            'startDate' => $startDate,
            'endDate' => $endDate,
            'statistics' => $statistics,
            'filters' => request()->only(['store_id']),
            'stores' => $stores,
        ]);
    }

    public function exportReportDetails()
    {
        $startDate = request()->startDate
            ? Carbon::parse(request()->startDate)->toDateTimeString()
            : Carbon::now()->startOfDay()->toDateTimeString();

        $endDate = request()->endDate
            ? Carbon::parse(request()->endDate)->format('Y-m-d H:i:59')
            : Carbon::now()->endOfDay()->format('Y-m-d H:i:59');

        $storeId = request()->store_id;

        // Get store IDs to query
        $storeIds = request()->user()->merchant?->store_ids;

        if ($storeId && $storeId !== 'all') {
            if (strpos($storeId, ',') !== false) {
                $storeIds = explode(',', $storeId);
            } else {
                $storeIds = [$storeId];
            }
        }

        $response = $this->uwService->getBulkStatistics($storeIds, $startDate, $endDate);
        $allStoreStats = [];

        if (! $response->failed()) {
            $responseData = json_decode($response, true);
            $allStoreData = $responseData['data'] ?? [];

            $turnoverAndWinLossData = $this->wwjService->getBulkTurnoverAndWinLoss([
                'start' => $startDate,
                'end' => $endDate,
                'store_ids' => $storeIds,
            ]);

            foreach ($allStoreData as $storeStat) {
                $merchantName = $storeStat['merchantName'];
                $storeId = $storeStat['storeId'];

                $turnoverData = $turnoverAndWinLossData[$storeId] ?? [
                    'total_bet_count' => 0,
                    'total_bet' => 0,
                    'total_wins' => 0,
                    'total_losses' => 0,
                ];

                $allStoreStats[$merchantName] = [
                    'totalRegistrations' => $storeStat['totalRegistrations'],
                    'totalActiveUsers' => $storeStat['totalActiveUsers'],
                    'totalDeposits' => [
                        'count' => $storeStat['totalDepositsCount'],
                        'sum' => $storeStat['totalDeposits'],
                    ],
                    'totalWithdrawals' => [
                        'count' => $storeStat['totalWithdrawalsCount'],
                        'sum' => $storeStat['totalWithdrawals'],
                    ],
                    'totalNettDeposits' => $storeStat['totalNettDeposits'],
                    'totalFTD' => [
                        'count' => $storeStat['totalFTDCount'],
                        'sum' => $storeStat['totalFTD'],
                    ],
                    'transferDetails' => [
                        'transfer_in' => [
                            'count' => $storeStat['transferInCount'],
                            'sum' => $storeStat['transferInSum'],
                        ],
                        'transfer_out' => [
                            'count' => $storeStat['transferOutCount'],
                            'sum' => $storeStat['transferOutSum'],
                        ],
                    ],
                    'totalTransfer' => [
                        'sum' => $storeStat['totalTransfer'],
                    ],
                    'totalTurnover' => [
                        'count' => $turnoverData['total_bet_count'],
                        'sum' => $turnoverData['total_bet'],
                    ],
                    'totalWinLoss' => [
                        'win' => $turnoverData['total_wins'],
                        'loss' => $turnoverData['total_losses'],
                    ],
                    'retentionRate' => $storeStat['retentionRate'],
                    'walletToCard' => [
                        'count' => $storeStat['walletToCardCount'],
                        'sum' => $storeStat['walletToCardSum'],
                    ],
                    'cardToWallet' => [
                        'count' => $storeStat['cardToWalletCount'],
                        'sum' => $storeStat['cardToWalletSum'],
                    ],
                    'p2pTransferIn' => [
                        'count' => $storeStat['p2pTransferInCount'],
                        'sum' => $storeStat['p2pTransferInSum'],
                    ],
                    'totalSTD' => [
                        'count' => $storeStat['totalSTDCount'] ?? 0,
                        'sum' => $storeStat['totalSTD'] ?? 0,
                        'rate' => $storeStat['totalSTDRate'] ?? 0,
                    ],
                    'totalTTD' => [
                        'count' => $storeStat['totalTTDCount'] ?? 0,
                        'sum' => $storeStat['totalTTD'] ?? 0,
                        'rate' => $storeStat['totalTTDRate'] ?? 0,
                    ],
                    'promotion' => [
                        'count' => $storeStat['promotionCount'] ?? 0,
                        'in' => $storeStat['promotionIn'] ?? 0,
                        'out' => $storeStat['promotionOut'] ?? 0,
                        'burnt' => $storeStat['promotionBurnt'] ?? 0,
                    ],
                    'adjustment' => [
                        'in' => [
                            'count' => $storeStat['adjustmentInCount'] ?? 0,
                            'sum' => $storeStat['adjustmentInSum'] ?? 0,
                        ],
                        'out' => [
                            'count' => $storeStat['adjustmentOutCount'] ?? 0,
                            'sum' => $storeStat['adjustmentOutSum'] ?? 0,
                        ],
                        'nett' => $storeStat['adjustmentNett'] ?? 0,
                    ],
                    'advanceCredit' => [
                        'count' => $storeStat['advanceCreditCount'] ?? 0,
                        'sum' => $storeStat['advanceCreditSum'] ?? 0,
                    ],
                ];
            }
        }

        $startDateFormatted = Carbon::parse($startDate)->format('Y-m-d');
        $endDateFormatted = Carbon::parse($endDate)->format('Y-m-d');

        // Determine if we're exporting for specific stores or all stores
        $storeDescription = '-All-Stores';
        $storeTitle = 'Report Details';

        if (request()->store_id && request()->store_id !== 'all') {
            if (strpos(request()->store_id, ',') !== false) {
                $storeDescription = '-Multiple-Stores';
                $storeTitle = 'Report Details - Multiple Stores';
            } else {
                $storeResponse = $this->uwService->getStores([request()->store_id]);
                if (!$storeResponse->failed()) {
                    $storeData = json_decode($storeResponse, true);
                    if (!empty($storeData['data'])) {
                        $storeName = $storeData['data'][0]['name'] ?? 'Single-Store';
                        $storeDescription = '-' . str_replace(' ', '-', $storeName);
                        $storeTitle = 'Report Details - ' . $storeName;
                    } else {
                        $storeDescription = '-Single-Store';
                        $storeTitle = 'Report Details - Single Store';
                    }
                } else {
                    $storeDescription = '-Single-Store';
                    $storeTitle = 'Report Details - Single Store';
                }
            }
        }

        $fileName = "report-details{$storeDescription}-{$startDateFormatted}-to-{$endDateFormatted}.xlsx";

        return Excel::download(
            new DashboardStatisticsExport($allStoreStats, $storeTitle, true),
            $fileName
        );
    }
}
