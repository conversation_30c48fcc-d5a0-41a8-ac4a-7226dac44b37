<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class UsersController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Users/Index', [
            'filters' => request()->only('search', 'trashed', 'perPage'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'users' => User::query()
                ->orderBy($sort, $direction)
                ->filter(request()->only('search', 'trashed'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($user) => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'roles' => $user->roles->pluck('name')->map(fn ($role) => ucfirst($role)),
                    'photo' => $user->photo,
                    'is_active' => $user->is_active,
                    'deleted_at' => $user->deleted_at,
                    'created_at' => $user->created_at->format('Y-m-d H:i:s'),
                ]),
        ]);
    }

    public function all()
    {
        $agents = User::role('agent')
            ->where('is_active', true)
            ->get();

        return response()->json($agents);
    }

    public function create(): Response
    {
        return Inertia::render('Users/Create');
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'name' => ['required'],
            'email' => ['required', 'email', Rule::unique('users')],
            'password' => ['nullable'],
            'photo' => ['nullable', 'image'],
            'is_active' => ['required', Rule::in([true, false])],
            'role' => ['required', 'exists:roles,name'],
            'merchant' => [
                Rule::requiredIf(function () {
                    return in_array(Request::get('role'), ['merchant', 'admin']);
                }),
                'nullable',
                'exists:merchants,id',
            ],
        ]);

        $password = Request::get('password') ?: 'password';

        $user = new User;
        $user->fill([
            'name' => Request::get('name'),
            'email' => Request::get('email'),
            'password' => bcrypt($password),
            'photo_path' => Request::file('photo') ? Request::file('photo')->store('users') : null,
            'is_active' => Request::get('is_active'),
            'merchant_id' => Request::get('merchant'),
        ]);
        $user->save();

        $user->assignRole(Request::get('role'));

        return Redirect::route('users')->with('success', 'User created.');
    }

    public function edit(User $user): Response
    {
        return Inertia::render('Users/Edit', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->roles->first()->name ?? null,
                'photo' => $user->photo,
                'is_active' => $user->is_active,
                'deleted_at' => $user->deleted_at,
                'code' => $user->merchant?->code,
                'merchant_id' => $user->merchant_id, // Ensure merchant_id is passed to the frontend
            ],
        ]);
    }

    public function issueToken(User $user)
    {
        $user->tokens()->delete();
        $token = $user->createToken('default token')->plainTextToken;

        return $token;
    }

    public function update(User $user): RedirectResponse
    {
        if (App::environment('demo') && $user->isDemoUser()) {
            return Redirect::back()->with('error', 'Updating the demo user is not allowed.');
        }

        Request::validate([
            'name' => ['required'],
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'password' => ['nullable'],
            'role' => ['required', 'exists:roles,name'],
            'photo' => ['nullable', 'image'],
            'is_active' => ['required', Rule::in([true, false])],
            'merchant' => [
                Rule::requiredIf(function () {
                    return in_array(Request::get('role'), ['merchant', 'admin']);
                }),
                'nullable',
                'exists:merchants,id',
            ],
        ]);

        $originalStatus = $user->is_active;
        $user->fill(Request::only('name', 'email', 'is_active'));
        
        // Set merchant_id based on the role
        if (in_array(Request::get('role'), ['merchant', 'admin'])) {
            $user->merchant_id = Request::get('merchant');
        } else {
            $user->merchant_id = null;
        }

        if (Request::file('photo')) {
            if ($user->photo_path) {
                Storage::delete($user->photo_path);
            }
            $user->fill(['photo_path' => Request::file('photo')->store('users')]);
        }

        if (Request::get('password')) {
            $user->fill(['password' => bcrypt(Request::get('password'))]);
        }

        $user->save();

        if (Request::get('role') !== $user->roles->first()?->name) {
            $user->syncRoles([Request::get('role')]);
        }

        if ($originalStatus === true && $user->is_active === false) {
            $this->logoutUserSessions($user);
        }

        return Redirect::back()->with('success', 'User updated successfully.');
    }

    public function destroy(User $user): RedirectResponse
    {
        if (App::environment('demo') && $user->isDemoUser()) {
            return Redirect::back()->with('error', 'Deleting the demo user is not allowed.');
        }

        if ($user->photo_path) {
            Storage::delete($user->photo_path);
        }

        $user->delete();

        return Redirect::route('users')->with('success', 'User deleted successfully.');
    }

    protected function logoutUserSessions(User $user): void
    {
        DB::table('sessions')->where('user_id', $user->id)->delete();

        $user->tokens()->delete();
    }
}
