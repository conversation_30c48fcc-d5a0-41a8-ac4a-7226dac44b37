<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesController extends Controller
{
    public function index(): Response
    {
        $filters = request()->only('search', 'trashed');
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        $roles = Role::with('permissions')
            ->orderBy($sort, $direction)
            ->when($filters['search'] ?? null, fn ($query, $search) => $query->where('name', 'like', '%'.$search.'%'))
            ->when($filters['trashed'] ?? null, function ($query, $trashed) {
                if ($trashed === 'with') {
                    $query->withTrashed();
                } elseif ($trashed === 'only') {
                    $query->onlyTrashed();
                }
            })
            ->paginate($perPage)
            ->withQueryString();

        return Inertia::render('Roles/Index', [
            'filters' => $filters,
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'roles' => $roles->through(fn ($role) => [
                'id' => $role->id,
                'name' => $role->name,
                'permissions' => $role->permissions->pluck('name'),
                'created_at' => $role->created_at->format('Y-m-d H:i:s'),
            ]),
        ]);
    }

    public function all()
    {
        return response()->json(Role::all());
    }

    public function create(): Response
    {
        return Inertia::render('Roles/Create', [
            'permissions' => Permission::all()->pluck('name'),
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles'],
            'permissions' => ['nullable', 'array'],
            'permissions.*' => ['string', 'exists:permissions,name'],
        ]);

        $role = new Role;
        $role->fill(['name' => $validated['name']]);
        $role->save();

        if (! empty($validated['permissions'])) {
            $role->syncPermissions($validated['permissions']);
        }

        return Redirect::route('roles')->with('success', 'Role created successfully.');
    }

    public function edit(Role $role): Response
    {
        return Inertia::render('Roles/Edit', [
            'role' => [
                'id' => $role->id,
                'name' => $role->name,
                'permissions' => $role->permissions->pluck('name'),
            ],
            'permissions' => Permission::all()->pluck('name'),
        ]);
    }

    public function update(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles,name,'.$role->id],
            'permissions' => ['nullable', 'array'],
            'permissions.*' => ['string', 'exists:permissions,name'],
        ]);

        $role->fill(['name' => $validated['name']]);
        $role->save();

        $role->syncPermissions($validated['permissions'] ?? []);

        return Redirect::route('roles')->with('success', 'Role updated successfully.');
    }

    public function destroy(Role $role)
    {
        if ($role->users()->exists()) {
            return Redirect::back()->with('error', 'Role is assigned to users and cannot be deleted.');
        }

        $role->delete();

        return Redirect::route('roles')->with('success', 'Role deleted successfully.');
    }
}
