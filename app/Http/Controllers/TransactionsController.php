<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;

class TransactionsController extends Controller
{
    public function store(): RedirectResponse
    {
        // Validate the incoming request
        Request::validate([
            'amount' => ['required', 'numeric', 'min:0'],
            'currency_id' => ['required', 'exists:currencies,id'],
            'transaction_type_id' => ['required', 'exists:transaction_types,id'],
            'currency_order_id' => ['required', 'exists:currency_orders,id'],
            'bank_id' => ['required', 'exists:banks,id'],
        ]);

        $transaction = new Transaction;

        $transaction->fill(Request::only('amount'));

        $transaction->currency()->associate(Request::get('currency_id'));
        $transaction->transactionType()->associate(Request::get('transaction_type_id'));

        if (Request::filled('currency_order_id')) {
            $transaction->currencyOrder()->associate(Request::get('currency_order_id'));
        }

        if (Request::filled('bank_id')) {
            $transaction->bank()->associate(Request::get('bank_id'));
        }

        $transaction->save();

        return Redirect::back()->with('success', 'Transaction created successfully.');
    }
}
