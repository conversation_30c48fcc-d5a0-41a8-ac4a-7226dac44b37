<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class WebBlockIPMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // $ips = [];
        // if (env('APP_ENV') == "local") {
        //     $ips = ['127.0.0.1'];
        // } else if (env('APP_ENV') == "production" || env('APP_ENV') == "staging") {
        //     $ips = ['**************'];
        // }

        // if (!in_array($request->ip(), $ips)) {
        //     abort(403, 'Access Denied. Please contact administrator.');
        // }

        if (! env('APP_ENV') == 'local') {
            $ipExist = Cache::get('ipExist', function () {
                return WhitelistIP::where('ip', $this->getIp())->first();
            });

            if (! $ipExist) {
                abort(403, 'Access Denied. Please contact administrator.');
            }
        }

        return $next($request);
    }

    public function getIp()
    {
        foreach (['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'] as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return request()->ip(); // it will return server ip when no client ip found
    }
}
