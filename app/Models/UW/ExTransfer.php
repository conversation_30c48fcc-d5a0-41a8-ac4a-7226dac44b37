<?php

namespace App\Models\UW;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExTransfer extends Model
{
    protected $connection = 'uw';

    protected $table = 'ex_transfer';

    public static $type = [
        'in' => 1,
        'out' => 2,
    ];

    public static $status = [
        'processing' => 0,
        'confirmed' => 1,
        'failed' => 2,
        'refunded' => 3,
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships */
    /* -------------------------------------------------------------------------- */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeIn($query)
    {
        return $query->where('type', self::$type['in']);
    }

    public function scopeOut($query)
    {
        return $query->where('type', self::$type['out']);
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', self::$status['confirmed']);
    }
}
