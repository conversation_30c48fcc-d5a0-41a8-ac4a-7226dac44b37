<?php

namespace App\Models\UW;

use App\Models\Merchant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Model
{
    protected $connection = 'uw';

    public static $userType = [
        'internal-account' => 0,
        'user-account' => 1,
        'shop-account' => 2,
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships */
    /* -------------------------------------------------------------------------- */
    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class);
    }

    public function deposits(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Deposit::class);
    }

    public function withdrawals(): Has<PERSON>any
    {
        return $this->hasMany(Withdrawal::class);
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeCustomer($query)
    {
        return $query->where('user_type', self::$userType['user-account']);
    }

    public function scopeActive($query)
    {
        return $query->where('activated', true)
            ->where('disabled', false)
            ->where('suspended', false);
    }
}
