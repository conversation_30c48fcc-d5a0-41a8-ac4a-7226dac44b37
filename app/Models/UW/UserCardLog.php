<?php

namespace App\Models\UW;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserCardLog extends Model
{
    protected $connection = 'uw';

    protected $table = 'user_card_logs';

    public static $operationType = [
        'deposit' => 1,
        'withdrawal' => 2,
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships */
    /* -------------------------------------------------------------------------- */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeDeposit($query)
    {
        return $query->where('operation_type', self::$operationType['deposit']);
    }

    public function scopeWithdrawal($query)
    {
        return $query->where('operation_type', self::$operationType['withdrawal']);
    }
}
