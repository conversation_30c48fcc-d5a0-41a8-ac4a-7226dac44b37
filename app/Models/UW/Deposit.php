<?php

namespace App\Models\UW;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Deposit extends Model
{
    protected $connection = 'uw';

    protected $table = 'deposit';

    public static $status = [
        'pending' => 0,
        'approved' => 1,
        'rejected' => 2,
        'cancelled' => 3,
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships */
    /* -------------------------------------------------------------------------- */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeApproved($query)
    {
        return $query->where('status', self::$status['approved']);
    }
}
