<?php

namespace App\Models\UW;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Withdrawal extends Model
{
    protected $connection = 'uw';

    protected $table = 'withdrawal';

    public static $status = [
        'waiting-approval' => 0,
        'approved' => 1,
        'pending' => 2,
        'rejected' => 3,
        'cancel' => 9,
    ];

    /* -------------------------------------------------------------------------- */
    /*                                Relationships */
    /* -------------------------------------------------------------------------- */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeApproved($query)
    {
        return $query->where('status', self::$status['approved']);
    }
}
