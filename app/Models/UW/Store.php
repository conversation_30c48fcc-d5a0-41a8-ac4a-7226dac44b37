<?php

namespace App\Models\UW;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class Store extends Model
{
    protected $connection = 'uw';

    protected $table = 'store';

    protected $casts = [
        'status' => 'boolean',
    ];

    /* -------------------------------------------------------------------------- */
    /*                                   Scopes */
    /* -------------------------------------------------------------------------- */
    public function scopeActive(Builder $query)
    {
        return $query->where('status', true);
    }

    public function scopeInactive(Builder $query)
    {
        return $query->where('status', false);
    }
}
