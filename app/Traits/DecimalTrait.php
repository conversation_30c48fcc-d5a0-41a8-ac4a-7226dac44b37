<?php

namespace App\Traits;

trait DecimalTrait
{
    public static function setDecimal($amount, $decimal = null, $cutOff = null)
    {
        if (! isset($decimal)) {
            $decimal = config('decimal.calculation') ?? 8;
        }
        if (is_null($cutOff)) {
            $cutOff = config('decimal.cut_off') ?? false;
        }

        if (($decimal >= 8) || ($cutOff == true)) {
            $floor = pow(10, $decimal); // floor for extra decimal
            $convertedAmount = number_format((floor(strval($amount * $floor)) / $floor), $decimal, '.', '');
        } else {
            $convertedAmount = number_format($amount, $decimal, '.', '');
        }

        return $convertedAmount;
    }
}
