<?php

namespace App\Traits;

/**
 * Trait for Game Log functionality
 * Provides methods to transform site and product codes to their full names
 */
trait GameLogTrait
{
    /**
     * Transform site code to full name
     * @param string $code Site code (FR, CQ, JK, etc.)
     * @return string Full site name
     */
    public function getSiteName($code)
    {
        $siteMap = [
            'FR' => 'PLAYTECH',
            'CQ' => 'CQ9',
            'JK' => 'JOKER',
            'BG' => 'BIG GAMING',
            'VP' => 'VPOWER',
            'PR' => 'PRAGMATIC PLAY',
            'JA' => 'JILI'
        ];

        return $siteMap[$code] ?? $code;
    }

    /**
     * Transform product code to full name
     * @param string $code Product code (OT, SL, FH)
     * @return string Full product name
     */
    public function getProductName($code)
    {
        $productMap = [
            'BN' => 'BONUS',
            'CB' => 'CARD & BOARD',
            'CF' => 'COCK FIGHTING',
            'EC' => 'E-CASINO',
            'ES' => 'ES-SPORTS',
            'FH' => 'FISH HUNTER',
            'FG' => 'FREE GAME',
            'JP' => 'JACKPOT',
            'LC' => 'LIVE CASINO',
            'LT' => 'LOTTO',
            'MG' => 'MINI GAMES',
            'OT' => 'OTHERS',
            'PG' => 'PAYMENT GATEWAY',
            'PK' => 'POKER',
            'SG' => 'SMS GATEWAY',
            'SB' => 'SPORTS BOOK',
            'SL' => 'SLOT',
            'TO' => 'TOURNAMENT',
            'VS' => 'VIRTUAL SPORT'
        ];

        return $productMap[$code] ?? $code;
    }
}
