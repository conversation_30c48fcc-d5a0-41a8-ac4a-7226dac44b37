<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\Lang;

trait DateTrait
{
    public static function dateFormat($date = '', $includeTime = true)
    {
        if (! $date) {
            return false;
        }

        if ($includeTime == true) {
            $date = date('d/m/Y H:i:s', strtotime($date));
        } else {
            $date = date('d/m/Y', strtotime($date));
        }

        return $date;
    }

    public static function dateDiff($type = null, $date1 = '', $date2 = '', $includeTime = false)
    {
        if (! $date1) {
            return null;
        }

        $status = 'remaining'; // over_date / today / remaining

        // initialize
        $dateTime1 = $date1;
        $date1 = date('Y-m-d', strtotime($dateTime1));
        $dateTime2 = ($date2) ? $date2 : date('Y-m-d H:i:s');
        $date2 = date('Y-m-d', strtotime($dateTime2));

        if ($includeTime) {
            $date1 = Carbon::createFromFormat('Y-m-d H:i:s', $dateTime1);
            $date2 = Carbon::createFromFormat('Y-m-d H:i:s', $dateTime2);
        } else {
            $date1 = Carbon::createFromFormat('Y-m-d', $date1);
            $date2 = Carbon::createFromFormat('Y-m-d', $date2);
        }

        switch ($type) {
            case 'age':
                $diffTime = Carbon::parse($date1)->diff($date2); // for past date
                break;
            default:
                $diffTime = Carbon::parse($date2)->diff($date1); // for upcoming date
                if ($diffTime->invert == 1 && $type == 'alert') {
                    $status = 'over_date';
                    $alert = Lang::has('lang.diff-time-over-date') ? Lang::get('lang.diff-time-over-date') : 'Over Date';
                }
                break;
        }

        if ($diffTime->invert == 0) {
            // Year=Yr, Month=Mo, Week=Wk, if more than 1 years old, show only Yr and Mo only, if less than 1 years old show Mo and Wk only, less than 1 week show <1Wk
            switch (true) {
                case $diffTime->y > 0:
                    $display1 = Lang::has('lang.diff-time-year-month') ? Lang::get('lang.diff-time-year-month') : '%year% Yr %month% Mo';
                    $display1 = str_replace(['%year%', '%month%'], [$diffTime->y, $diffTime->m], $display1);

                    $age = $display1;
                    break;

                case $diffTime->m > 0:
                    $display1 = Lang::has('lang.diff-time-month-week') ? Lang::get('lang.diff-time-month-week') : '%month% Mo %week% Wk';
                    $display1 = str_replace(['%month%', '%week%'], [$diffTime->m, floor($diffTime->d / 7)], $display1);

                    $age = $display1;
                    break;

                case $diffTime->d >= 7:
                    $display1 = Lang::has('lang.diff-time-week') ? Lang::get('lang.diff-time-week') : '%week% Wk';
                    $display1 = str_replace(['%week%'], [floor($diffTime->d / 7)], $display1);

                    $age = $display1;
                    break;

                case $diffTime->d > 0:
                    $display1 = Lang::has('lang.diff-time-days') ? Lang::get('lang.diff-time-days') : '%days% Days';
                    $display1 = str_replace(['%days%'], [$diffTime->d], $display1);

                    $age = $display1;
                    $alert = ($diffTime->d <= 3 && $diffTime->invert == 0) ? $display1 : null;
                    break;

                case $diffTime->d == 0:
                    $display1 = Lang::has('lang.diff-time-today') ? Lang::get('lang.diff-time-today') : 'Today';
                    $display2 = Lang::has('lang.diff-time-days') ? Lang::get('lang.diff-time-days') : '%days% Days';
                    $display2 = str_replace(['%days%'], [$diffTime->d], $display2);

                    $status = 'today';

                    $age = $display2;
                    $alert = $display1;
                    break;
                default:
                    break;
            }
        }

        $message = null;

        switch ($type) {
            case 'age':
                $message = $age ?? null;
                break;
            case 'alert':
            case 'days-left':
                if (isset($alert)) {
                    $message = [
                        'type' => $status,
                        'display' => $alert,
                    ];
                }
                break;
            default:
                break;
        }

        return $message;
    }
}
