<?php

use App\Http\Controllers\Api\V1\MerchantController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum', 'APIBlockIP'])->group(function () {
    Route::post('/logingame', [MerchantController::class, 'getLoginUrl'])->name('logingame');
    Route::post('/logoutgame', [MerchantController::class, 'logout'])->name('logoutgame');
    Route::post('/checkbalance', [MerchantController::class, 'checkGameBalance'])->name('checkbalance');
    Route::post('/withdraw', [MerchantController::class, 'withdraw'])->name('withdraw');
    Route::post('/getbettransactions', [MerchantController::class, 'getBetTransactions'])->name('getbettransactions');
});
