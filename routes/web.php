<?php

use App\Http\Controllers\ActiveAnalysisController;
use App\Http\Controllers\ActiveUserController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EventStatisticController;
use App\Http\Controllers\GameLogController;
use App\Http\Controllers\ImagesController;
use App\Http\Controllers\MerchantController;
use App\Http\Controllers\NewMemberController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\RolesController;
use App\Http\Controllers\StatisticController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\TopPlayerController;
use App\Http\Controllers\UserReferralController;
use App\Http\Controllers\UsersController;
use App\Models\Permission;
use App\Models\Role;
use Illuminate\Support\Facades\Route;

// Flush Permissions
Route::middleware(['auth', 'WebBlockIP', 'role:superadmin'])->get('/flush-permissions', function () {
    // Define Roles
    $roles = ['superadmin', 'admin', 'finance', 'merchant'];

    // Create Roles
    foreach ($roles as $role) {
        Role::updateOrCreate(['name' => $role]);
    }

    // Define Permissions
    // All Permissions
    $permissions = [
        'view dashboard',
        'view roles',
        'view users',
        'edit users',
        'delete users',
        'manage finances',
        'manage roles',
        'manage permissions',
    ];

    // Merchant Permissions
    $merchantPermissions = [
        'view dashboard',
    ];

    foreach ($permissions as $permission) {
        Permission::updateOrCreate(['name' => $permission]);
    }

    // Assign all permissions to the superadmin role
    $superAdminRole = Role::where('name', 'superadmin')->first();
    $superAdminRole->syncPermissions($permissions);

    Role::where('name', 'Merchant')->first()->syncPermissions($merchantPermissions);

    return redirect()->back()->with('success', 'Permissions flushed successfully.');
});

// Auth
Route::controller(AuthenticatedSessionController::class)->middleware('WebBlockIP')->group(function () {
    Route::get('login', 'create')->name('login')->middleware('guest');
    Route::post('login', 'store')->name('login.store')->middleware('guest');
    Route::delete('logout', 'destroy')->name('logout');
});

// Dashboard
Route::get('/', [DashboardController::class, 'index'])->name('dashboard')->middleware(['auth', 'WebBlockIP']);
Route::get('/dashboard/export', [DashboardController::class, 'export'])->name('dashboard.export');
Route::get('/report-details', [DashboardController::class, 'reportDetails'])->name('report-details');
Route::get('/report-details/export', [DashboardController::class, 'exportReportDetails'])->name('report-details.export');

// Statistic
Route::get('/statistics', [StatisticController::class, 'index'])->name('statistic')->middleware(['auth', 'WebBlockIP']);
Route::get('/statistics/export', [StatisticController::class, 'export'])->middleware(['auth', 'verified']);
Route::get('/top-players', [StatisticController::class, 'topPlayers'])->name('top-players')->middleware(['auth', 'WebBlockIP']);
Route::get('/top-players/export', [StatisticController::class, 'exportTopPlayers'])->name('top-players.export')->middleware(['auth', 'WebBlockIP']);

// Top Players
Route::get('/top-players', [TopPlayerController::class, 'topPlayers'])->name('top-players')->middleware(['auth', 'WebBlockIP']);
Route::get('/top-players/export', [TopplayerController::class, 'exportTopPlayers'])->name('top-players.export')->middleware(['auth', 'WebBlockIP']);

// Users
Route::controller(UsersController::class)
    ->prefix('users')
    ->name('users')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::middleware('permission:view users')->get('', 'index');
        Route::middleware('permission:view users')->get('/all', 'all')->name('.all');
        Route::middleware('permission:create users')->get('/create', 'create')->name('.create');
        Route::middleware('permission:create users')->post('/', 'store')->name('.store');
        Route::middleware('permission:edit users')->get('/{user}/edit', 'edit')->name('.edit');
        Route::middleware('permission:edit users')->put('/{user}', 'update')->name('.update');
        Route::post('/{user}', 'issuetoken')->name('.issueToken');
        Route::middleware('permission:delete users')->delete('/{user}', 'destroy')->name('.destroy');
    });

// Reports
Route::controller(ReportController::class)
    ->prefix('reports')
    ->name('reports')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::get('deposit', 'deposit')->name('.deposit');
        Route::get('withdrawal', 'withdrawal')->name('.withdrawal');
        Route::get('machine', 'machine')->name('.machine');
        Route::get('pos-recharge', [ReportController::class, 'posReport'])->defaults('type', 'recharge')->name('.pos-recharge');
        Route::get('pos-elimination', [ReportController::class, 'posReport'])->defaults('type', 'elimination')->name('.pos-elimination');
        Route::get('pos-store', 'posStore')->name('.pos-store')->middleware('role:admin');
        Route::get('machine-summary', 'machineSummary')->name('.machine-summary');
        Route::get('currency-detail', 'currencyDetail')->name('.currency-detail');
        Route::get('pos-recharge/export', [ReportController::class, 'exportPosReport'])
            ->defaults('type', 'recharge')
            ->name('.pos-recharge.export');
        Route::get('pos-elimination/export', [ReportController::class, 'exportPosReport'])
            ->defaults('type', 'elimination')
            ->name('.pos-elimination.export');
        Route::get('machine-summary/export', 'exportMachineSummary')->name('.machine-summary.export');
        Route::get('currency-detail/export', 'exportCurrencyDetail')->name('.currency-detail.export');
    });

Route::get('/img/{path}', [ImagesController::class, 'show'])
    ->where('path', '.*')
    ->name('image');

// Merchants
Route::controller(MerchantController::class)
    ->prefix('merchants')
    ->name('merchants')
    ->middleware(['auth', 'WebBlockIP', 'role:superadmin'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/all', 'all')->name('.all');
        Route::get('/create', 'create')->name('.create');
        Route::post('/', 'store')->name('.store');
        Route::get('/{merchant}/edit', 'edit')->name('.edit');
        Route::put('/{merchant}', 'update')->name('.update');
        Route::delete('/{merchant}', 'destroy')->name('.destroy');
    });

// Stores
Route::controller(StoreController::class)
    ->prefix('stores')
    ->name('stores')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::get('/all', 'all')->name('.all');
    });

// Roles
Route::controller(RolesController::class)
    ->prefix('roles')
    ->name('roles')
    ->middleware(['auth', 'WebBlockIP', 'role:superadmin'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/all', 'all')->name('.all');
        Route::get('/create', 'create')->name('.create');
        Route::post('/', 'store')->name('.store');
        Route::get('/{role}/edit', 'edit')->name('.edit');
        Route::put('/{role}', 'update')->name('.update');
        Route::delete('/{role}', 'destroy')->name('.destroy');
    });

// WWJ
Route::controller(GameLogController::class)
    ->prefix('game-logs')
    ->name('game-logs')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::get('player-logs', 'playerLogs')->name('.player-logs');
        Route::get('player-logs/export', 'exportPlayerLogs')->name('.player-logs.export');
        Route::get('player-detail-logs', 'playerDetailLogs')->name('.player-detail-logs');
        Route::get('player-detail-logs/export', 'exportPlayerDetailLogs')->name('.player-detail-logs.export');
        Route::get('gsc-player-logs', 'gscLogs')->name('.gsc-player-logs');
        Route::get('gsc-player-logs/export', 'exportGscLogs')->name('.gsc-player-logs.export');
        Route::get('gsc-player-detail-logs', 'gscDetailLogs')->name('.gsc-player-detail-logs');
        Route::get('gsc-player-detail-logs/export', 'exportGscDetailLogs')->name('.gsc-player-detail-logs.export');
        Route::get('all-player-logs', 'allPlayerLogs')->name('.all-player-logs');
        Route::get('all-player-logs/export', 'exportAllPlayerLogs')->name('.all-player-logs.export');

    });

// User Referrals
Route::controller(UserReferralController::class)
    ->prefix('user-referrals')
    ->name('user-referrals')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/export', 'export')->name('.export');
    });

// Active Analysis
Route::controller(ActiveAnalysisController::class)
    ->prefix('active-analysis')
    ->name('active-analysis')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::get('', 'index');
    });

// Event Statistics
Route::controller(EventStatisticController::class)
    ->prefix('event-statistics')
    ->name('event-statistics')
    ->middleware(['auth', 'WebBlockIP', 'role:admin'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/export', 'export')->name('.export');
    });

// New Members
Route::controller(NewMemberController::class)
    ->prefix('new-members')
    ->name('new-members')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/export', 'export')->name('.export');
    });

// Active Users
Route::controller(ActiveUserController::class)
    ->prefix('active-users')
    ->name('active-users')
    ->middleware(['auth', 'WebBlockIP'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/export', 'export')->name('.export');
    });
