@import 'tailwindcss/base';

@import 'tailwindcss/components';
@import 'buttons';
@import 'form';

@import 'tailwindcss/utilities';

.tooltip {
  display: none;
}

.relative:hover .tooltip {
  display: inline-block;
  position: absolute;
  top: 100%;
  left: 0;
  white-space: nowrap;
  margin-top: 4px;
  background-color: #333;
  color: #fff;
  padding: 5px;
  border-radius: 3px;
  z-index: 10;
}

.switch {
  position: relative;
  display: inline-block;
  width: 34px;
  height: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4CAF50;
}

input:checked + .slider:before {
  transform: translateX(14px);
}

/* Sticky table header styles */
.sticky-header-table {
  max-height: 70vh;
  overflow-y: auto;
}

.sticky-header-table thead {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: white;
}

.sticky-header-table th {
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 20;
}

/* Date range picker button to match multi-select height */
.date-range-button {
  min-height: 40px !important;
  height: auto !important;
}
