/**
 * Traits for Game Log components
 * Provides methods to transform site and product codes to their full names
 */

export default {
  methods: {
    /**
     * Transform site code to full name
     * @param {string} code - Site code (FR, CQ, JK, etc.)
     * @returns {string} Full site name
     */
    getSiteName(code) {
      if (!code) return code;

      const siteMap = {
        'FR': 'PLAYTECH',
        'CQ': 'CQ9',
        'JK': 'JOKER',
        'BG': 'BIG GAMING',
        'VP': 'VPOWER',
        'PR': 'PRAGMATIC PLAY',
        'JA': 'JILI'
      };

      return siteMap[code] || code;
    },

    /**
     * Transform product code to full name
     * @param {string} code - Product code (OT, SL, FH)
     * @returns {string} Full product name
     */
    getProductName(code) {
      if (!code) return code;

      const productMap = {
        'BN': 'BONUS',
        'CB': 'CARD & BOARD',
        'CF': 'COCK FIGHTING',
        'EC': 'E-CASINO',
        'ES': 'ES-SPORTS',
        'FH': 'FISH HUNTER',
        'FG': 'FREE GAME',
        'JP': 'JACKPOT',
        'LC': 'LIVE CASINO',
        'LT': 'LOTTO',
        'MG': 'MINI GAMES',
        'OT': 'OTHERS',
        'PG': 'PAYMENT GATEWAY',
        'PK': 'POKER',
        'SG': 'SMS GATEWAY',
        'SB': 'SPORTS BOOK',
        'SL': 'SLOT',
        'TO': 'TOURNAMENT',
        'VS': 'VIRTUAL SPORT'
      };

      return productMap[code] || code;
    }
  }
};
