<script setup>
import { cn } from '@/lib/utils';

const props = defineProps({
  class: { type: null, required: false },
  stickyHeader: { type: Boolean, default: true },
});
</script>

<template>
  <div class="relative w-full overflow-auto max-h-[70vh]">
    <table :class="cn('w-full caption-bottom text-sm', 
                      props.stickyHeader ? 'border-separate border-spacing-0' : '',
                      props.class)">
      <slot />
    </table>
  </div>
</template>
