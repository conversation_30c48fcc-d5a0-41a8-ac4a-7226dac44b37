<template>
  <div class="flex flex-col gap-2">
    <Label v-if="label">{{ label }}:</Label>
    <Popover v-model:open="open" :disabled="disabled">
      <PopoverTrigger as-child>
        <Button
          variant="outline"
          role="combobox"
          :aria-expanded="open"
          class="w-full justify-between h-auto min-h-10"
          :class="[
            'px-3 py-2 text-sm',
            open && 'border-ring',
            !selectedOption && 'text-muted-foreground',
            className
          ]"
          :disabled="disabled"
        >
          <div class="flex items-center justify-between w-full">
            <span class="truncate" :class="selectedOption ? 'text-gray-700' : 'text-muted-foreground'">
              {{ selectedOption ? selectedOption.label : placeholder }}
            </span>
            <ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50 ml-2" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent class="p-0 w-full min-w-[var(--radix-popover-trigger-width)]" align="start" :side-offset="4" :align-offset="0" position="fixed">
        <div class="w-full rounded-md border border-input bg-popover text-popover-foreground shadow-md outline-none">
          <!-- Search input -->
          <div class="flex items-center border-b px-3">
            <Search class="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              v-model="searchTerm"
              :placeholder="searchPlaceholder"
              class="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>

          <!-- Empty state -->
          <div v-if="filteredOptions.length === 0" class="py-6 text-center text-sm">
            {{ emptyText }}
          </div>

          <!-- Options list -->
          <div v-else class="max-h-[300px] overflow-auto p-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
            <div v-for="option in filteredOptions"
                 :key="option.value"
                 @click="selectOption(option.value)"
                 class="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                 :class="{'bg-gray-100': option.value === modelValue}">
              <Check class="mr-2 h-4 w-4 text-gray-600" :class="option.value === modelValue ? 'opacity-100' : 'opacity-0'" />
              {{ option.label }}
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
    <div v-if="error" class="text-red-600 text-sm">{{ error }}</div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Button } from '@/Components/ui/button';
import { Check, ChevronsUpDown, Search } from 'lucide-vue-next';
import { Label } from '@/Components/ui/label';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: 'Select an item...'
  },
  searchPlaceholder: {
    type: String,
    default: 'Search items...'
  },
  emptyText: {
    type: String,
    default: 'No items found.'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

// State
const open = ref(false);
const searchTerm = ref('');

// Selected option
const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue);
});

// Filter options based on search term
const filteredOptions = computed(() => {
  if (!searchTerm.value.trim()) {
    return props.options;
  }

  const term = searchTerm.value.toLowerCase();
  return props.options.filter(option =>
    option.label.toLowerCase().includes(term)
  );
});

// Select an option
function selectOption(value) {
  emit('update:modelValue', value);
  open.value = false;
}

// Clear search term when dropdown is closed
watch(open, (isOpen) => {
  if (!isOpen) {
    searchTerm.value = '';
  }
});
</script>
