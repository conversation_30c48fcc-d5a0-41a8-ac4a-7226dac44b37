<template>
  <div class="flex flex-col gap-2">
    <Label v-if="label">{{ label }}:</Label>
    <Popover v-model:open="open" :disabled="disabled">
      <PopoverTrigger as-child>
        <Button
          variant="outline"
          role="combobox"
          :aria-expanded="open"
          class="w-full justify-between h-auto min-h-10"
          :class="[
            'px-3 py-2 text-sm',
            open && 'border-ring',
            selectedValues.length === 0 && 'text-muted-foreground',
            className
          ]"
          :disabled="disabled"
        >
          <div class="flex flex-wrap gap-1 max-h-[80px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
            <div v-if="selectedValues.length > 0" class="flex flex-wrap gap-1 w-full">
              <Badge
                v-for="value in selectedValues"
                :key="value"
                variant="secondary"
                class="mr-1 mb-1 flex-shrink-0 inline-flex items-center"
              >
                <span class="truncate max-w-[150px]">{{ getDisplayValue(value) }}</span>
                <button
                  class="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  @click.stop="deselectValue(value)"
                >
                  <X class="h-3 w-3 text-muted-foreground hover:text-foreground" />
                </button>
              </Badge>
              <Badge v-if="selectedValues.length > 5" variant="outline" class="ml-1 mb-1">
                {{ selectedValues.length }} selected
              </Badge>
            </div>
            <span v-if="selectedValues.length === 0" class="text-muted-foreground">
              {{ placeholder }}
            </span>
          </div>
          <ChevronsUpDown class="h-4 w-4 shrink-0 opacity-50 ml-2" />
        </Button>
      </PopoverTrigger>
      <PopoverContent class="p-0 w-full min-w-[var(--radix-popover-trigger-width)]" align="start" :side-offset="4" :align-offset="0" position="fixed">
        <div class="w-full rounded-md border border-input bg-popover text-popover-foreground shadow-md outline-none">
          <!-- Search input -->
          <div class="flex items-center border-b px-3">
            <Search class="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              v-model="searchTerm"
              :placeholder="searchPlaceholder"
              class="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>

          <!-- Empty state -->
          <div v-if="filteredOptions.length === 0" class="py-6 text-center text-sm">
            {{ emptyText }}
          </div>

          <!-- Options list -->
          <div v-else class="max-h-[300px] overflow-auto p-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent">
            <!-- Selected items first -->
            <div v-if="selectedValues.length > 0" class="mb-2">
              <div class="px-2 py-1.5 text-xs font-semibold text-muted-foreground">Selected</div>
              <div v-for="value in selectedValues"
                   :key="value"
                   @click="toggleValue(value)"
                   class="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                <Check class="mr-2 h-4 w-4 opacity-100" />
                {{ getDisplayValue(value) }}
              </div>
            </div>

            <!-- Separator if there are selected items -->
            <div v-if="selectedValues.length > 0 && filteredOptions.filter(opt => !selectedValues.includes(opt.value)).length > 0"
                 class="my-1 border-t"></div>

            <!-- Available items -->
            <div v-if="filteredOptions.filter(opt => !selectedValues.includes(opt.value)).length > 0">
              <div v-if="selectedValues.length > 0" class="px-2 py-1.5 text-xs font-semibold text-muted-foreground">Available</div>
              <div v-for="option in filteredOptions.filter(opt => !selectedValues.includes(opt.value))"
                   :key="option.value"
                   @click="toggleValue(option.value)"
                   class="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50">
                <Check class="mr-2 h-4 w-4 opacity-0" />
                {{ option.label }}
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
    <div v-if="error" class="text-red-600 text-sm">{{ error }}</div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { Check, ChevronsUpDown, X, Search } from 'lucide-vue-next';
import { Label } from '@/Components/ui/label';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: 'Select items...'
  },
  searchPlaceholder: {
    type: String,
    default: 'Search items...'
  },
  emptyText: {
    type: String,
    default: 'No items found.'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue']);

// State
const open = ref(false);
const searchTerm = ref('');
const selectedValues = computed(() => props.modelValue);

// Filter options based on search term
const filteredOptions = computed(() => {
  if (!searchTerm.value.trim()) {
    return props.options;
  }

  const term = searchTerm.value.toLowerCase();
  return props.options.filter(option =>
    option.label.toLowerCase().includes(term)
  );
});

// Helper functions
function isSelected(value) {
  return selectedValues.value.includes(value);
}

function toggleValue(value) {
  if (isSelected(value)) {
    deselectValue(value);
  } else {
    selectValue(value);
  }
}

function selectValue(value) {
  // Special handling for "all" option
  if (value === 'all') {
    emit('update:modelValue', ['all']);
    return;
  }

  // If "all" is currently selected and selecting another item, remove "all"
  if (selectedValues.value.includes('all')) {
    emit('update:modelValue', [value]);
    return;
  }

  // Add the value to the selection
  const newValues = [...selectedValues.value, value];
  emit('update:modelValue', newValues);

  // Close the dropdown if too many items are selected (optional)
  if (newValues.length > 10) {
    setTimeout(() => {
      open.value = false;
    }, 300);
  }
}

function deselectValue(value) {
  // Don't allow deselecting the last item
  if (selectedValues.value.length === 1) {
    return;
  }

  const newValues = selectedValues.value.filter(v => v !== value);
  emit('update:modelValue', newValues);
}

function getDisplayValue(value) {
  const option = props.options.find(opt => opt.value === value);
  return option ? option.label : value;
}

// Add a watcher to log search results for debugging
watch(searchTerm, (newValue) => {
  console.log('Search term:', newValue);
  console.log('Filtered options:', filteredOptions.value);
});

// Clear search term when dropdown is closed
watch(open, (isOpen) => {
  if (!isOpen) {
    searchTerm.value = '';
  }
});
</script>
