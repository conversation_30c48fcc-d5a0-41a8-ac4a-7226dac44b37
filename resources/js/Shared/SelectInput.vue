<template>
  <div class="flex flex-col gap-2">
    <Label v-if="label">{{ label }}:</Label>
    <Popover v-model:open="open">
      <PopoverTrigger as-child>
        <Button variant="outline" role="combobox" :aria-expanded="open" class="w-full justify-between shadow-none"
          :class="{
            '!text-muted-foreground': !value
          }" :disabled="disabled">
          <slot name="selected" />
          <ChevronDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent :class="`p-0 ${popoverContentClass}`">
        <Command>
          <CommandInput class="h-9" :placeholder="`Search ${label}`" />
          <CommandEmpty>No {{ label }} Found.</CommandEmpty>
          <CommandList>
            <CommandGroup>
              <slot />
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
    <div v-if="error" class="text-red-600 text-sm">{{ error }}</div>
  </div>
</template>

<script>
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/Components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/Components/ui/popover';
import { v4 as uuid } from 'uuid';
import { Check, ChevronDown } from 'lucide-vue-next';
import { Button } from '@/Components/ui/button';

export default {
  components: {
    Label,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
    Popover,
    PopoverContent,
    PopoverTrigger,
    ChevronDown,
    Button,
    Check
  },
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default() {
        return `select-input-${uuid()}`;
      },
    },
    error: String,
    label: String,
    modelValue: [String, Number, Boolean],
    disabled: Boolean,
    optionValues: Array,
    value: String,
    popoverContentClass: String
  },
  emits: ['update:modelValue'],
  data() {
    return {
      selected: this.modelValue,
    };
  },
  watch: {
    selected(selected) {
      this.$emit('update:modelValue', selected);
    },
  },
  methods: {
    focus() {
      this.$refs.input.focus();
    },
    select() {
      this.$refs.input.select();
    },
  },
  computed: {
    open: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
  },
};
</script>
