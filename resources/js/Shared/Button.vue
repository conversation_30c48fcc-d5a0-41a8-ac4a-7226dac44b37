<template>
    <Link v-if="href" as-child :href="href">
        <UIButton :disabled="loading">
            <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
            <font-awesome-icon v-else-if="icon" :icon="icon" />
            <span>{{ label }}</span>
        </UIButton>
    </Link>
    <UIButton v-else :disabled="loading">
        <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
        <font-awesome-icon v-else-if="icon" :icon="icon" />
        <span>{{ label }}</span>
    </UIButton>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import { Button as UIButton } from '@/Components/ui/button';
import { Loader2 } from 'lucide-vue-next';

export default {
    name: 'But<PERSON>',
    components: {
        Link,
        UIButton,
        Loader2,
    },
    props: {
        href: {
            type: String,
        },
        label: {
            type: String,
            required: true,
        },
        icon: {
            type: [String, Array],
            required: false,
        },
        loading: {
            type: <PERSON><PERSON><PERSON>,
        }
    },
};
</script>

<style scoped></style>