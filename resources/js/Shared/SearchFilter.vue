<template>
  <div>
    <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-4">
      <!-- <div>
        <Label class="mb-2 inline-block">{{ searchLabel }}:</Label>
        <Input autocomplete="off" type="text" name="search" placeholder="Search…" v-model="value" />
      </div> -->
      <slot />
    </div>
    <div class="text-right">
      <Button @click="$emit('reset')" class="min-w-20">{{ buttonLabel }}</Button>
    </div>
  </div>
</template>

<script>
import { Label } from '@/Components/ui/label'
import { Input } from '@/Components/ui/input'
import { Button } from '@/Components/ui/button'
import Dropdown from '@/Shared/Dropdown.vue'

export default {
  components: {
    Dropdown,
    Label,
    Input,
    Button,
  },
  props: {
    modelValue: String,
    searchLabel: {
      type: String,
      default: 'Search',
    },   
    buttonLabel: {
      type: String,
      default: 'Reset',
    },  
    maxWidth: {
      type: Number,
      default: 300,
    },
  },
  emits: ['update:modelValue', 'reset'],
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
  },
}
</script>
