<template>
    <Link v-if="!mobileLink" :href="navlink.url" class="flex items-center px-3 py-2 text-muted-foreground rounded-lg"
        :class="{
            'gap-3 transition-all hover:text-primary': !mobileLink,
            'gap-4 hover:text-foreground': mobileLink,
            'bg-muted text-primary': isUrl(navlink.checkActive)
        }">
    <component v-if="navlink.icon" :is="navlink.icon" class="size-5" />
    <span :class="{ 'pl-8': !navlink.icon }">{{ navlink.name }}</span>
    </Link>
    <SheetClose v-else as-child>
        <Link :href="navlink.url" class="flex items-center px-3 py-2 text-muted-foreground rounded-lg" :class="{
            'gap-3 transition-all hover:text-primary': !mobileLink,
            'gap-4 hover:text-foreground': mobileLink,
            'bg-muted text-primary': isUrl(navlink.checkActive)
        }">
        <component v-if="navlink.icon" :is="navlink.icon" class="size-5" />
        <span :class="{ 'pl-9': !navlink.icon }">{{ navlink.name }}</span>
        </Link>
    </SheetClose>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import { SheetClose } from '@/Components/ui/sheet';
export default {
    components: {
        Link,
        SheetClose
    },
    props: {
        navlink: Object,
        mobileLink: Boolean
    },
    methods: {
        isUrl(...urls) {
            const currentUrl = this.$page.url.substr(1);
            if (urls[0] === '') {
                return currentUrl === '';
            }
            return urls.filter((url) => currentUrl.startsWith(url)).length;
        },
    },
};
</script>
