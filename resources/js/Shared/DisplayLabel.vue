<template>
    <div class="flex flex-wrap gap-y-3">
        <span
            class="w-full md:w-1/2 xl:text-right xl:pr-1.5 inline-block xl:inline-flex xl:items-center xl:justify-end">
            {{ label }}:
        </span>
        <span class="w-full md:w-1/2 xl:text-left xl:pl-1.5 inline-block xl:inline-flex xl:items-center">
            {{ value }}
        </span>
    </div>
</template>

<script>
export default {
    props: {
        label: String,
        value: [String, Number]
    }
};
</script>