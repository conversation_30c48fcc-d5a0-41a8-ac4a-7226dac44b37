<template>
<div v-if="data.links">
  <Pagination :total="data.total" :sibling-count="1" show-edges :items-per-page="data.per_page" :defaultPage="data.current_page">
    <PaginationList class="flex items-center gap-1">
      
      <!-- First Page -->
      <PaginationFirst as-child :disabled="data.current_page === 1">
        <Link :href="data.first_page_url">
          <ChevronsLeft />
        </Link>
      </PaginationFirst>

      <!-- Previous Page -->
      <PaginationPrev as-child :disabled="!data.prev_page_url">
        <Link v-if="data.prev_page_url" :href="data.prev_page_url">
          <ChevronLeft />
        </Link>
        <Button v-else class="w-10 h-10 bg-inherit text-inherit border-inherit border shadow-none">
          <ChevronLeft />
        </Button>
      </PaginationPrev>

      <!-- Page Numbers -->
      <template v-for="(item, index) in data.links" :key="index">
        <PaginationListItem as-child v-if="item.url">
          <Link :href="item.url">
            <Button variant="outline" class="w-10 h-10 hidden lg:flex" :class="{ 'text-primary-foreground bg-primary flex': item.active }">
              {{ item.label }}
            </Button>
          </Link>
        </PaginationListItem>
        <PaginationEllipsis v-else />
      </template>

      <!-- Next Page -->
      <PaginationNext as-child :disabled="!data.next_page_url">
        <Link v-if="data.next_page_url" :href="data.next_page_url">
          <ChevronRight />
        </Link>
        <Button v-else class="w-10 h-10 bg-inherit text-inherit border-inherit border shadow-none">
          <ChevronRight />
        </Button>
      </PaginationNext>

      <!-- Last Page -->
      <PaginationLast as-child :disabled="data.current_page === data.last_page">
        <Link :href="data.last_page_url">
          <ChevronsRight />
        </Link>
      </PaginationLast>

    </PaginationList>
  </Pagination>
</div>

</template>

<script>
import { Link } from '@inertiajs/vue3'
import {
  Pagination,
  PaginationEllipsis,
  PaginationFirst,
  PaginationLast,
  PaginationList,
  PaginationListItem,
  PaginationNext,
  PaginationPrev,
} from '@/Components/ui/pagination'
import { Button } from '@/Components/ui/button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-vue-next';

export default {
  components: {
    Link,
    Pagination,
    PaginationEllipsis,
    PaginationFirst,
    PaginationLast,
    PaginationList,
    PaginationListItem,
    PaginationNext,
    PaginationPrev,
    ChevronsLeft,
    ChevronLeft,
    Button,
    ChevronRight,
    ChevronsRight
  },
  props: {
    data: Object
  },
}
</script>
