<template>
  <SidebarLink v-if="!navLink.submenu" :navlink="navLink" :mobile-link="mobileLink" />
  <Collapsible v-else v-model:open="isOpen" class="w-full space-y-2">
    <div class="flex items-center justify-between space-x-4 text-muted-foreground rounded-lg" :class="{
      'bg-muted text-primary': isUrl(navLink.checkActive),
    }">
      <SidebarToggleLink :navlink="navLink" :mobile-link="mobileLink" />
      <CollapsibleTrigger as-child>
        <Button variant="ghost" size="sm" class="w-9 p-0 !bg-transparent text-muted-foreground hover:text-primary">
          <ChevronDown v-if="!isOpen" class="h-4 w-4" />
          <ChevronUp v-else class="h-4 w-4" />
          <span class="sr-only">Toggle</span>
        </Button>
      </CollapsibleTrigger>
    </div>
    <CollapsibleContent class="!mt-0 space-y-3 pt-3">
      <SidebarLink v-for="submenuNavlink in filteredSubmenu" :key="submenuNavlink.name" :navlink="submenuNavlink"
        :mobile-link="mobileLink" />
    </CollapsibleContent>
  </Collapsible>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import { Button } from '@/Components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/Components/ui/collapsible';
import { ChevronDown, ChevronRight, ChevronUp } from 'lucide-vue-next';
import SidebarLink from './SidebarLink.vue';
import SidebarToggleLink from './SidebarToggleLink.vue';

export default {
  components: {
    Link,
    Button,
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
    ChevronRight,
    ChevronDown,
    SidebarLink,
    SidebarToggleLink,
    ChevronUp
  },
  props: {
    navLink: Object,
    mobileLink: Boolean,
  },
  data() {
    return {
      isOpen: false,
    };
  },
  computed: {
    filteredSubmenu() {
      if (!this.navLink.submenu) return [];

      return this.navLink.submenu.filter(submenu => {
        // Check for role-based access
        let hasRequiredRole = true;
        if (submenu.roles && submenu.roles.length > 0) {
          hasRequiredRole = submenu.roles.some(role => this.$page.props.auth.user?.roles.includes(role));
        }

        return hasRequiredRole;
      });
    }
  },
  methods: {
    isUrl(...urls) {
      const currentUrl = this.$page.url.substring(1);
      if (urls[0] === '') {
        return currentUrl === '';
      }
      return urls.filter((url) => currentUrl.startsWith(url)).length;
    },
  },
};
</script>

<style>
.sidebar-menu {
  max-height: 100vh;
  overflow-y: auto;
  overflow-x: auto;
  padding-right: 8px;
  white-space: nowrap;
}

.sidebar-menu::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
}

.sidebar-menu:hover::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.sidebar-menu {
  scrollbar-width: none;
}

.sidebar-menu:hover {
  scrollbar-width: thin;
  scrollbar-color: #888 transparent;
}

.sidebar-menu .group {
  display: inline-flex;
  align-items: center;
}

.sidebar-menu .group .text-md {
  white-space: nowrap;
}
</style>
