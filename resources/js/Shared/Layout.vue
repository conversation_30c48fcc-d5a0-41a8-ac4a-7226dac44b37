<template>
  <div class="min-h-screen w-full md:grid md:grid-cols-[220px_1fr] lg:grid-cols-[252px_1fr]">
    <div class="hidden border-r bg-muted/40 md:block">
      <div class="flex h-full flex-col gap-2">
        <div class="flex h-14 items-center justify-center border-b px-4 lg:h-[60px] lg:px-6">
          <Link :href="route('dashboard')" class="flex items-center gap-2 font-semibold">
          <img src="/images/uwg.png" alt="UWon Gaming" class="h-8" />
          <!-- <Package2 class="h-6 w-6" /> -->
          <!-- <span class="">UWIN</span> -->
          </Link>
        </div>
        <div class="flex-1">
          <nav class="grid items-start px-2 text-sm font-medium lg:px-4 space-y-3">
            <MainMenu v-for="navLink in filteredNavLinks" :key="navLink.name" :nav-link="navLink" />
          </nav>
        </div>
      </div>
    </div>

    <div class="flex flex-col">
      <header class="flex h-14 items-center gap-4 border-b bg-muted/40 px-4 lg:h-[60px] lg:px-6">
        <Sheet>
          <SheetTrigger as-child>
            <Button variant="outline" size="icon" class="shrink-0 md:hidden">
              <Menu class="h-5 w-5" />
              <span class="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" class="flex flex-col">
            <SheetHeader>
              <SheetTitle></SheetTitle>
              <SheetDescription>
              </SheetDescription>
            </SheetHeader>
            <nav class="grid gap-2 text-sm font-medium">
              <SheetClose as-child>
                <Link :href="route('dashboard')" class="flex items-center gap-2 text-lg font-semibold">
                <img src="/images/uwg.png" alt="UWon Gaming" class="h-8" />
                <!-- <span>UWIN</span> -->
                </Link>
              </SheetClose>
              <ScrollArea class="h-[calc(100vh-7rem)] mx-[-0.65rem]">
                <div class="space-y-3">
                  <MainMenu v-for="navLink in filteredNavLinks" :key="navLink.name" :nav-link="navLink" mobile-link />
                </div>
              </ScrollArea>
            </nav>
          </SheetContent>
        </Sheet>
        <div class="w-full flex-1">
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="ghost" class="flex items-center gap-2 px-2 py-1 h-auto">
              <div class="hidden sm:block text-right mr-2">
                <p class="text-xs text-muted-foreground">Welcome back</p>
                <p class="text-sm font-medium">{{ auth.user.name }}</p>
              </div>
              <Avatar class="h-8 w-8">
                <AvatarImage :src="auth.user.photo" alt="User avatar" />
                <AvatarFallback>{{ auth.user.name }}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-56">
            <DropdownMenuLabel class="font-normal">
              <div class="flex flex-col space-y-1 py-1">
                <p class="text-sm font-medium leading-none">{{ auth.user.name }}</p>
                <p class="text-xs leading-none text-muted-foreground">{{ auth.user.email }}</p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div class="px-2 py-1.5">
              <Link :href="route('logout')" method="delete" as="button"
                class="w-full flex items-center justify-center bg-red-100 hover:bg-red-200 text-red-600 hover:text-red-700 rounded-md px-3 py-2 transition-colors duration-150 font-medium"
              >
                <LogOut class="h-4 w-4 mr-1.5" />
                <span>Logout</span>
              </Link>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </header>
      <main class="flex flex-1 flex-col gap-4 md:max-w-[calc(100vw-220px)] lg:max-w-[calc(100vw-280px)]">
        <FlashMessages />
        <slot />
      </main>
    </div>
  </div>
  <Toaster position="top-right" />
</template>

<script>
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/Components/ui/avatar';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/Components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/Components/ui/dropdown-menu';
import { Input } from '@/Components/ui/input';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from '@/Components/ui/sheet';
import { Link } from '@inertiajs/vue3';
import {
  Bell,
  CircleUser,
  Home,
  LayoutDashboard,
  LineChart,
  Menu,
  Package,
  Package2,
  Search,
  ShoppingCart,
  UserRoundCog,
  Store,
  Users,
  UsersRound,
  BarChart4,
  CalendarCog,
  PieChart,
  ClipboardList,
  FileBarChart2,
  FileText,
  Trophy,
  Gamepad2,
  Receipt
} from 'lucide-vue-next';
import MainMenu from './MainMenu.vue';
import { Toaster } from 'vue-sonner';
import FlashMessages from './FlashMessages.vue';
import { ScrollArea } from '@/Components/ui/scroll-area';

export default {
  components: {
    Badge,
    Button,
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    Input,
    Sheet,
    SheetContent,
    SheetTrigger,
    Bell,
    CircleUser,
    Home,
    LineChart,
    Menu,
    Package,
    Package2,
    Search,
    ShoppingCart,
    Users,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Link,
    MainMenu,
    SheetDescription,
    SheetHeader,
    SheetTitle,
    SheetClose,
    Toaster,
    FlashMessages,
    ScrollArea,
    BarChart4,
    CalendarCog,
    PieChart,
    ClipboardList,
    FileBarChart2,
    FileText,
    Trophy,
    Gamepad2,
    Receipt,
  },
  props: {
    auth: Object,
  },
  computed: {
    filteredNavLinks() {
      return this.navLinks.filter(link => {
        const hasPermission = this.auth.user?.can?.[link.permission];

        let hasRequiredRole = true;
        if (link.roles && link.roles.length > 0) {
          hasRequiredRole = link.roles.some(role => this.auth.user?.roles.includes(role));
        }

        return hasPermission && hasRequiredRole;
      });
    },
    filteredSubmenu() {
      return this.submenu.filter(submenu => {
        return this.auth.user?.can?.[submenu.permission];
      });
    }
  },
  data() {
    return {
      navLinks: [
        {
          url: route('dashboard'),
          name: 'Dashboard',
          icon: LayoutDashboard,
          checkActive: '',
          permission: 'view dashboard'
        },
        {
          url: route('statistic'),
          name: 'Statistics',
          icon: BarChart4,
          checkActive: 'statistics',
          permission: 'view dashboard'
        },
          {
          url: route('event-statistics'),
          name: 'Event Statistics',
          icon: CalendarCog,
          checkActive: 'event-statistics',
          permission: 'view dashboard',
          roles: ['admin']
        },
        {
          url: route('report-details'),
          name: 'Report Details',
          icon: FileBarChart2,
          checkActive: 'report-details',
          permission: 'view dashboard'
        },
        {
          url: route('top-players'),
          name: 'Top Players',
          icon: Trophy,
          checkActive: 'top-players',
          permission: 'view dashboard'
        },
         {
          url: route('active-analysis'),
          name: 'Active Analysis',
          icon: LineChart,
          checkActive: 'active-analysis',
          permission: 'view dashboard'
        },
        {
          url: route('user-referrals'),
          name: 'User Referrals',
          icon: UsersRound,
          checkActive: 'user-referrals',
          permission: 'view dashboard'
        },
        {
          url: route('users'),
          name: 'Users',
          icon: Users,
          checkActive: 'users',
          permission: 'view users'
        },
        {
          url: route('roles'),
          name: 'Roles',
          icon: UserRoundCog,
          checkActive: 'roles',
          permission: 'view roles'
        },
        {
          url: route('merchants'),
          name: 'Merchants',
          icon: Store,
          checkActive: 'merchants',
          permission: 'view merchants'
        },
        {
          url: '#',
          name: 'Game Logs',
          icon: Gamepad2,
          permission: 'view reports',
          submenu: [
            {
              url: route('game-logs.all-player-logs'),
              name: 'All Player Logs',
              checkActive: 'game-logs/all-player-logs',
            },
            {
              url: route('game-logs.player-logs'),
              name: 'WWJ Player Logs',
              checkActive: 'game-logs/player-logs',
            },
            {
              url: route('game-logs.gsc-player-logs'),
              name: 'GSC Player Logs',
              checkActive: 'game-logs/gsc-player-logs',
            },
          ]
        },
        {
          url: '#',
          name: 'POS Reports',
          icon: Receipt,
          permission: 'view reports',
          submenu: [
            {
              url: route('reports.pos-recharge'),
              name: 'POS Recharge',
              checkActive: 'reports/pos-recharge',
            },
            {
              url: route('reports.pos-elimination'),
              name: 'POS Elimination',
              checkActive: 'reports/pos-elimination',
            },
            {
              url: route('reports.pos-store'),
              name: 'POS Store Status',
              checkActive: 'reports/pos-store',
              roles: ['admin'],
            },
            {
              url: route('reports.machine-summary'),
              name: 'POS Machine Summary',
              checkActive: 'reports/machine-summary',
            },
            {
              url: route('reports.currency-detail'),
              name: 'POS Currency Detail',
              checkActive: 'reports/currency-detail',
            },
          ]
        },
      ]
    };
  },
};
</script>
