<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Banks" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter v-model="form.search" @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Trashed:</Label>
              <Select v-model="form.trashed" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Please Select Trash" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="with">
                      With Trashed
                    </SelectItem>
                    <SelectItem value="only">
                      Only Trashed
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-3 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Bank Overview</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <Button label="Create" icon="plus" class="cursor-pointer" @click="createBank" />
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create Bank</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateBank">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-4 max-h-[75vh]">
                  <TextInput v-model="dialog.account_number" :error="errors.account_number" label="Account Number" />
                  <TextInput v-model="dialog.name" :error="errors.name" label="Bank Name" />
                  <TextInput v-model="dialog.holder_name" :error="errors.holder_name" label="Holder Name" />
                  <SelectInput v-model="currencySelectIsOpen" :error="errors.currency" label="Currency"
                    :option-values="currencyOptionValues" :value="dialog.currency"
                    popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedCurrencyOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedCurrencyOptionValue?.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ selectedCurrencyOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.currency
                          ? selectedCurrencyOptionValue?.value
                          : `Select Currency` }}</span>
                      </div>

                    </template>
                    <CommandItem v-for="option in currencyOptionValues" :key="option.value" :value="option.value"
                      @select="(ev) => {
                        if (typeof ev.detail.value === 'string') {
                          dialog.currency = ev.detail.value
                        }
                        dialog.currency = option.id.toString()
                        currencySelectIsOpen = false
                      }">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        dialog.currency === option.id.toString() ? 'opacity-100' : 'opacity-0'
                      ]" />
                    </CommandItem>
                  </SelectInput>
                  <SwitchInput v-model="dialog.is_active" :error="errors.is_active" label="Status">
                    <Label>{{ dialog.is_active === true ? 'Active' : 'Inactive' }}</Label>
                  </SwitchInput>
                </div>
              </div>
              <DialogFooter class="px-2">
                <Button type="submit" label="Create Bank" />
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Account Number</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('name')">
              <SortableHeader title="Bank Name" field="name" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('holder_name')">
              <SortableHeader title="Holder Name" field="holder_name" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Total Balance</TableHead>
            <TableHead>Currency</TableHead>
            <TableHead>Status</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(bank, index) in banks.data" :key="bank.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ bank.account_number }}</TableCell>
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('banks.edit', bank.id)">
              {{ bank.name }}
              <icon v-if="bank.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('banks.edit', bank.id)">
              {{ bank.holder_name }}
              <icon v-if="bank.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>{{ bank.currency.code }} {{ bank.total_balance }}</TableCell>
            <TableCell>
              <div class="flex items-center space-x-2">
                <img v-if="bank.currency?.photo" :src="bank.currency.photo" alt="Currency"
                  class="w-6 h-6 rounded-full" />
                <span>{{ bank.currency?.name }}</span>
              </div>
            </TableCell>
            <TableCell>
              <span class="px-3 py-1 text-xs font-medium rounded-full"
                :class="bank.is_active === true ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ bank.is_active === true ? 'Active' : 'Inactive' }}
              </span>
            </TableCell>
            <TableCell>{{ bank.created_at }}</TableCell>
            <TableCell>
              <Link :href="route('banks.edit', bank.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
          </TableRow>
          <TableRow v-if="banks.data.length === 0">
            <TableCell class="text-center border-0" colspan="8">No banks found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="banks" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';
import Icon from '@/Shared/Icon.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import Tooltip from '@/Shared/Tooltip.vue';
import { Check, Info } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@/Components/ui/avatar";

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Icon,
    Tooltip,
    Info,
    Breadcrumb,
    CommandItem,
    Check,
    Avatar,
    AvatarFallback,
    AvatarImage
  },
  layout: Layout,
  props: {
    filters: Object,
    banks: Object,
    sort: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        trashed: this.filters.trashed,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      dialog: {
        account_number: '',
        name: '',
        holder_name: '',
        currency: '',
        is_active: true,
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Banks', link: route('banks'), is_active: true },
      ],
      currencies: [],
      currencySelectIsOpen: false,
    };
  },
  watch: {
    form: {
      deep: true,
      handler: throttle(function () {
        const params = {};
        Object.keys(this.form).forEach(key => {
          if (this.form[key] !== null &&
            this.form[key] !== undefined &&
            this.form[key] !== this.defaultValues[key]) {
            params[key] = this.form[key];
          }
        });

        this.$inertia.get('/banks', pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ['banks'],
          replace: true
        });
      }, 150),
    },
  },
  mounted() {
    this.loadCurrencies();
  },
  methods: {
    async loadCurrencies() {
      try {
        const response = await axios.get('/currencies/all');
        this.currencies = response.data;
      } catch (error) {
        console.error('Failed to load currencies:', error);
      }
    },
    reset() {
      this.form = {
        search: null,
        trashed: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.get('/banks', {}, {
        preserveState: true,
        preserveScroll: true,
        only: ['banks'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },
    createBank() {
      this.dialog.account_number = '';
      this.dialog.name = '';
      this.dialog.holder_name = '';
      this.dialog.currency = '';
      this.dialog.is_active = true;
      this.createDialogIsOpen = true;
    },
    submitCreateBank() {
      this.$inertia.post(route('banks.store'), {
        account_number: this.dialog.account_number,
        name: this.dialog.name,
        holder_name: this.dialog.holder_name,
        currency: this.dialog.currency,
        is_active: this.dialog.is_active,
      }, {
        preserveScroll: true,
        onSuccess: () => {
          this.dialog.account_number = '';
          this.dialog.name = '';
          this.dialog.holder_name = '';
          this.dialog.currency = '';
          this.dialog.is_active = true;
          this.createDialogIsOpen = true;
          this.errors = {};
          this.createDialogIsOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        },
      });
    },
  },
  computed: {
    currencyOptionValues() {
      return this.currencies.map(currency => ({ ...currency, value: currency.name }));
    },
    selectedCurrencyOptionValue() {
      return this.currencyOptionValues.find(option => option.id.toString() === this.dialog.currency);
    }
  }
};
</script>
