<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Create Bank" />
    <h1 class="mb-8 text-3xl font-bold">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('banks')">Banks</Link>
      <span class="text-indigo-400 font-medium">/</span> Create
    </h1>
    <div class="flex justify-center">
      <div class="w-full max-w-5xl bg-white rounded-md shadow overflow-hidden">
        <form @submit.prevent="store">
          <div class="flex flex-wrap -mb-8 -mr-6 p-8">
            <text-input v-model="form.account_number" :error="form.errors.account_number"
              class="pb-8 pr-6 w-full lg:w-1/2" label="Account Number" />
            <text-input v-model="form.name" :error="form.errors.name" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Bank Name" />
            <text-input v-model="form.holder_name" :error="form.errors.holder_name" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Holder Name" />
            <select-input v-model="form.currency" :error="form.errors.currency" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Currency">
              <option v-for="currency in currencies" :key="currency.id" :value="currency.id">{{ currency.name }}
              </option>
            </select-input>
            <div class="pb-8 pr-6 w-full lg:w-1/2">
              <label class="block text-sm font-medium text-gray-700">Status</label>
              <label class="switch mt-2">
                <input type="checkbox" v-model="form.is_active" :true-value=true :false-value=false />
                <span class="slider round"></span>
              </label>
              <span class="ml-2 text-sm text-gray-600">
                {{ form.active === true ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
          <div class="flex items-center justify-end px-8 py-4 bg-gray-50 border-t border-gray-100">
            <loading-button :loading="form.processing" class="btn-indigo" type="submit">
              Create Bank
            </loading-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3'
import Layout from '@/Shared/Layout.vue'
import TextInput from '@/Shared/TextInput.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import LoadingButton from '@/Shared/LoadingButton.vue'
import axios from 'axios'
import { route } from 'ziggy-js'

export default {
  components: {
    Head,
    Link,
    LoadingButton,
    TextInput,
    SelectInput,
  },
  layout: Layout,
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        account_number: '',
        name: '',
        holder_name: '',
        currency: '',
        is_active: true,
      }),
      currencies: [],
    }
  },
  mounted() {
    this.loadCurrencies();
  },
  methods: {
    async loadCurrencies() {
      try {
        const response = await axios.get('/currencies/all');
        this.currencies = response.data;
      } catch (error) {
        console.error('Failed to load currencies:', error);
      }
    },
    store() {
      this.form.post(route('banks.store'));
    },
  },
}
</script>