<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center max-w-3xl gap-4 flex-wrap mb-4">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">{{ form.is_active === true ? 'Active' :
          'Inactive'
        }}</span>
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
    </div>
    <div class="mb-6">
      <Tabs default-value="bank-details">
        <TabsList class="max-w-[500px] md:grid-cols-2">
          <TabsTrigger value="bank-details">
            Bank Details
          </TabsTrigger>
          <TabsTrigger value="audits">
            Audits
          </TabsTrigger>
        </TabsList>
        <TabsContent value="bank-details"
          class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <div>
            <Card>
              <CardContent class="p-0">
                <div class="p-4">
                  <form @submit.prevent="">
                    <div class="xl:columns-2 space-y-4 mb-5">
                      <div class="break-inside-avoid">
                        <SwitchInput label="Status" label-side>
                          <Label>Inactive</Label>
                        </SwitchInput>
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Bank Name" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Account Number" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Account Holders Number" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Currency" value="THB" />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Balance" value="THB 81,198.91" />
                      </div>
                      <div class="break-inside-avoid">
                        <SwitchInput label="Is Inventorized" label-side>
                          <Label>No</Label>
                        </SwitchInput>
                      </div>
                      <div class="break-inside-avoid">
                        <SwitchInput label="Is Payment Method?" label-side>
                          <Label>No</Label>
                        </SwitchInput>
                      </div>
                    </div>
                    <div class="flex items-center justify-end gap-4 flex-wrap">
                      <Button label="Save" />
                    </div>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="audits">

        </TabsContent>
      </Tabs>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Transactions</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Reference</TableHead>
            <TableHead>Transaction Type</TableHead>
            <TableHead>Currency</TableHead>
            <TableHead>In</TableHead>
            <TableHead>Out</TableHead>
            <TableHead>Bank Balance</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow class="bg-[#F1F5F9]">
            <TableCell colspan="11">No Record</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div>
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <form @submit.prevent="update">
              <div class="grid md:grid-cols-2 gap-4 mb-4">
                <TextInput v-model="form.account_number" :error="form.errors.account_number" label="Account Number" />
                <TextInput v-model="form.name" :error="form.errors.name" label="Bank Name" />
                <TextInput v-model="form.holder_name" :error="form.errors.holder_name" label="Holder Name" />
                <SelectInput v-model="currencySelectIsOpen" :error="form.errors.currency" label="Currency"
                  :option-values="currencyOptionValues" :value="form.currency"
                  popover-content-class="w-[701px] max-w-[calc(100vw-4rem)] md:w-[701px] md:max-w-[calc((100vw-219px-80px)/2)] lg:max-w-[calc((100vw-279px-96px)/2)]">
                  <template #selected>
                    <div class="flex items-center gap-2">
                      <Avatar v-if="selectedCurrencyOptionValue?.photo" class="size-6">
                        <AvatarImage :src="selectedCurrencyOptionValue?.photo" alt="Currency Photo" />
                        <AvatarFallback>{{ selectedCurrencyOptionValue?.name }}</AvatarFallback>
                      </Avatar>
                      <span>{{ form.currency
                        ? selectedCurrencyOptionValue?.value
                        : `Select Currency` }}</span>
                    </div>
                  </template>
                  <CommandItem v-for="option in currencyOptionValues" :key="option.value" :value="option.value" @select="(ev) => {
                    if (typeof ev.detail.value === 'string') {
                      form.currency = ev.detail.value
                    }
                    form.currency = option.id.toString()
                    currencySelectIsOpen = false
                  }">
                    <div class="flex items-center gap-2">
                      <Avatar v-if="option.photo" class="size-6">
                        <AvatarImage :src="option.photo" alt="Currency Photo" />
                        <AvatarFallback>{{ option.name }}</AvatarFallback>
                      </Avatar>
                      <span>{{ option.value }}</span>
                    </div>
                    <Check class="ml-auto h-4 w-4" :class="[
                      form.currency === option.id.toString() ? 'opacity-100' : 'opacity-0'
                    ]" />
                  </CommandItem>
                </SelectInput>
                <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status">
                  <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                </SwitchInput>
              </div>
              <div class="flex items-center justify-end gap-4 flex-wrap">
                <Button v-if="!bank.deleted_at" variant="destructive" @click="destroy" type="button"
                  label="Delete Bank" />
                <Button type="submit" label="Update Bank" :loading="form.processing" />
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SelectItem from '@/Components/ui/select/SelectItem.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Label } from '@/Components/ui/label';
import Button from '@/Shared/Button.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Check } from 'lucide-vue-next';
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@/Components/ui/avatar";
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Separator } from '@/Components/ui/separator';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    SelectInput,
    SelectItem,
    SwitchInput,
    Label,
    Button,
    Breadcrumb,
    CommandItem,
    Check,
    Avatar,
    AvatarFallback,
    AvatarImage,
    TabsTrigger,
    TabsList,
    Tabs,
    Separator,
    TabsContent,
    Table,
    TableRow,
    TableCell,
    TableHead,
    TableHeader,
    TableBody,
    DisplayLabel,
  },
  layout: Layout,
  props: {
    bank: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        account_number: this.bank.account_number,
        name: this.bank.name,
        holder_name: this.bank.holder_name,
        currency: this.bank.currency_id.toString(),
        is_active: this.bank.is_active
      }),
      currencies: [],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Banks', link: route('banks') },
        { name: 'Edit', link: route('banks.edit', this.bank.id), is_active: true },
      ],
      currencySelectIsOpen: false,
    };
  },
  mounted() {
    this.loadCurrencies();
  },
  methods: {
    async loadCurrencies() {
      try {
        const response = await axios.get('/currencies/all');
        this.currencies = response.data;
      } catch (error) {
        console.error('Failed to load currencies:', error);
      }
    },
    update() {
      this.form.post(route('banks.update', this.bank.id), {
        onSuccess: () => this.form.reset('photo'),
      });
    },
    destroy() {
      if (confirm('Are you sure you want to delete this bank?')) {
        this.$inertia.delete(route('banks.destroy', this.bank.id));
      }
    },
  },
  computed: {
    currencyOptionValues() {
      return this.currencies.map(currency => ({ ...currency, value: currency.name }));
    },
    selectedCurrencyOptionValue() {
      return this.currencyOptionValues.find(option => option.id.toString() === this.form.currency);
    }
  }
};
</script>
