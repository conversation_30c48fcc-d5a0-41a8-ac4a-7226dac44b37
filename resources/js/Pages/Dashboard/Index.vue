<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`Dashboard`" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Multi-Select -->
          <div v-if="isMerchant" class="space-y-2">
            <MultiSelectCombobox
              v-model="selectedStoreIds"
              :options="storeOptions"
              placeholder="Select Stores"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Date Range Picker with improved mobile responsiveness -->
          <div v-if="isMerchant" class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate date-range-button ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Center the calendar properly -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker with improved mobile layout -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <div v-if="isMerchant" class="md:col-span-2 flex justify-end space-x-4">
            <Button @click="resetFilters" class="min-w-[100px]" variant="outline">Reset</Button>
            <Button @click="search" class="min-w-[100px]">Search</Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Dashboard</h1>
        <div v-if="isMerchant" class="flex gap-2">
          <Button @click="exportData" class="min-w-[100px]" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </Button>
        </div>
      </div>

      <div v-if="isMerchant" class="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        <Card class="cursor-pointer hover:shadow-lg hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 transform hover:scale-105" @click="navigateToNewMembers">
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium hover:text-blue-600 transition-colors">
              New Member
            </CardTitle>
            <ChevronRight class="h-4 w-4 text-gray-400 hover:text-blue-600 transition-colors" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              {{ statistics.totalRegistrations }}
            </div>
          </CardContent>
        </Card>

        <Card class="cursor-pointer hover:shadow-lg hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 transform hover:scale-105" @click="navigateToActiveUsers">
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium hover:text-blue-600 transition-colors">
              Active Users
            </CardTitle>
            <ChevronRight class="h-4 w-4 text-gray-400 hover:text-blue-600 transition-colors" />
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              {{ statistics.totalActiveUsers }}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              First-Time Deposit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.totalFTD.sum }} ({{ statistics.totalFTD.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Deposit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.totalDeposits.sum }} ({{ statistics.totalDeposits.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Withdrawal
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.totalWithdrawals.sum }} ({{ statistics.totalWithdrawals.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Nett Deposit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.totalNettDeposits }}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Transfer In
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.transferDetails.in.sum }} ({{ statistics.transferDetails.in.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Transfer Out
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.transferDetails.out.sum }} ({{ statistics.transferDetails.out.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Nett Transfer
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.totalTransfer.sum }}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Wallet To Card
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.walletToCard.sum }} ({{ statistics.walletToCard.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Card To Wallet
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.cardToWallet.sum }} ({{ statistics.cardToWallet.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              P2P Transfer In
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.p2pTransferIn.sum }} ({{ statistics.p2pTransferIn.count }})
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Turnover
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.totalTurnover.sum }} ({{ statistics.totalTurnover.count }})
            </div>
          </CardContent>
        </Card>

        <Card v-if="isAdmin">
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Advance Credit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              RM {{ statistics.totalAdvanceCredit?.sum || '0.00' }} ({{ statistics.totalAdvanceCredit?.count || 0 }})
            </div>
          </CardContent>
        </Card>

        <!-- <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Total Win
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              {{ statistics.totalWinLoss.win }}
            </div>
          </CardContent>
        </Card> -->

        <!-- <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium">
              Total Loss
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div class="text-2xl font-bold">
              {{ statistics.totalWinLoss.loss }}
            </div>
          </CardContent>
        </Card> -->
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Head } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/Components/ui/card'
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/Components/ui/popover';
import {
  Check,
  Calendar as CalendarIcon,
  ChevronRight,
  Loader2
} from 'lucide-vue-next';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { Button as UIButton } from '@/Components/ui/button';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import { Button } from '@/Components/ui/button';
import pickBy from 'lodash/pickBy';
import {
  CalendarDate,
  DateFormatter,
  getLocalTimeZone,
} from '@internationalized/date';
import { CommandItem } from '@/Components/ui/command';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import MultiSelectCombobox from '@/Components/ui/multi-select-combobox.vue';

export default {
  components: {
    Head,
    Breadcrumb,
    Card, CardContent, CardDescription, CardHeader, CardTitle,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    ChevronRight,
    RangeCalendar,
    UIButton,
    SearchFilter,
    Label,
    Button,
    CommandItem,
    Check,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectValue,
    SelectTrigger,
    Separator,
    Loader2,
    MultiSelectCombobox
  },
  props: {
    startDate: String,
    endDate: String,
    auth: Object,
    statistics: Object,
    store_id: [String, Number, null],
  },
  methods: {
    search() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      // Get store ID from the helper method
      const storeId = this.getStoreIdParam();

      // Update filters.store_id for consistency
      this.filters.store_id = storeId === null ? 'all' : storeId;

      const params = {
        store_id: storeId,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      };

      this.$inertia.get('/', pickBy(params), {
        preserveState: true,
        preserveScroll: true,
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },

    async loadStores() {
      try {
        const response = await axios.get('/stores/all');
        if (response.data && Array.isArray(response.data)) {
          // Ensure each store has a string ID
          this.stores = response.data.map(store => ({
            ...store,
            id: store.id.toString() // Ensure ID is a string
          }));

          // If we have a store_id in the URL but it's not in the selectedStoreIds, add it
          if (this.store_id && !this.selectedStoreIds.includes(this.store_id.toString())) {
            this.selectedStoreIds = [this.store_id.toString()];
          }
        } else {
          console.error('Invalid store data format:', response.data);
        }
      } catch (error) {
        console.error('Failed to load stores:', error);
      }
    },
    async exportData() {
      this.isExporting = true;

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      // Get store ID from the helper method
      const storeId = this.getStoreIdParam();

      const params = new URLSearchParams({
        store_id: storeId || '',
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      });

      try {
        // Use fetch to get the file
        const response = await fetch(`/dashboard/export?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        // Get the file as blob
        const blob = await response.blob();

        // Create object URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create a temporary link element and trigger download
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        // Get the filename from the Content-Disposition header
        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        }

        document.body.appendChild(a);
        a.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        // Set isExporting back to false after download completes or fails
        this.isExporting = false;
      }
    },
    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    resetFilters() {
      // Reset store selection to 'all'
      this.selectedStoreIds = ['all'];

      // Reset date range to today
      const today = new Date();
      this.dateRange = {
        start: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate()),
        end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
      };

      // Reset time to beginning and end of day
      this.startTime = {
        hour: "00",
        minute: "00"
      };

      this.endTime = {
        hour: "23",
        minute: "59"
      };

      // Navigate to dashboard with default parameters
      this.$inertia.visit('/', {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },

    // Helper method to get store ID parameter from selected store IDs
    getStoreIdParam() {
      // If "all" is selected or no stores are selected, use null (all stores)
      if (this.selectedStoreIds.includes('all') || this.selectedStoreIds.length === 0) {
        // If "all" is selected along with other stores, keep only "all"
        if (this.selectedStoreIds.includes('all') && this.selectedStoreIds.length > 1) {
          this.$nextTick(() => {
            this.selectedStoreIds = ['all'];
          });
        }
        return null;
      }
      // If only one store is selected (and it's not "all"), use that store ID
      else if (this.selectedStoreIds.length === 1) {
        return this.selectedStoreIds[0];
      }
      // If multiple stores are selected, join them with commas
      else {
        return this.selectedStoreIds.join(',');
      }
    },

    // Navigate to New Members page with current filters
    navigateToNewMembers() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = {
        store_id: this.getStoreIdParam(),
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      };

      this.$inertia.visit('/new-members', {
        data: pickBy(params),
        method: 'get'
      });
    },

    // Navigate to Active Users page with current filters
    navigateToActiveUsers() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = {
        store_id: this.getStoreIdParam(),
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      };

      this.$inertia.visit('/active-users', {
        data: pickBy(params),
        method: 'get'
      });
    },
  },
  data() {
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    if (this.startDate && this.startDate.includes(' ')) {
      const timePart = this.startDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        startHour = timeComponents[0];
        startMinute = timeComponents[1];
      }
    }

    if (this.endDate && this.endDate.includes(' ')) {
      const timePart = this.endDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        endHour = timeComponents[0];
        endMinute = timeComponents[1];
      }
    }

    const startDateObj = new Date(this.startDate);
    const endDateObj = new Date(this.endDate);

    return {
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      dateRange: {
        start: new CalendarDate(
          startDateObj.getFullYear(),
          startDateObj.getMonth() + 1,
          startDateObj.getDate()
        ),
        end: new CalendarDate(
          endDateObj.getFullYear(),
          endDateObj.getMonth() + 1,
          endDateObj.getDate()
        )
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
      filters: {
        store_id: this.store_id ? this.store_id.toString() : 'all',
      },
      stores: [],
      selectedStoreIds: this.store_id ? [this.store_id.toString()] : ['all'],
      isExporting: false,
    };
  },
  computed: {
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        dateResult = this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()));

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        dateResult = `${this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()))} ${this.startTime.hour}:${this.startTime.minute} - ${this.df.format(this.dateRange.end?.toDate(getLocalTimeZone()))} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },

    isMerchant() {
      return this.auth.user.roles.includes('merchant') || this.auth.user.roles.includes('admin');
    },

    isAdmin() {
      return this.auth.user.roles.includes('admin');
    },

    storeOptionValues() {
      return this.stores.map(store => ({ ...store, value: store.name }));
    },

    storeOptions() {
      // Add "All Stores" option
      const allStoresOption = { value: 'all', label: 'All Stores' };

      // Map store data to the format expected by MultiSelectCombobox
      const storeOptions = this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));

      // Return combined options with "All Stores" first
      return [allStoresOption, ...storeOptions];
    }
  },
  watch: {
    // Watch for changes in selectedStoreIds and update filters.store_id
    selectedStoreIds: {
      handler() {
        // Get store ID from the helper method
        const storeId = this.getStoreIdParam();

        // Update filters.store_id for consistency
        this.filters.store_id = storeId === null ? 'all' : storeId;
      },
      deep: true
    }
  },
  mounted() {
    this.loadStores();

    // Debug log for store options
    console.log('Initial selectedStoreIds:', this.selectedStoreIds);
    setTimeout(() => {
      console.log('Stores loaded:', this.stores);
      console.log('Store options:', this.storeOptions);
    }, 1000);
  },
  layout: Layout,
};
</script>
