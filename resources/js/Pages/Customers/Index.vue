<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Customers" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter v-model="form.search" @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Trashed:</Label>
              <Select v-model="form.trashed" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Please Select Trash" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="with">
                      With Trashed
                    </SelectItem>
                    <SelectItem value="only">
                      Only Trashed
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Customers</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <Button label="Create" icon="plus" class="cursor-pointer" @click="createCustomer" />
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create Customer</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateCustomer">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-4 max-h-[75vh]">
                  <TextInput v-model="dialog.name" :error="errors.name" label="Customer Name" />
                  <TextInput v-model="dialog.code" :error="errors.code" label="Customer Code" />
                  <TextInput v-model="dialog.display_rate" :error="errors.display_rate" label="Display Rate" />
                  <TextInput v-model="dialog.credit_limit" :error="errors.credit_limit" label="Credit Limit" />
                  <SelectInput v-model="agentSelectIsOpen" :error="errors.agent_id" label="Agent"
                    :option-values="agentOptionValues" :value="dialog.agent_id"
                    popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedAgentOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedAgentOptionValue?.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ selectedAgentOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.agent_id
                          ? selectedAgentOptionValue?.value
                          : `Select Agent` }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in agentOptionValues" :key="option.value" :value="option.value" @select="(ev) => {
                      if (typeof ev.detail.value === 'string') {
                        dialog.agent_id = ev.detail.value
                      }
                      dialog.agent_id = option.id.toString()
                      agentSelectIsOpen = false
                    }">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        dialog.agent_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                      ]" />
                    </CommandItem>
                  </SelectInput>
                  <SelectInput v-model="referralSelectIsOpen" :error="errors.referral_id" label="Referral"
                    :option-values="referralOptionValues" :value="dialog.referral_id"
                    popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedReferralOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedReferralOptionValue?.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ selectedReferralOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.referral_id
                          ? selectedReferralOptionValue?.value
                          : `Select Referral` }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in referralOptionValues" :key="option.value" :value="option.value"
                      @select="(ev) => {
                        if (typeof ev.detail.value === 'string') {
                          dialog.referral_id = ev.detail.value
                        }
                        dialog.referral_id = option.id.toString()
                        referralSelectIsOpen = false
                      }">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        dialog.referral_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                      ]" />
                    </CommandItem>
                  </SelectInput>
                  <TextareaInput v-model="dialog.remarks" :error="errors.remarks" label="Remarks" />
                  <SwitchInput v-model="dialog.is_active" :error="errors.is_active" label="Status">
                    <Label>{{ dialog.is_active === true ? 'Active' : 'Inactive' }}</Label>
                  </SwitchInput>
                </div>
              </div>
              <DialogFooter class="px-2">
                <Button type="submit" label="Create Customer" />
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="cursor-pointer" @click="changeSort('name')">
              <SortableHeader title="Name" field="name" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead>Code</TableHead>
            <TableHead>Display Rate</TableHead>
            <TableHead>Credit Limit</TableHead>
            <TableHead>Credit Amount</TableHead>
            <TableHead>Agent</TableHead>
            <TableHead>Referral</TableHead>
            <TableHead>Status</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          <TableRow v-for="(customer, index) in customers.data" :key="customer.id"
            :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('customers.edit', customer.id)">
              {{ customer.name }}
              <Icon v-if="customer.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('customers.edit', customer.id)">
              {{ customer.code }}
              <Icon v-if="customer.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>{{ customer.display_rate }}</TableCell>
            <TableCell>{{ customer.credit_limit }}</TableCell>
            <TableCell>{{ customer.credit_amount }}</TableCell>
            <TableCell>{{ customer.agent?.name }}</TableCell>
            <TableCell>{{ customer.referral?.name }}</TableCell>
            <TableCell>
              <span class="px-3 py-1 text-xs font-medium rounded-full"
                :class="customer.is_active === true ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ customer.is_active === true ? 'Active' : 'Inactive' }}
              </span>
            </TableCell>
            <TableCell>{{ customer.created_at }}</TableCell>
            <TableCell>
              <Link :href="route('customers.edit', customer.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
          </TableRow>
          <TableRow v-if="customers.data.length === 0">
            <TableCell class="text-center border-0" colspan="11">No customers found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="customers" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import TextareaInput from '@/Shared/TextareaInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Icon from '@/Shared/Icon.vue';
import Pagination from '@/Shared/Pagination.vue';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { CommandItem } from '@/Components/ui/command';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    TextareaInput,
    SwitchInput,
    Table,
    TableBody,
    TableHeader,
    TableRow,
    TableCell,
    TableHead,
    SortableHeader,
    Icon,
    Pagination,
    Info,
    Tooltip,
    Breadcrumb,
    Avatar,
    AvatarFallback,
    AvatarImage,
    CommandItem,
    Check
  },
  layout: Layout,
  props: {
    filters: Object,
    customers: Object,
    sort: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        trashed: this.filters.trashed,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      dialog: {
        name: '',
        code: '',
        display_rate: '',
        credit_limit: '',
        agent_id: '',
        referral_id: '',
        remarks: '',
        is_active: true,
      },
      agents: [],
      referrals: [],
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Customers', link: route('customers'), is_active: true },
      ],
      agentSelectIsOpen: false,
      referralSelectIsOpen: false,
    };
  },
  watch: {
    form: {
      deep: true,
      handler: throttle(function () {
        const params = {};
        Object.keys(this.form).forEach((key) => {
          if (
            this.form[key] !== null &&
            this.form[key] !== undefined &&
            this.form[key] !== this.defaultValues[key]
          ) {
            params[key] = this.form[key];
          }
        });

        this.$inertia.get('/customers', pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ['customers'],
          replace: true,
        });
      }, 150),
    },
  },
  mounted() {
    this.loadAgents();
    this.loadReferrals();
  },
  methods: {
    async loadAgents() {
      try {
        const response = await axios.get('/users/all');
        this.agents = response.data;
      } catch (error) {
        console.error('Failed to load agents:', error);
      }
    },
    async loadReferrals() {
      try {
        const response = await axios.get('/customers/all');
        this.referrals = response.data;
      } catch (error) {
        console.error('Failed to load referrals:', error);
      }
    },
    reset() {
      this.form = {
        search: null,
        trashed: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.get('/customers', {}, {
        preserveState: true,
        preserveScroll: true,
        only: ['customers'],
        replace: true,
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },
    createCustomer() {
      this.dialog.name = '';
      this.dialog.code = '';
      this.dialog.display_rate = '';
      this.dialog.credit_limit = '';
      this.dialog.agent_id = '';
      this.dialog.referral_id = '';
      this.dialog.remarks = '';
      this.dialog.is_active = true;
      this.createDialogIsOpen = true;
    },
    submitCreateCustomer() {
      this.$inertia.post(route('customers.store'), {
        name: this.dialog.name,
        code: this.dialog.code,
        display_rate: this.dialog.display_rate,
        credit_limit: this.dialog.credit_limit,
        agent_id: this.dialog.agent_id,
        referral_id: this.dialog.referral_id,
        remarks: this.dialog.remarks,
        is_active: this.dialog.is_active,
      }, {
        preserveScroll: true,
        onSuccess: () => {
          this.dialog.name = '';
          this.dialog.code = '';
          this.dialog.display_rate = '';
          this.dialog.credit_limit = '';
          this.dialog.agent_id = '';
          this.dialog.referral_id = '';
          this.dialog.remarks = '';
          this.dialog.is_active = true;
          this.errors = {};
          this.createDialogIsOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        },
      });
    },
  },
  computed: {
    agentOptionValues() {
      return this.agents.map(agent => ({ ...agent, value: agent.name }));
    },
    selectedAgentOptionValue() {
      return this.agentOptionValues.find(option => option.id.toString() === this.dialog.agent_id);
    },
    referralOptionValues() {
      return this.referrals.map(referral => ({ ...referral, value: referral.name }));
    },
    selectedReferralOptionValue() {
      return this.referralOptionValues.find(option => option.id.toString() === this.dialog.referral_id);
    }
  }
};
</script>
