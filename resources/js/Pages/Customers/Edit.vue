<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center max-w-3xl gap-4 flex-wrap mb-4">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">{{ form.is_active === true ? 'Active' :
          'Inactive'
        }}</span>
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
    </div>
    <div class="mb-6">
      <Tabs default-value="user-details">
        <TabsList class="max-w-[700px] md:grid-cols-4">
          <TabsTrigger value="user-details">
            User Details
          </TabsTrigger>
          <TabsTrigger value="yunpian-messages">
            Yunpian Messages
          </TabsTrigger>
          <TabsTrigger value="audits">
            Audits
          </TabsTrigger>
          <TabsTrigger value="devices">
            Devices
          </TabsTrigger>
        </TabsList>
        <TabsContent value="user-details"
          class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <div>
            <Card>
              <CardContent class="p-0">
                <div class="p-4">
                  <form @submit.prevent="">
                    <div class="xl:columns-2 space-y-4 mb-5">
                      <div class="break-inside-avoid">
                        <SwitchInput label="Status" label-side>
                          <Label>Inactive</Label>
                        </SwitchInput>
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Name" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Agent" value="HH" />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Group" value="itdev" />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Code" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Default Password" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Phone No." label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Credit Limit Amount (RM)" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Credit Amount (RM)" value="0.00" />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Referral" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextareaInput label-side label="Remarks" />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput label="Display Rate" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Special Code" value="veLQA2JhGm" />
                      </div>
                    </div>
                    <div class="flex items-center justify-end gap-4 flex-wrap">
                      <Button label="Save" />
                    </div>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="yunpian-messages">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Yunpian Messages</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Created At</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Phone No</TableHead>
                  <TableHead>SMS Body</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Result</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow class="bg-[#F1F5F9]">
                  <TableCell colspan="8">No Record</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="audits">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Audits</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Created At</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Audited Changes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow class="bg-[#F1F5F9]">
                  <TableCell colspan="4">No Record</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="devices">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Devices</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Model</TableHead>
                  <TableHead>Platform</TableHead>
                  <TableHead>Mobile No</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow class="bg-[#F1F5F9]">
                  <TableCell colspan="4">No Record</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Currency Orders</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Reference</TableHead>
            <TableHead>Order Type</TableHead>
            <TableHead>Currency In</TableHead>
            <TableHead>Currency Out</TableHead>
            <TableHead>Receivable Amount (Fulfilled Amount)</TableHead>
            <TableHead>Exchange Rate</TableHead>
            <TableHead>Payment Amount (Fulfilled Amount)</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow class="bg-[#F1F5F9]">
            <TableCell colspan="10">No Record</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Transactions</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Reference</TableHead>
            <TableHead>Transaction Type</TableHead>
            <TableHead>Debit / Credit</TableHead>
            <TableHead>Currency</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Bank</TableHead>
            <TableHead>Bank Balance</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow class="bg-[#F1F5F9]">
            <TableCell colspan="11">No Record</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Referrals</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Mobile No</TableHead>
            <TableHead>Point Earned</TableHead>
            <TableHead>Currency</TableHead>
            <TableHead>Last Point Earned Date</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow class="bg-[#F1F5F9]">
            <TableCell colspan="7">No Record</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Coins</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Expiry Date</TableHead>
            <TableHead>Currency Order</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Used</TableHead>
            <TableHead>Balance</TableHead>
            <TableHead>Validity (Days)</TableHead>
            <TableHead>Message</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow class="bg-[#F1F5F9]">
            <TableCell colspan="10">No Record</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div>
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <form @submit.prevent="update">
              <div class="grid md:grid-cols-2 gap-4 mb-4">
                <TextInput v-model="form.name" :error="form.errors.name" label="Customer Name" />
                <TextInput v-model="form.code" :error="form.errors.code" label="Customer Code" />
                <TextInput v-model="form.display_rate" :error="form.errors.display_rate" label="Display Rate" />
                <TextInput v-model="form.credit_limit" :error="form.errors.credit_limit" label="Credit Limit" />
                <SelectInput v-model="agentSelectIsOpen" :error="form.errors.agent_id" label="Agent"
                  :option-values="agentOptionValues" :value="form.agent_id"
                  popover-content-class="w-[701px] max-w-[calc(100vw-4rem)] md:w-[701px] md:max-w-[calc((100vw-219px-80px)/2)] lg:max-w-[calc((100vw-279px-96px)/2)]">
                  <template #selected>
                    <div class="flex items-center gap-2">
                      <Avatar v-if="selectedAgentOptionValue?.photo" class="size-6">
                        <AvatarImage :src="selectedAgentOptionValue?.photo" alt="Currency Photo" />
                        <AvatarFallback>{{ selectedAgentOptionValue?.name }}</AvatarFallback>
                      </Avatar>
                      <span>{{ form.agent_id
                        ? selectedAgentOptionValue?.value
                        : `Select Agent` }}</span>
                    </div>
                  </template>
                  <CommandItem v-for="option in agentOptionValues" :key="option.value" :value="option.value" @select="(ev) => {
                    if (typeof ev.detail.value === 'string') {
                      form.agent_id = ev.detail.value
                    }
                    form.agent_id = option.id.toString()
                    agentSelectIsOpen = false
                  }">
                    <div class="flex items-center gap-2">
                      <Avatar v-if="option.photo" class="size-6">
                        <AvatarImage :src="option.photo" alt="Currency Photo" />
                        <AvatarFallback>{{ option.name }}</AvatarFallback>
                      </Avatar>
                      <span>{{ option.value }}</span>
                    </div>
                    <Check class="ml-auto h-4 w-4" :class="[
                      form.agent_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                    ]" />
                  </CommandItem>
                </SelectInput>
                <SelectInput v-model="referralSelectIsOpen" :error="form.errors.referral_id" label="Referral"
                  :option-values="referralOptionValues" :value="form.referral_id"
                  popover-content-class="w-[701px] max-w-[calc(100vw-4rem)] md:w-[701px] md:max-w-[calc((100vw-219px-80px)/2)] lg:max-w-[calc((100vw-279px-96px)/2)]">
                  <template #selected>
                    <div class="flex items-center gap-2">
                      <Avatar v-if="selectedReferralOptionValue?.photo" class="size-6">
                        <AvatarImage :src="selectedReferralOptionValue?.photo" alt="Currency Photo" />
                        <AvatarFallback>{{ selectedReferralOptionValue?.name }}</AvatarFallback>
                      </Avatar>
                      <span>{{ form.agent_id
                        ? selectedReferralOptionValue?.value
                        : `Select Referral` }}</span>
                    </div>
                  </template>
                  <CommandItem v-for="option in referralOptionValues" :key="option.value" :value="option.value" @select="(ev) => {
                    if (typeof ev.detail.value === 'string') {
                      form.referral_id = ev.detail.value
                    }
                    form.referral_id = option.id.toString()
                    referralSelectIsOpen = false
                  }">
                    <div class="flex items-center gap-2">
                      <Avatar v-if="option.photo" class="size-6">
                        <AvatarImage :src="option.photo" alt="Currency Photo" />
                        <AvatarFallback>{{ option.name }}</AvatarFallback>
                      </Avatar>
                      <span>{{ option.value }}</span>
                    </div>
                    <Check class="ml-auto h-4 w-4" :class="[
                      form.referral_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                    ]" />
                  </CommandItem>
                </SelectInput>
                <TextareaInput v-model="form.remarks" :error="form.errors.remarks" label="Remarks" />
                <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status">
                  <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                </SwitchInput>
              </div>
              <div class="flex items-center justify-end gap-4 flex-wrap">
                <Button v-if="!customer.deleted_at" variant="destructive" @click="destroy" type="button"
                  label="Delete Customer" />
                <Button type="submit" label="Update Customer" :loading="form.processing" />
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import TextInput from '@/Shared/TextInput.vue';
import TextareaInput from '@/Shared/TextareaInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import LoadingButton from '@/Shared/LoadingButton.vue';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { SelectItem } from '@/Components/ui/select';
import FileInput from '@/Shared/FileInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Label } from '@/Components/ui/label';
import Button from '@/Shared/Button.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { CommandItem } from '@/Components/ui/command';
import { Check } from 'lucide-vue-next';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { Button as UIButton } from '@/Components/ui/button';
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Separator } from '@/Components/ui/separator';

export default {
  components: {
    Head,
    Link,
    LoadingButton,
    TextInput,
    TextareaInput,
    SelectInput,
    Label,
    Button,
    SwitchInput,
    FileInput,
    SelectItem,
    Card,
    CardContent,
    Breadcrumb,
    Avatar,
    AvatarFallback,
    AvatarImage,
    CommandItem,
    Check,
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
    Table,
    TableRow,
    TableCell,
    TableHead,
    TableHeader,
    TableBody,
    DisplayLabel,
    UIButton,
    Separator
  },
  layout: Layout,
  props: {
    customer: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.customer.name,
        code: this.customer.code,
        display_rate: this.customer.display_rate,
        credit_limit: this.customer.credit_limit,
        agent_id: this.customer.agent_id?.toString(),
        referral_id: this.customer.referral_id?.toString(),
        remarks: this.customer.remarks || '',
        is_active: this.customer.is_active,
      }),
      agents: [],
      referrals: [],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Customers', link: route('customers') },
        { name: 'Edit', link: route('customers.edit', this.customer.id), is_active: true },
      ],
      agentSelectIsOpen: false,
      referralSelectIsOpen: false,
    };
  },
  mounted() {
    this.loadAgents();
    this.loadReferrals();
  },
  methods: {
    async loadAgents() {
      try {
        const response = await axios.get('/users/all');
        this.agents = response.data;
      } catch (error) {
        console.error('Failed to load agents:', error);
      }
    },
    async loadReferrals() {
      try {
        const response = await axios.get('/customers/all');
        this.referrals = response.data;
      } catch (error) {
        console.error('Failed to load referrals:', error);
      }
    },
    update() {
      this.form.post(route('customers.update', this.customer.id));
    },
    destroy() {
      if (confirm('Are you sure you want to delete this customer?')) {
        this.$inertia.delete(route('customers.destroy', this.customer.id));
      }
    },
  },
  computed: {
    agentOptionValues() {
      return this.agents.map(agent => ({ ...agent, value: agent.name }));
    },
    selectedAgentOptionValue() {
      return this.agentOptionValues.find(option => option.id.toString() === this.form.agent_id);
    },
    referralOptionValues() {
      return this.referrals.map(referral => ({ ...referral, value: referral.name }));
    },
    selectedReferralOptionValue() {
      return this.referralOptionValues.find(option => option.id.toString() === this.form.referral_id);
    }
  }
};
</script>
