<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Create Customer" />
    <h1 class="mb-8 text-3xl font-bold">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('customers')">Customers</Link>
      <span class="text-indigo-400 font-medium">/</span> Create
    </h1>
    <div class="flex justify-center">
      <div class="w-full max-w-5xl bg-white rounded-md shadow overflow-hidden">
        <form @submit.prevent="store">
          <div class="flex flex-wrap -mb-8 -mr-6 p-8">
            <text-input v-model="form.name" :error="form.errors.name" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Customer Name" />
            <text-input v-model="form.code" :error="form.errors.code" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Customer Code" />
            <text-input v-model="form.display_rate" :error="form.errors.display_rate" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Display Rate" />
            <text-input v-model="form.credit_limit" :error="form.errors.credit_limit" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Credit Limit" />
            <select-input v-model="form.agent_id" :error="form.errors.agent_id" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Agent">
              <option v-for="agent in agents" :key="agent.id" :value="agent.id">{{ agent.name }}</option>
            </select-input>
            <select-input v-model="form.referral_id" :error="form.errors.referral_id" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Referral">
              <option v-for="referral in referrals" :key="referral.id" :value="referral.id">{{ referral.name }}</option>
            </select-input>
            <textarea-input v-model="form.remarks" :error="form.errors.remarks" class="pb-8 pr-6 w-full"
              label="Remarks" />
            <div class="pb-8 pr-6 w-full lg:w-1/2">
              <label class="block text-sm font-medium text-gray-700">Status</label>
              <label class="switch mt-2">
                <input type="checkbox" v-model="form.is_active" :true-value=true :false-value=false />
                <span class="slider round"></span>
              </label>
              <span class="ml-2 text-sm text-gray-600">
                {{ form.is_active === true ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
          <div class="flex items-center justify-end px-8 py-4 bg-gray-50 border-t border-gray-100">
            <loading-button :loading="form.processing" class="btn-indigo" type="submit">
              Create Customer
            </loading-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3'
import Layout from '@/Shared/Layout.vue'
import TextInput from '@/Shared/TextInput.vue'
import TextareaInput from '@/Shared/TextareaInput.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import LoadingButton from '@/Shared/LoadingButton.vue'
import axios from 'axios';

export default {
  components: {
    Head,
    Link,
    LoadingButton,
    TextInput,
    TextareaInput,
    SelectInput,
  },
  layout: Layout,
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        name: '',
        code: '',
        display_rate: '',
        credit_limit: '',
        agent_id: '',
        referral_id: '',
        is_active: true,
      }),
      agents: [],
      referrals: [],
    };
  },
  mounted() {
    this.loadAgents();
    this.loadReferrals();
  },
  methods: {
    async loadAgents() {
      try {
        const response = await axios.get('/users/all');
        this.agents = response.data;
      } catch (error) {
        console.error('Failed to load agents:', error);
      }
    },
    async loadReferrals() {
      try {
        const response = await axios.get('/customers/all');
        this.referrals = response.data;
      } catch (error) {
        console.error('Failed to load referrals:', error);
      }
    },
    store() {
      this.form.post(route('customers.store'));
    },
  },
};
</script>
