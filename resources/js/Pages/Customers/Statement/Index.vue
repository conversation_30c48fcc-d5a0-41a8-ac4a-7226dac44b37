<template>
    <div class="p-4 lg:gap-6 lg:p-6">

        <Head title="Customer Statement" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        <Card>
            <CardContent class="p-0">
                <div class="p-4">
                    <SearchFilter v-model="form.search" @reset="reset">
                        <div>
                            <Label class="mb-2 inline-block">Date Range:</Label>
                            <Popover>
                                <PopoverTrigger as-child>
                                    <UIButton variant="outline"
                                        :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!value ? 'text-muted-foreground' : ''}`">
                                        <CalendarIcon class="mr-2 h-4 w-4" />
                                        {{ formattedDateResult }}
                                    </UIButton>
                                </PopoverTrigger>
                                <PopoverContent class="w-auto p-0">
                                    <RangeCalendar v-model="value" initial-focus :number-of-months="2"
                                        @update:start-value="(startDate) => value.start = startDate" />
                                </PopoverContent>
                            </Popover>
                        </div>
                        <div class="md:col-span-2 lg:col-span-3 xl:col-span-4 overflow-hidden">

                            <div class="overflow-x-auto">
                                <div class="w-fit border p-1 rounded-md">
                                    <div class="flex gap-1">
                                        <UIButton
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8">
                                            Today
                                        </UIButton>
                                        <UIButton
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8 ">
                                            Yesterday
                                        </UIButton>
                                        <UIButton
                                            class="text-sm bg-transparent !bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8">
                                            This Week
                                        </UIButton>
                                        <UIButton
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8">
                                            This Month
                                        </UIButton>
                                        <UIButton
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8">
                                            Last Week
                                        </UIButton>
                                        <UIButton
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8">
                                            Last Month
                                        </UIButton>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </SearchFilter>
                </div>
            </CardContent>
        </Card>
        <div>
            <Separator class="my-5" />
        </div>
        <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
            <h1 class="text-2xl font-bold">Customer Statement</h1>
            <div class="flex items-center gap-3 flex-wrap">
                <Button label="Export CSV" class="cursor-pointer" />
                <Button label="Export PDF" class="cursor-pointer" />
            </div>
        </div>
        <div class="bg-white overflow-x-auto mb-6">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>No</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead>Currency In</TableHead>
                        <TableHead>Currency Out</TableHead>
                        <TableHead>WEWE Buy</TableHead>
                        <TableHead>Exchange Rate</TableHead>
                        <TableHead>WEWE Sell</TableHead>
                        <TableHead>Balance (Buy)</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow class="bg-[#F1F5F9]">
                        <TableCell colspan="9">No Record</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import { Button as UIButton } from '@/Components/ui/button';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
    TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-vue-next';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import {
    CalendarDate,
    DateFormatter,
    getLocalTimeZone,
} from '@internationalized/date';

export default {
    components: {
        Head,
        Link,
        Card,
        CardContent,
        SearchFilter,
        Label,
        Select,
        SelectContent,
        SelectGroup,
        SelectItem,
        SelectTrigger,
        SelectValue,
        Separator,
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
        Button,
        TextInput,
        SelectInput,
        SwitchInput,
        Table,
        TableBody,
        TableCell,
        TableHead,
        TableHeader,
        TableRow,
        SortableHeader,
        Avatar,
        AvatarFallback,
        AvatarImage,
        Icon,
        Badge,
        Pagination,
        ShowEntries,
        Info,
        Tooltip,
        Breadcrumb,
        CommandItem,
        Check,
        Popover,
        PopoverContent,
        PopoverTrigger,
        CalendarIcon,
        RangeCalendar,
        UIButton
    },
    layout: Layout,
    props: {
        filters: Object,
        sort: Object,
    },
    data() {
        return {
            defaultValues: {
                sort: 'created_at',
                direction: 'desc',
                perPage: 10,
            },
            form: {
                search: this.filters?.search,
                trashed: this.filters?.trashed,
                sort: this.sort?.field ?? 'created_at',
                direction: this.sort?.direction ?? 'desc',
                perPage: this.filters?.perPage?.toString() || '10',
            },
            errors: {},
            breadcrumbs: [],
            df: new DateFormatter('en-US', {
                dateStyle: 'medium',
            }),
            value: {
                start: new CalendarDate(2023, 12, 20),
                end: new CalendarDate(2024, 1, 20)
            }
        };
    },
    methods: {
        reset() { },
        changeSort(field) {
            this.form.direction = this.form.sort === field
                ? this.form.direction === 'asc'
                    ? 'desc'
                    : 'asc'
                : 'asc';
            this.form.sort = field;
        },
    },
    computed: {
        formattedDateResult() {
            let dateResult = 'Pick a date';

            if (this.value.start) {
                dateResult = this.df.format(this.value.start.toDate(getLocalTimeZone()));
            }

            if (this.value.end) {
                dateResult = `${this.df.format(this.value.start.toDate(getLocalTimeZone()))} - ${this.df.format(this.value.end.toDate(getLocalTimeZone()))}`;
            }

            return dateResult;
        }
    }
};
</script>