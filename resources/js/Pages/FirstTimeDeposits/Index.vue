<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="First-Time Deposits" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <div class="flex items-center justify-between mb-5">
      <h1 class="text-2xl font-bold">First-Time Deposits</h1>
      <div class="flex gap-2">
        <Button @click="exportData" class="min-w-[100px]" :disabled="isExporting">
          <Loader2 v-if="isExporting" class="mr-2 h-4 w-4 animate-spin" />
          {{ isExporting ? 'Exporting...' : 'Export' }}
        </Button>
      </div>
    </div>

    <!-- First-Time Deposits Table -->
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>User ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Phone Number</TableHead>
            <TableHead>Store</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Approved Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(deposit, index) in firstTimeDeposits" :key="deposit.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ deposit.id }}</TableCell>
            <TableCell>{{ deposit.name }}</TableCell>
            <TableCell>{{ deposit.phone_no || '-' }}</TableCell>
            <TableCell>{{ deposit.store_name || '-' }}</TableCell>
            <TableCell>RM {{ formatAmount(deposit.amount) }}</TableCell>
            <TableCell>{{ formatDate(deposit.approved_at) }}</TableCell>
          </TableRow>
          <TableRow v-if="firstTimeDeposits.length === 0">
            <TableCell class="text-center border-0" colspan="6">No first-time deposits found for the selected period.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <!-- Pagination -->
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination v-if="pagination && pagination.total > 0" class="flex justify-end" :data="pagination" />
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Button } from '@/Components/ui/button';
import { Loader2 } from 'lucide-vue-next';
import { Table, TableBody, TableCell, TableHead, TableHeader } from '@/Shared/table';
import { TableRow } from '@/Components/ui/table';
import Pagination from '@/Shared/Pagination.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import pickBy from 'lodash/pickBy';

export default {
  components: {
    Head,
    Breadcrumb,
    Button,
    Loader2,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Pagination,
    ShowEntries
  },
  props: {
    firstTimeDeposits: Array,
    pagination: Object,
    startDate: String,
    endDate: String,
    store_id: [String, Number, null],
    perPage: [String, Number],
    auth: Object,
  },
  data() {
    return {
      breadcrumbs: [
        {
          title: 'Dashboard',
          href: '/'
        },
        {
          title: 'First-Time Deposits',
          href: '/first-time-deposits'
        }
      ],
      isExporting: false,
      form: {
        perPage: this.perPage ? this.perPage.toString() : '10',
      },
    };
  },
  watch: {
    'form.perPage'() {
      this.updatePerPage();
    }
  },
  methods: {
    updatePerPage() {
      const params = {
        store_id: this.store_id,
        startDate: this.startDate,
        endDate: this.endDate,
        perPage: this.form.perPage,
      };

      this.$inertia.get('/first-time-deposits', pickBy(params), {
        preserveState: true,
        preserveScroll: true,
        replace: true
      });
    },
    async exportData() {
      this.isExporting = true;

      const params = new URLSearchParams({
        store_id: this.store_id || '',
        startDate: this.startDate,
        endDate: this.endDate,
      });

      try {
        const response = await fetch(`/first-time-deposits/export?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          } else {
            a.download = 'first-time-deposits-export.xlsx';
          }
        } else {
          a.download = 'first-time-deposits-export.xlsx';
        }

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        return new Date(dateString).toLocaleString();
      } catch (e) {
        return dateString;
      }
    },
    formatAmount(amount) {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    }
  },
  layout: Layout,
};
</script>
