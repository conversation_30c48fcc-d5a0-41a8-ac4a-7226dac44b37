<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Roles" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter v-model="form.search" @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Trashed:</Label>
              <Select v-model="form.trashed" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Please Select Trash" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="with">
                      With Trashed
                    </SelectItem>
                    <SelectItem value="only">
                      Only Trashed
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Roles</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <Button label="Create" icon="plus" class="cursor-pointer" @click="createRole" />
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create Role</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateRole">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-8 max-h-[75vh]">
                  <TextInput v-model="dialog.name" :error="errors.name" label="Role Name" />
                  <div>
                    <table class="min-w-full bg-white">
                      <thead>
                        <tr>
                          <th class="py-2 text-left">Permission</th>
                          <th class="py-2 text-left"></th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="permission in permissions" :key="permission">
                          <td class="py-2">{{ permission }}</td>
                          <td class="py-2">
                            <label class="switch">
                              <input type="checkbox" :value="permission" v-model="dialog.permissions">
                              <span class="slider round"></span>
                            </label>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <DialogFooter class="px-2">
                <Button type="submit" label="Create Role" />
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="cursor-pointer" @click="changeSort('name')">
              <SortableHeader title="Name" field="name" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead>Permissions</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(role, index) in roles.data" :key="role.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>
              <Link class="flex items-cente focus:text-indigo-500" :href="route('roles.edit', role.id)">
              {{ role.name }}
              </Link>
            </TableCell>
            <TableCell>
              <div class="relative">
                <span class="truncate inline-block max-w-xs align-middle">
                  {{ role.permissions.slice(0, 3).join(', ') }}
                  <span v-if="role.permissions.length > 3">...</span>
                </span>
                <span v-if="role.permissions.length > 3"
                  class="absolute z-10 hidden px-2 py-1 bg-gray-800 text-white text-sm rounded-md shadow-md tooltip">
                  {{ role.permissions.join(', ') }}
                </span>
              </div>
            </TableCell>
            <TableCell>
              {{ role.created_at }}
            </TableCell>
            <TableCell>
              <Link :href="route('roles.edit', role.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
          </TableRow>
          <TableRow v-if="roles.data.length === 0">
            <TableCell class="text-center border-0" colspan="4">No roles found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="roles" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import TextInput from '@/Shared/TextInput.vue';
import Button from '@/Shared/Button.vue';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import { Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    TextInput,
    Button,
    SortableHeader,
    Pagination,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Info,
    Tooltip,
    Breadcrumb
  },
  layout: Layout,
  props: {
    filters: Object,
    roles: Object,
    permissions: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        trashed: this.filters.trashed,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      dialog: {
        name: '',
        permissions: [],
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Roles', link: route('roles'), is_active: true },
      ]
    };
  },
  watch: {
    form: {
      deep: true,
      handler: throttle(function () {
        const params = {};
        Object.keys(this.form).forEach(key => {
          if (this.form[key] !== null &&
            this.form[key] !== undefined &&
            this.form[key] !== this.defaultValues[key]) {
            params[key] = this.form[key];
          }
        });

        this.$inertia.get('/roles', pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ['roles'],
          replace: true
        });
      }, 150),
    },
  },
  methods: {
    reset() {
      this.form = {
        search: null,
        trashed: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.get('/roles', {}, {
        preserveState: true,
        preserveScroll: true,
        only: ['roles'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },
    createRole() {
      this.dialog.name = '';
      this.dialog.permissions = [];
      this.createDialogIsOpen = true;
    },
    submitCreateRole() {
      this.$inertia.post(route('roles.store'), {
        name: this.dialog.name,
        permissions: this.dialog.permissions,
      }, {
        preserveScroll: true,
        onSuccess: () => {
          this.dialog.name = '';
          this.dialog.permissions = [];
          this.createDialogIsOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        },
      });
    },
  },
};
</script>
