<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="Create Role" />
    <h1 class="mb-8 text-3xl font-bold">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('roles')">Roles</Link>
      <span class="text-indigo-400 font-medium">/</span> Create
    </h1>
    <div class="flex justify-center">
      <div class="w-full max-w-5xl bg-white rounded-md shadow overflow-hidden">
        <form @submit.prevent="store">
          <div class="flex flex-wrap -mb-8 -mr-6 p-8">
            <text-input v-model="form.name" :error="form.errors.name" class="pb-8 pr-6 w-full lg:w-1/2" label="Role Name" />
            <div class="pb-8 pr-6 w-full">
              <div class="mt-2">
                <table class="min-w-full bg-white">
                  <thead>
                    <tr>
                      <th class="py-2 text-left">Permission</th>
                      <th class="py-2 text-left"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="permission in permissions" :key="permission">
                      <td class="py-2">{{ permission }}</td>
                      <td class="py-2">
                        <label class="switch">
                          <input type="checkbox" :value="permission" v-model="form.permissions">
                          <span class="slider round"></span>
                        </label>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-if="form.errors.permissions" class="text-red-500 text-sm mt-1">
                {{ form.errors.permissions }}
              </div>
            </div>
          </div>
          <div class="flex items-center justify-end px-8 py-4 bg-gray-50 border-t border-gray-100">
            <loading-button :loading="form.processing" class="btn-indigo" type="submit">
              Create Role
            </loading-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3'
import Layout from '@/Shared/Layout.vue'
import TextInput from '@/Shared/TextInput.vue'
import LoadingButton from '@/Shared/LoadingButton.vue'

export default {
  components: {
    Head,
    Link,
    LoadingButton,
    TextInput,
  },
  layout: Layout,
  props: {
    permissions: {
      type: Array,
      required: true
    }
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        name: '',
        permissions: [],
      }),
    }
  },
  methods: {
    store() {
      this.form.post(route('roles.store'));
    },
  },
}
</script>