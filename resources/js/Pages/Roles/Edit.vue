<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="form.name" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start mb-4 max-w-3xl">
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
    </div>
    <div>
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <form @submit.prevent="update">
              <div class="grid gap-8">
                <TextInput v-model="form.name" :error="form.errors.name" label="Role Name" />
                <div>
                  <table class="min-w-full bg-white">
                    <thead>
                      <tr>
                        <th class="py-2 text-left">Permission</th>
                        <th class="py-2 text-left"></th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="permission in permissions" :key="permission">
                        <td class="py-2">{{ permission }}</td>
                        <td class="py-2">
                          <label class="switch">
                            <input type="checkbox" :value="permission" v-model="form.permissions">
                            <span class="slider round"></span>
                          </label>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="text-right">
                <Button type="submit" label="Update Role" :loading="form.processing" />
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import Button from '@/Shared/Button.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    Button,
    Breadcrumb
  },
  layout: Layout,
  props: {
    role: Object,
    permissions: {
      type: Array,
      required: true
    }
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.role.name,
        permissions: this.role.permissions || [],
      }),
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Roles', link: route('roles') },
        { name: 'Edit', link: route('roles.edit', this.role.id), is_active: true },
      ]
    };
  },
  methods: {
    update() {
      this.form.post(route('roles.update', this.role.id));
    },
    destroy() {
      if (confirm('Are you sure you want to delete this role?')) {
        this.$inertia.delete(route('roles.destroy', this.role.id));
      }
    },
  },
};
</script>
