<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Machine Report" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter buttonLabel="Search" searchLabel="Account" v-model="form.search" @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Date Range:</Label>
              <Popover>
                <PopoverTrigger as-child>
                    <UIButton variant="outline"
                        :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!dateRange ? 'text-muted-foreground' : ''}`">
                        <CalendarIcon class="mr-2 h-4 w-4" />
                        {{ formattedDateResult }}
                    </UIButton>
                </PopoverTrigger>

                <!-- TODO: Change to Date Time Picker --> 
                <PopoverContent class="w-auto p-0">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                        @update:start-value="(startDate) => dateRange.start = startDate" />
                </PopoverContent>
            </Popover>
          </div>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-5" />
    </div>

    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Machine Report</h1>
      <div>
      </div>
    </div>

    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Store</TableHead>
            <TableHead>Total In</TableHead>
            <TableHead>Total Out</TableHead>
            <TableHead>Total Profit</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          <TableRow v-for="(item, index) in data.items" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>
              {{ item.store }}
            </TableCell>
            
            <TableCell>
              {{ item.total_token_in }}
            </TableCell>

            <TableCell>
              {{ item.total_token_out }}
            </TableCell>

            <TableCell>
              {{ item.total_profit }}
            </TableCell>
          </TableRow>

          <TableRow v-if="data.items.length === 0">
            <TableCell class="text-center border-0" colspan="7">No record found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="page" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-vue-next';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { Button as UIButton } from '@/Components/ui/button';
import {
    CalendarDate,
    DateFormatter,
    getLocalTimeZone,
} from '@internationalized/date';
export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Icon,
    Badge,
    Pagination,
    ShowEntries,
    Info,
    Tooltip,
    Breadcrumb,
    CommandItem,
    Check,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    RangeCalendar,
    UIButton
  },
  layout: Layout,
  props: {
    filters: Object,
    sort: Object,
    startDate: String,
    endDate: String,
    data: Object,
    page: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      errors: {},
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Machine Report', link: route('reports.machine'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
                dateStyle: 'medium',
            }),
      dateRange: {
          start: new CalendarDate(new Date(this.startDate).getFullYear(), new Date(this.startDate).getMonth() + 1, new Date(this.startDate).getDate()),
          end: new CalendarDate(new Date(this.endDate).getFullYear(), new Date(this.endDate).getMonth() + 1, new Date(this.endDate).getDate())
      }
    };
  },
  mounted() {
  },
  watch: {
    //
  },
  methods: {
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },
    reset() {
      const params = {};
      Object.keys(this.form).forEach(key => {
        if (this.form[key] !== null &&
          this.form[key] !== undefined &&
          this.form[key] !== this.defaultValues[key]) {
          params[key] = this.form[key];
        }
      });
      params['page'] = 1;
      params['startDate'] = this.dateRange.start.toString();
      params['endDate'] = this.dateRange.end.toString();

      this.$inertia.get('/reports/machine', pickBy(params), {
        preserveState: true,
        preserveScroll: true,
        only: ['data', 'page', 'search', 'perPage', 'dateRange'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },
  },
  computed: {
    formattedDateResult() {
        let dateResult = 'Pick a date';

        if (this.dateRange.start) {
            dateResult = this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()));
        }

        if (this.dateRange.end) {
            dateResult = `${this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()))} - ${this.df.format(this.dateRange.end?.toDate(getLocalTimeZone()))}`;
        }

        return dateResult;
    }
  }
};
</script>