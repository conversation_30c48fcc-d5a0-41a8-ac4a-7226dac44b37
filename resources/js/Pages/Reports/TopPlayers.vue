<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Top 10 Players" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Multi-Select -->
          <div class="space-y-2">
            <MultiSelectCombobox
              v-model="selectedStoreIds"
              :options="storeOptions"
              placeholder="Select Stores"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Add Date Range Picker -->
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Responsive calendar - 1 month on mobile, 2 months on desktop -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- Limit Select -->
          <div class="space-y-2">
            <Label>Show Entries:</Label>
            <Select v-model="form.limit" class="w-full">
              <SelectTrigger>
                <SelectValue placeholder="Select Limit" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="10">10 entries</SelectItem>
                  <SelectItem value="30">30 entries</SelectItem>
                  <SelectItem value="50">50 entries</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <!-- Search and Reset Buttons -->
          <div class="md:col-span-2 flex justify-end space-x-4">
            <UIButton class="min-w-[100px]" type="button" variant="outline" @click="resetFilters">
              Reset
            </UIButton>
            <UIButton class="min-w-[100px]" type="button" @click="search">
              Search
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Top 10 Players</h1>
        <div class="flex gap-2">
          <UIButton class="min-w-[100px]" type="button" @click="exportData" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </UIButton>
        </div>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Store</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Phone Number</TableHead>
              <TableHead class="cursor-pointer" @click="setSortBy('deposits')">
                Deposit Amount
                <span v-if="form.sortBy === 'deposits'" class="ml-1">↓</span>
              </TableHead>
              <TableHead class="cursor-pointer" @click="setSortBy('withdrawals')">
                Withdrawal Amount
                <span v-if="form.sortBy === 'withdrawals'" class="ml-1">↓</span>
              </TableHead>
              <TableHead class="cursor-pointer" @click="setSortBy('turnover')">
                Total Turnover
                <span v-if="form.sortBy === 'turnover'" class="ml-1">↓</span>
              </TableHead>
              <TableHead class="cursor-pointer" @click="setSortBy('p2p')">
                P2P Transfer
                <span v-if="form.sortBy === 'p2p'" class="ml-1">↓</span>
              </TableHead>
              <TableHead>Last Active At</TableHead>
              <TableHead>Last Deposit At</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            <TableRow v-for="(player, index) in topPlayers" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ player.store_name }}</TableCell>
              <TableCell>{{ player.username }}</TableCell>
              <TableCell>{{ player.phone_no }}</TableCell>
              <TableCell>RM {{ formatAmount(player.deposit_amount) }}</TableCell>
              <TableCell>RM {{ formatAmount(player.withdrawal_amount) }}</TableCell>
              <TableCell>RM {{ formatAmount(player.turnover_amount) }}</TableCell>
              <TableCell>RM {{ formatAmount(player.p2p_amount) }}</TableCell>
              <TableCell>{{ formatDateTime(player.last_active_at) }}</TableCell>
              <TableCell>{{ formatDateTime(player.last_deposit_at) }}</TableCell>
            </TableRow>

            <TableRow v-if="topPlayers.length === 0">
              <TableCell class="text-center border-0" colspan="9">No record found.</TableCell>
            </TableRow>
          </TableBody>

          <!-- Add Table Footer for Grand Totals -->
          <tfoot v-if="topPlayers.length > 0" class="bg-gray-50 font-medium text-sm">
            <tr>
              <td class="px-2 py-1 text-center font-bold" colspan="3">Grand Total:</td>
              <td class="px-2 py-1 font-bold">RM {{ formatAmount(grandTotals.deposits) }}</td>
              <td class="px-2 py-1 font-bold">RM {{ formatAmount(grandTotals.withdrawals) }}</td>
              <td class="px-2 py-1 font-bold">RM {{ formatAmount(grandTotals.turnover) }}</td>
              <td class="px-2 py-1 font-bold">RM {{ formatAmount(grandTotals.p2p) }}</td>
              <td colspan="2"></td>
            </tr>
          </tfoot>
        </Table>
      </div>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Separator } from '@/Components/ui/separator';
import { Button as UIButton } from '@/Components/ui/button';
import { TableRow } from '@/Components/ui/table';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Loader2 } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { CalendarIcon } from 'lucide-vue-next';
import { CalendarDate } from '@internationalized/date';
import { getLocalTimeZone } from '@internationalized/date';
import MultiSelectCombobox from '@/Components/ui/multi-select-combobox.vue';

export default {
  components: {
    Head,
    Card,
    CardContent,
    Label,
    Separator,
    UIButton,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Breadcrumb,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Loader2,
    Popover,
    PopoverContent,
    PopoverTrigger,
    RangeCalendar,
    CalendarIcon,
    MultiSelectCombobox,
  },
  layout: Layout,
  props: {
    filters: Object,
    startDate: String,
    endDate: String,
    topPlayers: {
      type: Array,
      default: () => []
    },
    stores: {
      type: Array,
      default: () => []
    },
    sortBy: {
      type: String,
      default: 'deposits'
    }
  },
  data() {
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    const now = new Date();
    let startDateObj = this.startDate ? new Date(this.startDate) : now;
    let endDateObj = this.endDate ? new Date(this.endDate) : now;

    if (this.startDate && this.startDate.includes(' ')) {
      const timePart = this.startDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        startHour = timeComponents[0];
        startMinute = timeComponents[1];
      }
    }

    if (this.endDate && this.endDate.includes(' ')) {
      const timePart = this.endDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        endHour = timeComponents[0];
        endMinute = timeComponents[1];
      }
    }

    return {
      form: {
        sortBy: this.filters?.sortBy || this.sortBy,
        limit: this.filters?.limit || '10',
      },
      selectedStoreIds: this.filters?.store_id ?
        (this.filters.store_id === 'all' ? ['all'] : this.filters.store_id.split(',')) :
        ['all'],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Top Players', link: route('top-players'), is_active: true },
      ],
      isExporting: false,
      dateRange: {
        start: new CalendarDate(startDateObj.getFullYear(), startDateObj.getMonth() + 1, startDateObj.getDate()),
        end: new CalendarDate(endDateObj.getFullYear(), endDateObj.getMonth() + 1, endDateObj.getDate())
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
    }
  },
  computed: {
    grandTotals() {
      if (!this.topPlayers || this.topPlayers.length === 0) {
        return {
          deposits: 0,
          withdrawals: 0,
          turnover: 0,
          p2p: 0
        };
      }

      return {
        deposits: this.topPlayers.reduce((sum, player) => sum + parseFloat(player.deposit_amount || 0), 0),
        withdrawals: this.topPlayers.reduce((sum, player) => sum + parseFloat(player.withdrawal_amount || 0), 0),
        turnover: this.topPlayers.reduce((sum, player) => sum + parseFloat(player.turnover_amount || 0), 0),
        p2p: this.topPlayers.reduce((sum, player) => sum + parseFloat(player.p2p_amount || 0), 0)
      };
    },
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        const startDate = new Date(this.dateRange.start.toString());
        const options = { month: 'short', day: 'numeric', year: 'numeric' };
        dateResult = startDate.toLocaleDateString('en-US', options);

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        const startDate = new Date(this.dateRange.start.toString());
        const endDate = new Date(this.dateRange.end.toString());
        const options = { month: 'short', day: 'numeric', year: 'numeric' };

        dateResult = `${startDate.toLocaleDateString('en-US', options)} ${this.startTime.hour}:${this.startTime.minute} - ${endDate.toLocaleDateString('en-US', options)} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },
    storeOptions() {
      // Add "All Stores" option
      const allStoresOption = { value: 'all', label: 'All Stores' };

      // Map store data to the format expected by MultiSelectCombobox
      const storeOptions = this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));

      // Return combined options with "All Stores" first
      return [allStoresOption, ...storeOptions];
    }
  },
  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    formatDateToSQL(date) {
      // Make sure date exists before formatting
      if (!date) return null;

      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      
      const isStartDate = this.dateRange.start &&
        date.getFullYear() === this.dateRange.start.toDate(getLocalTimeZone()).getFullYear() &&
        date.getMonth() === this.dateRange.start.toDate(getLocalTimeZone()).getMonth() &&
        date.getDate() === this.dateRange.start.toDate(getLocalTimeZone()).getDate() &&
        date.getHours() === parseInt(this.startTime.hour) &&
        date.getMinutes() === parseInt(this.startTime.minute);

      const seconds = "00";

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    setSortBy(column) {
      this.form.sortBy = column;
      this.search();
    },
    // Helper method to get store ID parameter from selected store IDs
    getStoreIdParam() {
      // If "all" is selected or no stores are selected, use null (all stores)
      if (this.selectedStoreIds.includes('all') || this.selectedStoreIds.length === 0) {
        // If "all" is selected along with other stores, keep only "all"
        if (this.selectedStoreIds.includes('all') && this.selectedStoreIds.length > 1) {
          this.$nextTick(() => {
            this.selectedStoreIds = ['all'];
          });
        }
        return 'all';
      }
      // If only one store is selected (and it's not "all"), use that store ID
      else if (this.selectedStoreIds.length === 1) {
        return this.selectedStoreIds[0];
      }
      // If multiple stores are selected, join them with commas
      else {
        return this.selectedStoreIds.join(',');
      }
    },
    search() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute), 0);

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute), 59);

      const params = pickBy({
        store_id: this.getStoreIdParam(),
        sortBy: this.form.sortBy,
        limit: this.form.limit,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      });

      this.$inertia.get(route('top-players'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['topPlayers', 'filters'],
      });
    },
    resetFilters() {
      this.form = {
        sortBy: 'deposits',
        limit: '10',
      };

      this.selectedStoreIds = ['all'];

      const today = new Date();
      this.dateRange = {
        start: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate()),
        end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
      };

      this.startTime = {
        hour: "00",
        minute: "00"
      };

      this.endTime = {
        hour: "23",
        minute: "59"
      };

      this.$inertia.visit('/top-players', {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },
    async exportData() {
      this.isExporting = true;

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute), 0);

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute), 59);

      const params = new URLSearchParams({
        store_id: this.getStoreIdParam() === 'all' ? '' : this.getStoreIdParam(),
        sortBy: this.form.sortBy,
        limit: this.form.limit,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      });

      try {
        const exportUrl = route('top-players.export', Object.fromEntries(params));
        const response = await fetch(exportUrl);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        } else {
          a.download = 'top-players-export.xlsx';
        }

        document.body.appendChild(a);
        a.click();

        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },
    formatDateTime(dateTime) {
      if (!dateTime) return '-';
      return new Date(dateTime).toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    }
  },
  watch: {
    filters: {
      deep: true,
      handler(newFilters) {
        if (newFilters) {
          this.form.sortBy = newFilters.sortBy || this.form.sortBy;
          this.form.limit = newFilters.limit || '10';

          if (newFilters.store_id) {
            this.selectedStoreIds = newFilters.store_id === 'all'
              ? ['all']
              : newFilters.store_id.split(',');
          }
        }
      }
    },
    // Watch for changes in selectedStoreIds
    selectedStoreIds: {
      handler() {
        console.log('Selected store IDs changed:', this.selectedStoreIds);
      },
      deep: true
    }
  },
  mounted() {
    if (this.endTime.hour === "00" && this.endTime.minute === "00") {
      this.endTime.hour = "23";
      this.endTime.minute = "59";
    }
  }
}
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
