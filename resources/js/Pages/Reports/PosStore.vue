<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="POS Store Status" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 gap-6">
          <!-- Fetch Button -->
          <div class="flex justify-end">
            <UIButton class="min-w-[100px]" type="button" @click="fetchData" :disabled="isFetchingData">
              <Loader2 v-if="isFetchingData" class="w-4 h-4 mr-2 animate-spin" />
              {{ isFetchingData ? 'Fetching...' : 'Fetch Data' }}
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">POS Store Status</h1>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Store</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="(store, index) in stores" :key="store.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ store.name }}</TableCell>
              <TableCell>
                <Badge :variant="store.onlineStatus ? 'success' : 'destructive'">
                  {{ store.onlineStatus ? 'Online' : 'Offline' }}
                </Badge>
              </TableCell>
            </TableRow>
            <TableRow v-if="(!stores || stores.length === 0) && isFetched">
              <TableCell class="text-center border-0" colspan="2">No stores found.</TableCell>
            </TableRow>
            <TableRow v-if="!isFetched">
              <TableCell class="text-center border-0" colspan="2">Click the "Fetch Data" button to load store status.</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { Card, CardContent } from '@/Components/ui/card';
import { TableRow } from '@/Components/ui/table';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Badge } from '@/Components/ui/badge';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Separator } from '@/Components/ui/separator';
import { Button as UIButton } from '@/Components/ui/button';
import { Loader2 } from 'lucide-vue-next';
import { route } from 'ziggy-js';

export default {
  components: {
    Head,
    Card,
    CardContent,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Badge,
    Breadcrumb,
    Separator,
    UIButton,
    Loader2,
  },
  layout: Layout,
  props: {
    stores: {
      type: Array,
      default: () => []
    },
    isFetched: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      breadcrumbs: [
        {
          title: 'Dashboard',
          href: route('dashboard')
        },
        {
          title: 'POS Store',
          href: route('reports.pos-store')
        }
      ],
      isFetchingData: false,
    };
  },
  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    fetchData() {
      this.isFetchingData = true;

      this.$inertia.get(route('reports.pos-store'), { fetch: 'true' }, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        onSuccess: () => {
          this.isFetchingData = false;
        },
        onError: () => {
          this.isFetchingData = false;
        }
      });
    }
  },
};
</script>
