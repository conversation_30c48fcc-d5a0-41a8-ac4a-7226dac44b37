<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Report Details" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Select -->
          <div class="space-y-2">
            <MultiSelectCombobox
              v-model="selectedStoreIds"
              :options="storeOptions"
              placeholder="Select Stores"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Date Range Picker with improved mobile responsiveness -->
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate date-range-button ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Center the calendar properly -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker with improved mobile layout -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- Search and Reset Buttons -->
          <div class="md:col-span-2 flex justify-end space-x-4">
            <UIButton class="min-w-[100px]" type="button" variant="outline" @click="resetFilters">
              Reset
            </UIButton>
            <UIButton class="min-w-[100px]" type="button" @click="search">
              Search
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Report Details</h1>
        <div class="flex gap-2">
          <UIButton class="min-w-[100px]" type="button" @click="exportData" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </UIButton>
        </div>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Merchant Name</TableHead>
              <TableHead>New Member</TableHead>
              <TableHead>Active Users</TableHead>
              <TableHead>First-Time Deposit</TableHead>
              <TableHead>Second-Time Deposit</TableHead>
              <TableHead>Third-Time Deposit</TableHead>
              <TableHead>Deposit</TableHead>
              <TableHead>Withdrawal</TableHead>
              <TableHead>Nett Deposit</TableHead>
              <TableHead>Transfer In</TableHead>
              <TableHead>Transfer Out</TableHead>
              <TableHead>Nett Transfer</TableHead>
              <TableHead>Wallet To Card</TableHead>
              <TableHead>Card To Wallet</TableHead>
              <TableHead>P2P Transfer In</TableHead>
              <TableHead>Turnover</TableHead>
              <!-- Only show these columns for admin users -->
              <TableHead v-if="isAdmin">Promotion In</TableHead>
              <TableHead v-if="isAdmin">Promotion Out</TableHead>
              <TableHead v-if="isAdmin">Promotion Burnt</TableHead>
              <TableHead v-if="isAdmin">Adjustment In</TableHead>
              <TableHead v-if="isAdmin">Adjustment Out</TableHead>
              <TableHead v-if="isAdmin">Adjustment Nett</TableHead>
              <TableHead v-if="isAdmin">Advance Credit</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            <TableRow v-for="(item, index) in statistics" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ item.merchantName }}</TableCell>
              <TableCell>{{ item.totalRegistrations }}</TableCell>
              <TableCell>{{ item.totalActiveUsers }}</TableCell>
              <TableCell>RM {{ formatAmount(item.totalFTD) }} ({{ item.totalFTDCount }})</TableCell>
              <TableCell>RM {{ formatAmount(item.totalSTD) }} ({{ item.totalSTDCount || 0 }})</TableCell>
              <TableCell>RM {{ formatAmount(item.totalTTD) }} ({{ item.totalTTDCount || 0 }})</TableCell>
              <TableCell>RM {{ formatAmount(item.totalDeposits) }} ({{ item.totalDepositsCount }})</TableCell>
              <TableCell>RM {{ formatAmount(item.totalWithdrawals) }} ({{ item.totalWithdrawalsCount }})</TableCell>
              <TableCell>RM {{ formatAmount(item.totalNettDeposits) }}</TableCell>
              <TableCell>RM {{ formatAmount(item.transferInSum) }} ({{ item.transferInCount || 0 }})</TableCell>
              <TableCell>RM {{ formatAmount(item.transferOutSum) }} ({{ item.transferOutCount || 0 }})</TableCell>
              <TableCell>RM {{ formatAmount(item.totalTransfer) }}</TableCell>
              <TableCell>RM {{ formatAmount(item.walletToCardSum) }} ({{ item.walletToCardCount || 0 }})</TableCell>
              <TableCell>RM {{ formatAmount(item.cardToWalletSum) }} ({{ item.cardToWalletCount || 0 }})</TableCell>
              <TableCell>RM {{ formatAmount(item.p2pTransferInSum) }} ({{ item.p2pTransferInCount || 0 }})</TableCell>
              <TableCell>RM {{ formatAmount(item.totalTurnover) }} ({{ item.totalTurnoverCount }})</TableCell>
              <!-- Only show these cells for admin users -->
              <TableCell v-if="isAdmin">RM {{ formatAmount(item.promotionIn) }} ({{ item.promotionCount || 0 }})</TableCell>
              <TableCell v-if="isAdmin">RM {{ formatAmount(item.promotionOut) }} ({{ item.promotionCount || 0 }})</TableCell>
              <TableCell v-if="isAdmin">RM {{ formatAmount(item.promotionBurnt) }} ({{ item.promotionCount || 0 }})</TableCell>
              <TableCell v-if="isAdmin">RM {{ formatAmount(item.adjustmentInSum) }} ({{ item.adjustmentInCount || 0 }})</TableCell>
              <TableCell v-if="isAdmin">RM {{ formatAmount(item.adjustmentOutSum) }} ({{ item.adjustmentOutCount || 0 }})</TableCell>
              <TableCell v-if="isAdmin">RM {{ formatAmount(item.adjustmentNett) }}</TableCell>
              <TableCell v-if="isAdmin">RM {{ formatAmount(item.advanceCreditSum) }} ({{ item.advanceCreditCount || 0 }})</TableCell>
            </TableRow>

            <TableRow v-if="statistics.length === 0">
              <TableCell class="text-center border-0" colspan="22">No record found.</TableCell>
            </TableRow>
          </TableBody>

          <!-- Add Table Footer for Totals if multiple stores data exist -->
          <tfoot v-if="statistics.length > 1" class="bg-gray-50 font-medium text-sm">
            <tr>
              <td class="px-2 py-1 font-bold">Grand Total</td>
              <td class="px-2 py-1">{{ totals.totalRegistrations }}</td>
              <td class="px-2 py-1">{{ totals.totalActiveUsers }}</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalFTD) }} ({{ totals.totalFTDCount }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalSTD) }} ({{ totals.totalSTDCount || 0 }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalTTD) }} ({{ totals.totalTTDCount || 0 }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalDeposits) }} ({{ totals.totalDepositsCount }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalWithdrawals) }} ({{ totals.totalWithdrawalsCount }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalNettDeposits) }}</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.transferInSum) }} ({{ totals.transferInCount || 0 }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.transferOutSum) }} ({{ totals.transferOutCount || 0 }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalTransfer) }}</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.walletToCardSum) }} ({{ totals.walletToCardCount || 0 }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.cardToWalletSum) }} ({{ totals.cardToWalletCount || 0 }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.p2pTransferInSum) }} ({{ totals.p2pTransferInCount || 0 }})</td>
              <td class="px-2 py-1">RM {{ formatAmount(totals.totalTurnover) }} ({{ totals.totalTurnoverCount }})</td>
              <!-- Only show these cells for admin users -->
              <td v-if="isAdmin" class="px-2 py-1">RM {{ formatAmount(totals.promotionIn) }} ({{ totals.promotionCount || 0 }})</td>
              <td v-if="isAdmin" class="px-2 py-1">RM {{ formatAmount(totals.promotionOut) }} ({{ totals.promotionCount || 0 }})</td>
              <td v-if="isAdmin" class="px-2 py-1">RM {{ formatAmount(totals.promotionBurnt) }} ({{ totals.promotionCount || 0 }})</td>
              <td v-if="isAdmin" class="px-2 py-1">RM {{ formatAmount(totals.adjustmentInSum) }} ({{ totals.adjustmentInCount || 0 }})</td>
              <td v-if="isAdmin" class="px-2 py-1">RM {{ formatAmount(totals.adjustmentOutSum) }} ({{ totals.adjustmentOutCount || 0 }})</td>
              <td v-if="isAdmin" class="px-2 py-1">RM {{ formatAmount(totals.adjustmentNett) }}</td>
              <td v-if="isAdmin" class="px-2 py-1">RM {{ formatAmount(totals.advanceCreditSum) }} ({{ totals.advanceCreditCount || 0 }})</td>
            </tr>
          </tfoot>
        </Table>
      </div>

      <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
        <div></div>
        <Pagination v-if="page && page.total > 0" class="flex justify-end" :data="page" />
      </div>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Separator } from '@/Components/ui/separator';
import { Button as UIButton } from '@/Components/ui/button';
import { TableRow } from '@/Components/ui/table';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { CalendarIcon, Loader2 } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import axios from 'axios';
import MultiSelectCombobox from '@/Components/ui/multi-select-combobox.vue';

export default {
  components: {
    Head,
    Card,
    CardContent,
    Label,
    Separator,
    UIButton,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Pagination,
    Breadcrumb,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    RangeCalendar,
    ShowEntries,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Loader2,
    MultiSelectCombobox,
  },
  layout: Layout,
  props: {
    filters: Object,
    startDate: String,
    endDate: String,
    statistics: {
      type: Array,
      default: () => []
    },
    page: Object,
    stores: {
      type: Array,
      default: () => []
    }
  },
  data() {
    // Extract time from date strings
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    if (this.startDate && this.startDate.includes(' ')) {
      const timePart = this.startDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        startHour = timeComponents[0];
        startMinute = timeComponents[1];
      }
    }

    if (this.endDate && this.endDate.includes(' ')) {
      const timePart = this.endDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        endHour = timeComponents[0];
        endMinute = timeComponents[1];
      }
    }

    const startDateObj = new Date(this.startDate || new Date());
    const endDateObj = new Date(this.endDate || new Date());

    return {
      form: {
        store_id: this.filters?.store_id || 'all',
        perPage: '50',
      },
      selectedStoreIds: this.filters?.store_id ? this.filters.store_id.split(',') : ['all'],
      dateRange: {
        start: new CalendarDate(startDateObj.getFullYear(), startDateObj.getMonth() + 1, startDateObj.getDate()),
        end: new CalendarDate(endDateObj.getFullYear(), endDateObj.getMonth() + 1, endDateObj.getDate())
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Report Details', link: route('report-details'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      isExporting: false,
      stores: [], // Initialize stores as empty array
    }
  },
  computed: {
    isAdmin() {
      return this.$page.props.auth.user.roles.includes('admin');
    },
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        dateResult = this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()));

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        dateResult = `${this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()))} ${this.startTime.hour}:${this.startTime.minute} - ${this.df.format(this.dateRange.end?.toDate(getLocalTimeZone()))} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },
    totals() {
      if (!this.statistics || this.statistics.length === 0) {
        return {
          totalRegistrations: 0,
          totalActiveUsers: 0,
          totalDepositsCount: 0,
          totalDeposits: 0,
          totalWithdrawalsCount: 0,
          totalWithdrawals: 0,
          totalFTDCount: 0,
          totalFTD: 0,
          totalTurnoverCount: 0,
          totalTurnover: 0,
          totalWin: 0,
          totalLoss: 0,
          totalTransfer: 0,
          transferInCount: 0,
          transferInSum: 0,
          transferOutCount: 0,
          transferOutSum: 0,
          walletToCardCount: 0,
          walletToCardSum: 0,
          cardToWalletCount: 0,
          cardToWalletSum: 0,
          p2pTransferInCount: 0,
          p2pTransferInSum: 0,
          avgRetentionRate: 0,
          totalNettDeposits: 0,
          totalSTDCount: 0,
          totalSTD: 0,
          totalSTDRate: 0,
          totalTTDCount: 0,
          totalTTD: 0,
          totalTTDRate: 0,
          promotionCount: 0,
          promotionIn: 0,
          promotionOut: 0,
          promotionBurnt: 0,
          adjustmentInCount: 0,
          adjustmentInSum: 0,
          adjustmentOutCount: 0,
          adjustmentOutSum: 0,
          adjustmentNett: 0,
          advanceCreditCount: 0,
          advanceCreditSum: 0,
        };
      }

      const totals = {
        totalRegistrations: this.statistics.reduce((sum, item) => sum + parseInt(item.totalRegistrations || 0), 0),
        totalActiveUsers: this.statistics.reduce((sum, item) => sum + parseInt(item.totalActiveUsers || 0), 0),
        totalDepositsCount: this.statistics.reduce((sum, item) => sum + parseInt(item.totalDepositsCount || 0), 0),
        totalDeposits: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalDeposits || 0), 0),
        totalWithdrawalsCount: this.statistics.reduce((sum, item) => sum + parseInt(item.totalWithdrawalsCount || 0), 0),
        totalWithdrawals: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalWithdrawals || 0), 0),
        totalFTDCount: this.statistics.reduce((sum, item) => sum + parseInt(item.totalFTDCount || 0), 0),
        totalFTD: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalFTD || 0), 0),
        transferInCount: this.statistics.reduce((sum, item) => sum + parseInt(item.transferInCount || 0), 0),
        transferInSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.transferInSum || 0), 0),
        transferOutCount: this.statistics.reduce((sum, item) => sum + parseInt(item.transferOutCount || 0), 0),
        transferOutSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.transferOutSum || 0), 0),
        walletToCardCount: this.statistics.reduce((sum, item) => sum + parseInt(item.walletToCardCount || 0), 0),
        walletToCardSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.walletToCardSum || 0), 0),
        cardToWalletCount: this.statistics.reduce((sum, item) => sum + parseInt(item.cardToWalletCount || 0), 0),
        cardToWalletSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.cardToWalletSum || 0), 0),
        p2pTransferInCount: this.statistics.reduce((sum, item) => sum + parseInt(item.p2pTransferInCount || 0), 0),
        p2pTransferInSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.p2pTransferInSum || 0), 0),
        totalTurnoverCount: this.statistics.reduce((sum, item) => sum + parseInt(item.totalTurnoverCount || 0), 0),
        totalTurnover: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalTurnover || 0), 0),
        totalWin: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalWin || 0), 0),
        totalLoss: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalLoss || 0), 0),
        totalTransfer: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalTransfer || 0), 0),
        totalNettDeposits: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalNettDeposits || 0), 0),
        totalSTDCount: this.statistics.reduce((sum, item) => sum + parseInt(item.totalSTDCount || 0), 0),
        totalSTD: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalSTD || 0), 0),
        totalTTDCount: this.statistics.reduce((sum, item) => sum + parseInt(item.totalTTDCount || 0), 0),
        totalTTD: this.statistics.reduce((sum, item) => sum + parseFloat(item.totalTTD || 0), 0),
        promotionCount: this.statistics.reduce((sum, item) => sum + parseInt(item.promotionCount || 0), 0),
        promotionIn: this.statistics.reduce((sum, item) => sum + parseFloat(item.promotionIn || 0), 0),
        promotionOut: this.statistics.reduce((sum, item) => sum + parseFloat(item.promotionOut || 0), 0),
        promotionBurnt: this.statistics.reduce((sum, item) => sum + parseFloat(item.promotionBurnt || 0), 0),
        adjustmentInCount: this.statistics.reduce((sum, item) => sum + parseInt(item.adjustmentInCount || 0), 0),
        adjustmentInSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.adjustmentInSum || 0), 0),
        adjustmentOutCount: this.statistics.reduce((sum, item) => sum + parseInt(item.adjustmentOutCount || 0), 0),
        adjustmentOutSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.adjustmentOutSum || 0), 0),
        adjustmentNett: this.statistics.reduce((sum, item) => sum + parseFloat(item.adjustmentNett || 0), 0),
        advanceCreditCount: this.statistics.reduce((sum, item) => sum + parseInt(item.advanceCreditCount || 0), 0),
        advanceCreditSum: this.statistics.reduce((sum, item) => sum + parseFloat(item.advanceCreditSum || 0), 0),
      };

      // Calculate average retention rate
      const totalRetention = this.statistics.reduce((sum, item) => sum + parseFloat(item.retentionRate || 0), 0);
      totals.avgRetentionRate = this.statistics.length > 0 ? totalRetention / this.statistics.length : 0;

      // Calculate STD and TTD rates
      totals.totalSTDRate = totals.totalRegistrations > 0 ? totals.totalSTDCount / totals.totalRegistrations : 0;
      totals.totalTTDRate = totals.totalRegistrations > 0 ? totals.totalTTDCount / totals.totalRegistrations : 0;

      return totals;
    },
    storeOptions() {
      // Add "All Stores" option
      const allStoresOption = { value: 'all', label: 'All Stores' };

      // Map store data to the format expected by MultiSelectCombobox
      const storeOptions = this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));

      // Return combined options with "All Stores" first
      return [allStoresOption, ...storeOptions];
    }
  },
  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    formatPercentage(value) {
      return Number(value || 0).toLocaleString('en-US', {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    formatRetentionRate(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }) + '%';
    },
    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    getStoreIdParam() {
      // If "all" is selected or no stores are selected, use null (all stores)
      if (this.selectedStoreIds.includes('all') || this.selectedStoreIds.length === 0) {
        // If "all" is selected along with other stores, keep only "all"
        if (this.selectedStoreIds.includes('all') && this.selectedStoreIds.length > 1) {
          this.$nextTick(() => {
            this.selectedStoreIds = ['all'];
          });
        }
        return null;
      }
      // If only one store is selected (and it's not "all"), use that store ID
      else if (this.selectedStoreIds.length === 1) {
        return this.selectedStoreIds[0];
      }
      // If multiple stores are selected, join them with commas
      else {
        return this.selectedStoreIds.join(',');
      }
    },
    search() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = pickBy({
        store_id: this.getStoreIdParam(),
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        page: 1,
        perPage: this.form.perPage,
      });

      this.$inertia.get(route('report-details'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['statistics', 'page', 'filters'],
      });
    },
    resetFilters() {
      this.form = {
        store_id: 'all',
        perPage: '50',
      };
      this.selectedStoreIds = ['all'];

      // Reset time as well
      this.startTime = {
        hour: "00",
        minute: "00"
      };

      this.endTime = {
        hour: "23",
        minute: "59"
      };

      const today = new Date();
      this.dateRange = {
        start: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate()),
        end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
      };

      this.$inertia.visit('/report-details', {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },
    async exportData() {
      this.isExporting = true;

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = new URLSearchParams({
        store_id: this.getStoreIdParam() || '',
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      });

      try {
        const exportUrl = route('report-details.export', Object.fromEntries(params));
        const response = await fetch(exportUrl);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        }

        document.body.appendChild(a);
        a.click();

        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },
    async loadStores() {
      if (!Array.isArray(this.stores) || this.stores.length === 0) {
        try {
          const response = await axios.get('/stores/all');
          this.stores = response.data;
        } catch (error) {
          console.error('Failed to load stores:', error);
          this.stores = [];
        }
      }
    },
  },
  mounted() {
    // Make sure stores are loaded on component mount
    this.loadStores();
  },
  watch: {
    selectedStoreIds: {
      handler() {
        // Get store ID from the helper method
        const storeId = this.getStoreIdParam();
        // Update filters.store_id for consistency
        this.filters.store_id = storeId === null ? 'all' : storeId;
      },
      deep: true
    }
  }
}
</script>
