<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="POS Machine Summary" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Select with Search -->
          <div class="space-y-2">
            <SearchableCombobox
              v-model="form.store_id"
              :options="storeOptions"
              placeholder="Select Store"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Date Range Picker with time selection -->
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate date-range-button ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Center the calendar properly -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker with improved mobile layout -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- Search and Reset Buttons -->
          <div class="md:col-span-2 flex justify-end space-x-4">
            <UIButton class="min-w-[100px]" type="button" variant="outline" @click="resetFilters">
              Reset
            </UIButton>
            <UIButton class="min-w-[100px]" type="button" @click="reset">
              Search
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">POS Machine Summary</h1>
        <div class="flex gap-2">
          <UIButton class="min-w-[100px]" type="button" @click="exportData" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </UIButton>
        </div>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Machine Number</TableHead>
              <TableHead>Machine Name</TableHead>
              <TableHead>Token In</TableHead>
              <TableHead>Token Out</TableHead>
              <TableHead>Total Profit</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            <TableRow v-for="(item, index) in data.items" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ item.machine_no }}</TableCell>
              <TableCell>{{ item.machine_name }}</TableCell>
              <TableCell>{{ formatAmount(item.token_in) }}</TableCell>
              <TableCell>{{ formatAmount(item.token_out) }}</TableCell>
              <TableCell>{{ formatAmount(item.total_profit) }}</TableCell>
            </TableRow>

            <!-- Grand Total Row -->
            <TableRow v-if="data.items.length > 0" class="bg-gray-100 font-bold">
              <TableCell colspan="2" class="text-right">Grand Total:</TableCell>
              <TableCell>{{ formatAmount(data.grand_total.token_in) }}</TableCell>
              <TableCell>{{ formatAmount(data.grand_total.token_out) }}</TableCell>
              <TableCell>{{ formatAmount(data.grand_total.total_profit) }}</TableCell>
            </TableRow>

            <TableRow v-if="data.items.length === 0">
              <TableCell class="text-center border-0" colspan="6">No record found.</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import axios from 'axios';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import { Button as UIButton } from '@/Components/ui/button';
import {
  TableRow
} from '@/Components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { CalendarIcon, Loader2 } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date';
import SearchableCombobox from '@/Components/ui/searchable-combobox.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    UIButton,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Badge,
    Pagination,
    ShowEntries,
    Breadcrumb,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    RangeCalendar,
    Loader2,
    SearchableCombobox,
  },
  layout: Layout,
  props: {
    filters: Object,
    startDate: String,
    endDate: String,
    data: Object,
    page: Object,
    stores: {
      type: Array,
      default: () => []
    }
  },
  data() {
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    if (this.startDate && this.startDate.includes(' ')) {
      const timePart = this.startDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        startHour = timeComponents[0];
        startMinute = timeComponents[1];
      }
    }

    if (this.endDate && this.endDate.includes(' ')) {
      const timePart = this.endDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        endHour = timeComponents[0];
        endMinute = timeComponents[1];
      }
    }

    // Use provided dates if available, otherwise default to today for both start and end
    const today = new Date();
    const startDateObj = this.startDate ? new Date(this.startDate) : today;
    const endDateObj = this.endDate ? new Date(this.endDate) : today;

    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        store_id: this.filters?.store_id || null,
        perPage: this.filters?.perPage?.toString() || '10',
      },
      errors: {},
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Machine Summary', link: route('reports.machine-summary'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      dateRange: {
        start: new CalendarDate(
          startDateObj.getFullYear(),
          startDateObj.getMonth() + 1,
          startDateObj.getDate()
        ),
        end: new CalendarDate(
          endDateObj.getFullYear(),
          endDateObj.getMonth() + 1,
          endDateObj.getDate()
        )
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
      isExporting: false,
    }
  },
  computed: {
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        dateResult = this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()));

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        dateResult = `${this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()))} ${this.startTime.hour}:${this.startTime.minute} - ${this.df.format(this.dateRange.end?.toDate(getLocalTimeZone()))} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },
    storeOptions() {
      // Map store data to the format expected by SearchableCombobox
      return this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));
    }
  },
  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    reset() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = pickBy({
        store_id: this.form.store_id,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        page: 1,
      });

      this.$inertia.get(route('reports.machine-summary'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page', 'filters'],
      });
    },
    resetFilters() {
      this.form = {
        store_id: null,
        perPage: '10',
      };

      const today = new Date();
      this.dateRange = {
        start: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate()),
        end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
      };

      // Reset time to beginning and end of day
      this.startTime = {
        hour: "00",
        minute: "00"
      };

      this.endTime = {
        hour: "23",
        minute: "59"
      };

      this.$inertia.visit('/reports/machine-summary', {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },
    async exportData() {
      this.isExporting = true;

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = {
        store_id: this.form.store_id,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      };

      try {
        // Use fetch to get the file
        const response = await fetch(route('reports.machine-summary.export', params));

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        // Get the file as blob
        const blob = await response.blob();

        // Create object URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create a temporary link element and trigger download
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        // Get the filename from the Content-Disposition header
        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        }

        document.body.appendChild(a);
        a.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        // Set isExporting back to false after download completes or fails
        this.isExporting = false;
      }
    }
  },
  watch: {
    filters: {
      deep: true,
      handler(newFilters) {
        if (newFilters) {
          this.form.store_id = newFilters.store_id || this.form.store_id;
        }
      }
    }
  }
}
</script>