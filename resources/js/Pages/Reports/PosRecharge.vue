<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="POS Recharge" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Select with Search -->
          <div class="space-y-2">
            <SearchableCombobox
              v-model="form.store_id"
              :options="storeOptions"
              placeholder="Select Store"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Date Range Picker with time selection -->
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate date-range-button ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Center the calendar properly -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker with improved mobile layout -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- Online Transaction Dropdown (on a new row, half-length) -->
          <div class="space-y-2">
            <Label>Online Transaction:</Label>
            <Select v-model="form.isIncludeOnline" class="w-full">
              <SelectTrigger>
                <SelectValue>
                  {{ form.isIncludeOnline === 'true' ? 'Include' : 'Exclude' }}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="true">Include</SelectItem>
                  <SelectItem value="false">Exclude</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <!-- Empty div to maintain grid layout -->
          <div></div>

          <!-- Search and Reset Buttons -->
          <div class="md:col-span-2 flex justify-end space-x-4">
            <UIButton class="min-w-[100px]" type="button" variant="outline" @click="resetFilters">
              Reset
            </UIButton>
            <UIButton class="min-w-[100px]" type="button" @click="reset">
              Search
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">POS Recharge</h1>
        <div class="flex gap-2">
          <UIButton class="min-w-[100px]" type="button" @click="exportData" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </UIButton>
        </div>
      </div>

      <!-- Summary boxes with inline layout -->
      <div class="flex gap-4 mb-6">
        <Card class="bg-white border border-gray-200">
          <CardContent class="p-2">
            <div class="flex items-center gap-1">
              <span class="text-[11px] text-gray-700">Total All Recharge Amount:</span>
              <span class="text-sm font-semibold text-black tabular-nums">
                {{ formatAmount(data.grand_total.total_all_amount) }}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card class="bg-white border border-gray-200">
          <CardContent class="p-2">
            <div class="flex items-center gap-1">
              <span class="text-[11px] text-gray-700">Total Recharge Amount:</span>
              <span class="text-sm font-semibold text-black tabular-nums">
                {{ formatAmount(data.grand_total.total_amount) }}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date & Time</TableHead>
              <TableHead>Terminal</TableHead>
              <TableHead>Card Serial No.</TableHead>
              <TableHead>Transaction Type</TableHead>
              <TableHead>Initial Amount</TableHead>
              <TableHead>Recharge Amount</TableHead>
              <TableHead>Balance</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            <TableRow v-for="(item, index) in data.items" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ item.transaction_date }}</TableCell>
              <TableCell>{{ item.terminal_address }}</TableCell>
              <TableCell>{{ item.member_card_serial_no }}</TableCell>
              <TableCell>{{ item.transaction_type }}</TableCell>
              <TableCell>{{ formatAmount(item.initial_balance) }}</TableCell>
              <TableCell>{{ formatAmount(item.amount_used) }}</TableCell>
              <TableCell>{{ formatAmount(item.balance) }}</TableCell>
            </TableRow>

            <TableRow v-if="data.items.length === 0">
              <TableCell class="text-center border-0" colspan="10">No record found.</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>

    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination v-if="page && page.links && page.links.length > 0" class="flex justify-end" :data="page" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import { Button as UIButton } from '@/Components/ui/button';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Calendar as CalendarIcon, Loader2 } from 'lucide-vue-next';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import {
  CalendarDate,
  DateFormatter,
  getLocalTimeZone,
} from '@internationalized/date';
import { Switch } from '@/Components/ui/switch';
import SearchableCombobox from '@/Components/ui/searchable-combobox.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    UIButton,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Icon,
    Badge,
    Pagination,
    ShowEntries,
    Info,
    Tooltip,
    Breadcrumb,
    CommandItem,
    Check,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    RangeCalendar,
    Switch,
    Loader2,
    SearchableCombobox
  },
  layout: Layout,
  props: {
    filters: Object,
    sort: Object,
    startDate: String,
    endDate: String,
    data: Object,
    page: {
      type: Object,
      default: () => ({
        links: [],
        current_page: 1,
        from: 0,
        last_page: 1,
        per_page: 10,
        to: 0,
        total: 0
      })
    },
    stores: {
      type: Array,
      default: () => []
    }
  },
  data() {
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    if (this.startDate && this.startDate.includes(' ')) {
      const timePart = this.startDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        startHour = timeComponents[0];
        startMinute = timeComponents[1];
      }
    }

    if (this.endDate && this.endDate.includes(' ')) {
      const timePart = this.endDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        endHour = timeComponents[0];
        endMinute = timeComponents[1];
      }
    }

    const startDateObj = new Date(this.startDate);
    const endDateObj = new Date(this.endDate);

    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        store_id: this.filters?.store_id || null,
        isIncludeOnline: this.filters?.isIncludeOnline || 'true',
        search: this.filters?.search,
        perPage: this.filters?.perPage?.toString() || '10',
      },
      errors: {},
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'POS Report', link: route('reports.pos-recharge'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      dateRange: {
        start: new CalendarDate(
          startDateObj.getFullYear(),
          startDateObj.getMonth() + 1,
          startDateObj.getDate()
        ),
        end: new CalendarDate(
          endDateObj.getFullYear(),
          endDateObj.getMonth() + 1,
          endDateObj.getDate()
        )
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
      isExporting: false,
    }
  },
  computed: {
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        dateResult = this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()));

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        dateResult = `${this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()))} ${this.startTime.hour}:${this.startTime.minute} - ${this.df.format(this.dateRange.end?.toDate(getLocalTimeZone()))} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },
    storeOptions() {
      // Map store data to the format expected by SearchableCombobox
      return this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));
    }
  },
  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    reset() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = pickBy({
        store_id: this.form.store_id,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        isIncludeOnline: this.form.isIncludeOnline,
        page: 1,
        perPage: this.form.perPage,
      });

      this.$inertia.get(route('reports.pos-recharge'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page', 'filters'],
      });
    },
    resetFilters() {
      this.form = {
        store_id: null,
        isIncludeOnline: 'true',
        search: '',
        perPage: '10',
      };

      const today = new Date();
      this.dateRange = {
        start: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate()),
        end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
      };

      // Reset time to beginning and end of day
      this.startTime = {
        hour: "00",
        minute: "00"
      };

      this.endTime = {
        hour: "23",
        minute: "59"
      };

      this.$inertia.visit('/reports/pos-recharge', {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },
    async exportData() {
      this.isExporting = true;

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = new URLSearchParams({
        store_id: this.form.store_id,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        isIncludeOnline: this.form.isIncludeOnline,
      });

      try {
        // Use fetch to get the file
        const response = await fetch(route('reports.pos-recharge.export', Object.fromEntries(params)));

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        // Get the file as blob
        const blob = await response.blob();

        // Create object URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create a temporary link element and trigger download
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        // Get the filename from the Content-Disposition header
        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        }

        document.body.appendChild(a);
        a.click();

        // Clean up
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        // Set isExporting back to false after download completes or fails
        this.isExporting = false;
      }
    }
  },
  watch: {
    'form.perPage'(newValue) {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = pickBy({
        store_id: this.form.store_id,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        isIncludeOnline: this.form.isIncludeOnline,
        page: 1,
        perPage: newValue,
      });

      this.$inertia.get(route('reports.pos-recharge'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page'],
      });
    },
    filters: {
      deep: true,
      handler(newFilters) {
        if (newFilters) {
          this.form.store_id = newFilters.store_id || this.form.store_id;
        }
      }
    }
  },
  mounted() {
  },
}
</script>