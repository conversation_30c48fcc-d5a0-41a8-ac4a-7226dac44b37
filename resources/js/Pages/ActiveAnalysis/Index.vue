<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="Active Analysis" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Multi-Select -->
          <div class="space-y-2">
            <MultiSelectCombobox
              v-model="selectedStoreIds"
              :options="storeOptions"
              placeholder="Select Stores"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Date Range Picker with improved mobile responsiveness -->
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate date-range-button ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Center the calendar properly -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker with improved mobile layout -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- Time Aggregation Selector -->
          <div class="space-y-2">
            <Label>Time Aggregation:</Label>
            <Select v-model="selectedTimeAggregation">
              <SelectTrigger class="w-full">
                <SelectValue :placeholder="selectedTimeAggregation" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="hourly">Hourly</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <!-- View Mode Selector -->
          <div class="space-y-2">
            <Label>View Mode:</Label>
            <Select v-model="viewMode">
              <SelectTrigger class="w-full">
                <SelectValue :placeholder="viewMode === 'game' ? 'Game Count' : 'User Count'" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="game">Game Count</SelectItem>
                  <SelectItem value="user">User Count</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>

          <div class="md:col-span-2 flex justify-end space-x-4">
            <Button @click="resetFilters" class="min-w-[100px]" variant="outline">Reset</Button>
            <Button @click="search" class="min-w-[100px]">Search</Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Active Analysis</h1>
        <div v-if="chartData" class="flex space-x-3">
          <Button
            @click="exportChart"
            variant="outline"
            class="flex items-center h-9 px-4 rounded-md border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground"
          >
            <DownloadIcon class="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button
            @click="printChart"
            variant="outline"
            class="flex items-center h-9 px-4 rounded-md border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground"
          >
            <PrinterIcon class="mr-2 h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      <Card v-if="chartData">
        <CardContent class="p-6">

          <div class="h-[400px]" id="chart-container">
            <canvas ref="chartCanvas"></canvas>
          </div>
        </CardContent>
      </Card>



      <Card v-if="!chartData">
        <CardContent class="p-6 flex justify-center items-center h-[400px]">
          <p class="text-muted-foreground">No data available. Please select a date range and store to view statistics.</p>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Separator } from '@/Components/ui/separator';
import { Button as UIButton } from '@/Components/ui/button';
import { Button } from '@/Components/ui/button';
import { CalendarIcon, Loader2, DownloadIcon, PrinterIcon } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date';
import MultiSelectCombobox from '@/Components/ui/multi-select-combobox.vue';
import { Chart, registerables } from 'chart.js';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import axios from 'axios';

Chart.register(...registerables);

export default {
  components: {
    Head,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    Label,
    Separator,
    UIButton,
    Button,
    Breadcrumb,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    RangeCalendar,
    Loader2,
    DownloadIcon,
    PrinterIcon,
    MultiSelectCombobox,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
  },
  layout: Layout,
  props: {
    filters: Object,
    startDate: String,
    endDate: String,
    chartData: Object,
    stores: {
      type: Array,
      default: () => []
    },
    timeAggregation: {
      type: String,
      default: 'hourly'
    }
  },
  data() {
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    if (this.startDate && this.startDate.includes(' ')) {
      const timePart = this.startDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        startHour = timeComponents[0];
        startMinute = timeComponents[1];
      }
    }

    if (this.endDate && this.endDate.includes(' ')) {
      const timePart = this.endDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        endHour = timeComponents[0];
        endMinute = timeComponents[1];
      }
    }

    const startDateObj = this.startDate ? new Date(this.startDate) : new Date(new Date().setDate(new Date().getDate() - 7));
    const endDateObj = this.endDate ? new Date(this.endDate) : new Date();

    let initialStoreIds = ['all'];
    if (this.filters && this.filters.store_id && this.filters.store_id !== 'all') {
      initialStoreIds = this.filters.store_id.includes(',')
        ? this.filters.store_id.split(',')
        : [this.filters.store_id];
    }

    return {
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Active Analysis', link: route('active-analysis'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      dateRange: {
        start: new CalendarDate(
          startDateObj.getFullYear(),
          startDateObj.getMonth() + 1,
          startDateObj.getDate()
        ),
        end: new CalendarDate(
          endDateObj.getFullYear(),
          endDateObj.getMonth() + 1,
          endDateObj.getDate()
        )
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
      filters: {
        store_id: this.filters?.store_id || 'all',
        time_aggregation: this.filters?.time_aggregation || 'hourly',
      },
      selectedStoreIds: initialStoreIds,
      selectedTimeAggregation: this.timeAggregation || 'hourly',
      chart: null,
      stores: [],
      viewMode: 'game', // Default view mode is game count
    };
  },
  computed: {
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        dateResult = this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()));

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        dateResult = `${this.df.format(this.dateRange.start?.toDate(getLocalTimeZone()))} ${this.startTime.hour}:${this.startTime.minute} - ${this.df.format(this.dateRange.end?.toDate(getLocalTimeZone()))} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },
    storeOptions() {
      const allStoresOption = { value: 'all', label: 'All Stores' };

      const storeOptions = this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));

      return [allStoresOption, ...storeOptions];
    },

  },
  methods: {
    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    search() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      let storeId = null;
      if (this.selectedStoreIds.includes('all') || this.selectedStoreIds.length === 0) {
        storeId = null;
      } else if (this.selectedStoreIds.length === 1) {
        storeId = this.selectedStoreIds[0];
      } else {
        storeId = this.selectedStoreIds.join(',');
      }

      this.filters.store_id = storeId === null ? 'all' : storeId;
      this.filters.time_aggregation = this.selectedTimeAggregation;

      const params = {
        store_id: storeId === null ? 'all' : storeId,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        time_aggregation: this.selectedTimeAggregation
      };

      this.$inertia.get(route('active-analysis'), pickBy(params), {
        preserveState: true,
        preserveScroll: true,
        replace: true
      });
    },
    resetFilters() {
      this.selectedStoreIds = ['all'];

      this.selectedTimeAggregation = 'hourly';

      // Reset view mode to game count
      this.viewMode = 'game';

      const today = new Date();
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 7);

      this.dateRange = {
        start: new CalendarDate(sevenDaysAgo.getFullYear(), sevenDaysAgo.getMonth() + 1, sevenDaysAgo.getDate()),
        end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
      };

      this.startTime = {
        hour: "00",
        minute: "00"
      };

      this.endTime = {
        hour: "23",
        minute: "59"
      };

      this.$inertia.visit(route('active-analysis'), {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },
    async loadStores() {
      try {
        const response = await axios.get('/stores/all');
        if (response.data && Array.isArray(response.data)) {
          this.stores = response.data.map(store => ({
            ...store,
            id: store.id.toString() // Ensure ID is a string
          }));

          console.log('Loaded stores:', this.stores);
          console.log('Current selectedStoreIds:', this.selectedStoreIds);

          if (this.selectedStoreIds.length > 0 && !this.selectedStoreIds.includes('all')) {
            const validStoreIds = this.stores.map(store => store.id.toString());
            const validSelectedIds = this.selectedStoreIds.filter(id => validStoreIds.includes(id));

            if (validSelectedIds.length !== this.selectedStoreIds.length) {
              if (validSelectedIds.length === 0) {
                this.selectedStoreIds = ['all'];
              } else {
                this.selectedStoreIds = validSelectedIds;
              }
              console.log('Updated selectedStoreIds after validation:', this.selectedStoreIds);
            }
          }
        } else {
          console.error('Invalid store data format:', response.data);
        }
      } catch (error) {
        console.error('Failed to load stores:', error);
      }
    },
    initChart() {
      if (this.chartData && this.$refs.chartCanvas) {
        const ctx = this.$refs.chartCanvas.getContext('2d');

        if (this.chart) {
          this.chart.destroy();
        }

        // Define colors for each dataset
        const colors = [
          { borderColor: 'rgba(75, 192, 192, 1)' }, // Deposits - Green
          { borderColor: 'rgba(54, 162, 235, 1)' }, // Games - Blue
          { borderColor: 'rgba(255, 99, 132, 1)' }, // Withdrawals - Red
        ];

        // Create datasets based on the view mode
        const datasets = [];

        // Process datasets based on view mode
        if (this.viewMode === 'game') {
          // Game Count view (default) - Show game counts as primary
          this.chartData.datasets.forEach((dataset) => {
            // Rename "Transfers" to "Games" if that's the label
            let label = dataset.label;
            if (label === 'Transfers') {
              label = 'Games';

              datasets.push({
                label: label,
                data: dataset.data,
                borderColor: colors[1].borderColor, // Blue for Games
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.1,
                fill: false,
              });
            } else if (label === 'Deposits') {
              datasets.push({
                label: label,
                data: dataset.data,
                borderColor: colors[0].borderColor, // Green for Deposits
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.1,
                fill: false,
              });
            } else if (label === 'Withdrawals') {
              datasets.push({
                label: label,
                data: dataset.data,
                borderColor: colors[2].borderColor, // Red for Withdrawals
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.1,
                fill: false,
              });
            } else if (label === 'Deposits Distinct Users' || label === 'Transfers Distinct Users' || label === 'Withdrawals Distinct Users') {
              // Add distinct users datasets but hide them
              datasets.push({
                label: label,
                data: dataset.data,
                borderColor: 'rgba(0, 0, 0, 0)', // Transparent border
                backgroundColor: 'transparent',
                borderWidth: 0,
                tension: 0.1,
                fill: false,
                hidden: true, // Hide these datasets from the legend
              });
            }
          });
        } else {
          // User Count view - Show user counts as primary
          this.chartData.datasets.forEach((dataset) => {
            let label = dataset.label;

            if (label === 'Deposits Distinct Users') {
              // Make distinct users visible and primary
              datasets.push({
                label: 'Deposits Users',
                data: dataset.data,
                borderColor: colors[0].borderColor, // Green for Deposits
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.1,
                fill: false,
              });
            } else if (label === 'Transfers Distinct Users') {
              // Make distinct users visible and primary
              datasets.push({
                label: 'Games Users',
                data: dataset.data,
                borderColor: colors[1].borderColor, // Blue for Games
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.1,
                fill: false,
              });
            } else if (label === 'Withdrawals Distinct Users') {
              // Make distinct users visible and primary
              datasets.push({
                label: 'Withdrawals Users',
                data: dataset.data,
                borderColor: colors[2].borderColor, // Red for Withdrawals
                backgroundColor: 'transparent',
                borderWidth: 2,
                tension: 0.1,
                fill: false,
              });
            } else if (label === 'Deposits' || label === 'Transfers' || label === 'Withdrawals') {
              // Add game count datasets but hide them
              // Rename "Transfers" to "Games" if that's the label
              if (label === 'Transfers') {
                label = 'Games';
              }

              datasets.push({
                label: label,
                data: dataset.data,
                borderColor: 'rgba(0, 0, 0, 0)', // Transparent border
                backgroundColor: 'transparent',
                borderWidth: 0,
                tension: 0.1,
                fill: false,
                hidden: true, // Hide these datasets from the legend
              });
            }
          });
        }

        this.chart = new Chart(ctx, {
          type: 'line',
          data: {
            labels: this.chartData.labels,
            datasets: datasets,
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  precision: 0
                },
                title: {
                  display: true,
                  text: this.viewMode === 'game' ? 'Game Count' : 'User Count'
                }
              },
              x: {
                ticks: {
                  maxRotation: 45,
                  minRotation: 45
                }
              }
            },
            plugins: {
              tooltip: {
                callbacks: {
                  label: (context) => {
                    const label = context.dataset.label || '';
                    const value = context.parsed.y;
                    const dataIndex = context.dataIndex;
                    const allDatasets = context.chart.data.datasets;

                    if (this.viewMode === 'game') {
                      // Game Count view - Show game counts as primary with user counts in tooltips
                      if (label === 'Deposits' || label === 'Games' || label === 'Withdrawals') {
                        // Find the corresponding distinct users dataset
                        const searchLabel = label === 'Games' ? 'Transfers Distinct Users' : `${label} Distinct Users`;
                        const distinctUsersDataset = allDatasets.find(ds => ds.label === searchLabel);

                        if (distinctUsersDataset && distinctUsersDataset.data[dataIndex] !== undefined) {
                          const distinctUsers = distinctUsersDataset.data[dataIndex];
                          return [
                            `${label}: ${value}`,
                            `User Count: ${distinctUsers}`
                          ];
                        }

                        return `${label}: ${value}`;
                      }
                    } else {
                      // User Count view - Show user counts as primary with game counts in tooltips
                      if (label === 'Deposits Users' || label === 'Games Users' || label === 'Withdrawals Users') {
                        // Find the corresponding game count dataset
                        const gameLabel = label === 'Games Users' ? 'Games' : label.replace(' Users', '');
                        const gameDataset = allDatasets.find(ds => ds.label === gameLabel);

                        if (gameDataset && gameDataset.data[dataIndex] !== undefined) {
                          const gameCount = gameDataset.data[dataIndex];
                          return [
                            `${label}: ${value}`,
                            `${gameLabel}: ${gameCount}`
                          ];
                        }

                        return `${label}: ${value}`;
                      }
                    }

                    // For hidden datasets, don't show in tooltip
                    return null;
                  }
                }
              },
              legend: {
                labels: {
                  usePointStyle: true,
                  padding: 20,
                  filter: function(legendItem) {
                    // Only show visible datasets in the legend
                    return !legendItem.hidden;
                  }
                }
              }
            },
            interaction: {
              mode: 'nearest',
              intersect: true
            },
            hover: {
              mode: 'nearest',
              intersect: true
            }
          },
        });
      }
    },

    // Export chart as an image
    exportChart() {
      if (!this.chart) return;

      // Get the canvas element
      const canvas = this.$refs.chartCanvas;

      // Create a temporary link element
      const link = document.createElement('a');

      // Set the download attribute with a filename
      link.download = `active-analysis-${this.viewMode}-${this.selectedTimeAggregation}-${new Date().toISOString().split('T')[0]}.png`;

      // Convert the canvas to a data URL
      link.href = canvas.toDataURL('image/png');

      // Append the link to the document
      document.body.appendChild(link);

      // Trigger the download
      link.click();

      // Remove the link from the document
      document.body.removeChild(link);
    },

    // Print the chart
    printChart() {
      if (!this.chart) return;

      // Get the canvas element
      const canvas = this.$refs.chartCanvas;

      // Create a new window
      const printWindow = window.open('', '_blank');

      // Create the HTML content for the print window
      const viewModeText = this.viewMode === 'game' ? 'Game Count' : 'User Count';
      const title = `Active Analysis - ${viewModeText} (${this.selectedTimeAggregation}) - ${new Date().toLocaleDateString()}`;
      const dateRange = this.formattedDateResult;
      const storeInfo = this.selectedStoreIds.includes('all') ? 'All Stores' :
                        `Selected Stores: ${this.selectedStoreIds.join(', ')}`;

      // Get the chart image
      const chartImage = canvas.toDataURL('image/png');

      // Create the HTML content
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${title}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 20px;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
            }
            .chart-container {
              text-align: center;
              margin: 20px 0;
            }
            .chart-image {
              max-width: 100%;
              height: auto;
            }
            .info {
              margin-bottom: 10px;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${title}</h1>
            <div class="info">Date Range: ${dateRange}</div>
            <div class="info">${storeInfo}</div>
          </div>
          <div class="chart-container">
            <img src="${chartImage}" class="chart-image" alt="Active Analysis Chart">
          </div>
        </body>
        </html>
      `;

      // Use a more modern approach to write content to the window
      printWindow.document.open();

      // Create a new HTML document
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');

      // Clone the document and append it to the print window
      const importedNode = printWindow.document.importNode(doc.documentElement, true);
      printWindow.document.appendChild(importedNode);

      printWindow.document.close();

      // Wait for the image to load before printing
      setTimeout(() => {
        printWindow.print();
        // Close the window after printing (optional)
        // printWindow.close();
      }, 500);
    }
  },
  mounted() {
    this.loadStores();

    // Initialize chart after a short delay to ensure DOM is ready
    this.$nextTick(() => {
      this.initChart();

      // Add console logs for debugging
      console.log('Component mounted');
      console.log('Chart data:', this.chartData);
      console.log('Selected store IDs:', this.selectedStoreIds);
    });
  },
  updated() {
    this.$nextTick(() => {
      this.initChart();
      console.log('Component updated, reinitializing chart');
    });
  },
  watch: {
    // Watch for changes in selectedStoreIds and update filters.store_id
    selectedStoreIds: {
      handler() {
        // Get store ID directly
        let storeId = null;
        if (this.selectedStoreIds.includes('all') || this.selectedStoreIds.length === 0) {
          storeId = null;
          // If "all" is selected along with other stores, keep only "all"
          if (this.selectedStoreIds.includes('all') && this.selectedStoreIds.length > 1) {
            this.$nextTick(() => {
              this.selectedStoreIds = ['all'];
            });
          }
        } else if (this.selectedStoreIds.length === 1) {
          storeId = this.selectedStoreIds[0];
        } else {
          storeId = this.selectedStoreIds.join(',');
        }

        // Update filters.store_id for consistency
        this.filters.store_id = storeId === null ? 'all' : storeId;
      },
      deep: true
    },
    // Watch for changes in viewMode and reinitialize the chart
    viewMode: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      }
    }
  },
};
</script>
