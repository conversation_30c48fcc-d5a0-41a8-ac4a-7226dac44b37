<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Merchants" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter v-model="form.search" @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Trashed:</Label>
              <Select v-model="form.trashed" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Please Select Trash" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="with">
                      With Trashed
                    </SelectItem>
                    <SelectItem value="only">
                      Only Trashed
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-5" />
    </div>

    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Merchants</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <Button label="Create" icon="plus" class="cursor-pointer" @click="createMerchant" />
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create Merchant</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateMerchant">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-4 max-h-[75vh]">
                  <TextInput v-model="dialog.name" :error="errors.name" label="Name" />

                  <TextInput v-model="dialog.code" :error="errors.code" label="Code" />

                  <TextInput v-model="dialog.store_ids" :error="errors.store_ids" label="Store Ids" />
                </div>
              </div>
              <DialogFooter class="px-2">
                <Button type="submit" label="Create Merchant" />
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>

    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Merchant</TableHead>
            <TableHead>Code</TableHead>
            <TableHead>Store Ids</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody>
          <TableRow v-for="(merchant, index) in merchants.data" :key="merchant.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>
              <Link class="flex items-center" :href="route('merchants.edit', merchant.id)">
              {{ merchant.name }}
              <Icon v-if="merchant.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>

            <TableCell>
              <Link class="flex items-center" :href="route('merchants.edit', merchant.id)" tabindex="-1">
              {{ merchant.code }}
              </Link>
            </TableCell>

            <TableCell>
              {{ merchant.store_ids }}
            </TableCell>

            <TableCell>
              {{ merchant.created_at }}
            </TableCell>

            <TableCell>
              <Link :href="route('merchants.edit', merchant.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
          </TableRow>

          <TableRow v-if="merchants.data.length === 0">
            <TableCell class="text-center border-0" colspan="7">No merchants found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="merchants" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Icon,
    Badge,
    Pagination,
    ShowEntries,
    Info,
    Tooltip,
    Breadcrumb,
    CommandItem,
    Check
  },
  layout: Layout,
  props: {
    filters: Object,
    merchants: Object,
    sort: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        trashed: this.filters.trashed,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      dialog: {
        name: '',
        code: '',
        store_ids: '',
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Merchants', link: route('merchants'), is_active: true },
      ],
    };
  },

  mounted() {
    //
  },

  watch: {
    form: {
      deep: true,
      handler: throttle(function () {
        const params = {};
        Object.keys(this.form).forEach(key => {
          if (this.form[key] !== null &&
            this.form[key] !== undefined &&
            this.form[key] !== this.defaultValues[key]) {
            params[key] = this.form[key];
          }
        });

        this.$inertia.get('/merchants', pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ['merchants'],
          replace: true
        });
      }, 150),
    },
  },
  methods: {
    async loadMerchants() {
      try {
        const response = await axios.get('/merchants/all');
        this.merchants = response.data;
      } catch (error) {
        console.error('Failed to load merchants:', error);
      }
    },

    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1);
    },

    reset() {
      this.form = {
        search: null,
        trashed: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.get('/merchants', {}, {
        preserveState: true,
        preserveScroll: true,
        only: ['merchants'],
        replace: true
      });
    },

    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },

    createMerchant() {
      this.dialog.name = '';
      this.dialog.code = '';
      this.dialog.store_ids = '';
      this.createDialogIsOpen = true;
    },

    submitCreateMerchant() {
      this.$inertia.post(route('merchants.store'), {
        name: this.dialog.name,
        code: this.dialog.code,
        store_ids: this.dialog.store_ids,
      }, {
        preserveScroll: true,
        onSuccess: () => {
          this.dialog.name = '';
          this.dialog.code = '';
          this.dialog.store_ids = '';
          this.errors = {};
          this.createDialogIsOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        },
      });
    },
  },

  computed: {
    roleOptionValues() {
      return this.roles.map(role => ({ ...role, value: role.name }));
    },

    merchantOptionValues() {
      return this.merchants.map(merchant => ({ ...merchant, value: merchant.name }));
    }
  }
};
</script>