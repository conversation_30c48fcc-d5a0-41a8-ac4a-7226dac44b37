<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
  
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    
    <div>
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <form @submit.prevent="update">
              <div class="grid md:grid-cols-2 gap-4 mb-4">
                <TextInput v-model="form.name" :error="form.errors.name" label="Merchant" />

                <TextInput v-model="form.code" :error="form.errors.code" label="Code" />

                <TextInput v-model="form.store_ids" :error="form.errors.store_ids" label="Store Ids" />
              </div>

              <div class="flex items-center justify-end gap-4 flex-wrap">
                <Button v-if="!merchant.deleted_at" variant="destructive" @click="destroy" type="button"
                  label="Delete Merchant" />
                <Button type="submit" label="Update Merchant" :loading="form.processing" />
              </div>
            </form>

          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import { SelectItem } from '@/Components/ui/select';
import FileInput from '@/Shared/FileInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Label } from '@/Components/ui/label';
import Button from '@/Shared/Button.vue';
import axios from 'axios';
import { route } from 'ziggy-js';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Check } from 'lucide-vue-next';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Separator } from '@/Components/ui/separator';
import TextareaInput from '@/Shared/TextareaInput.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    SelectInput,
    SelectItem,
    FileInput,
    SwitchInput,
    Label,
    Button,
    Breadcrumb,
    CommandItem,
    Check,
    TabsContent,
    TableRow,
    TableCell,
    TableHead,
    TableHeader,
    TableBody,
    Table,
    DisplayLabel,
    TabsTrigger,
    TabsList,
    Tabs,
    Separator,
    TextareaInput
  },
  layout: Layout,
  props: {
    merchant: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.merchant.name,
        code: this.merchant.code,
        store_ids: this.merchant.store_ids,
      }),
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Merchants', link: route('merchants') },
        { name: 'Edit', link: route('merchants.edit', this.merchant.id) },
      ],
    };
  },
  mounted() {
    //
  },

  methods: {
    update() {
      this.form.post(route('merchants.update', this.merchant.id), {
        onSuccess: () => this.form.reset('password', 'photo'),
      });
    },

    destroy() {
      if (confirm('Are you sure you want to delete this merchant?')) {
        this.$inertia.delete(route('merchants.destroy', this.merchant.id));
      }
    },
  },

  computed: {
    //
  }
};
</script>
