<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="User Referrals" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Multi-Select -->
          <div class="space-y-2">
            <MultiSelectCombobox
              v-model="selectedStoreIds"
              :options="storeOptions"
              placeholder="Select Stores"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Date Range Picker - Commented out
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          -->

          <!-- Search and Reset Buttons -->
          <div class="md:col-span-2 flex justify-end space-x-4">
            <UIButton class="min-w-[100px]" type="button" variant="outline" @click="resetFilters">
              Reset
            </UIButton>
            <UIButton class="min-w-[100px]" type="button" @click="reset">
              Search
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">User Referrals</h1>
        <div class="flex gap-2">
          <UIButton class="min-w-[100px]" type="button" @click="exportData" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </UIButton>
        </div>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Store Name</TableHead>
              <TableHead>Referrer Name</TableHead>
              <TableHead>Downline Name</TableHead>
              <TableHead>Total Turnover</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            <TableRow v-for="(item, index) in data?.items" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ item.store_name }}</TableCell>
              <TableCell>{{ item.referrer_name }}</TableCell>
              <TableCell>{{ item.downline_name }}</TableCell>
              <TableCell>{{ formatAmount(item.total_turnover) }}</TableCell>
            </TableRow>

            <TableRow v-if="!data?.items?.length">
              <TableCell class="text-center border-0" colspan="4">No record found.</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>

      <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
        <ShowEntries v-model="form.perPage" />
        <Pagination v-if="page && page.total > 0" class="flex justify-end" :data="page" />
      </div>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Separator } from '@/Components/ui/separator';
import { Button as UIButton } from '@/Components/ui/button';
import { TableRow } from '@/Components/ui/table';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Loader2 } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import axios from 'axios';
import MultiSelectCombobox from '@/Components/ui/multi-select-combobox.vue';
import { CalendarDate } from '@internationalized/date';

export default {
  components: {
    Head,
    Card,
    CardContent,
    Label,
    Separator,
    UIButton,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Pagination,
    Breadcrumb,
    ShowEntries,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Loader2,
    MultiSelectCombobox,
  },
  layout: Layout,
  props: {
    filters: Object,
    startDate: String,
    endDate: String,
    data: Object,
    page: Object,
  },
  data() {
    // Extract time from date strings
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    // Create date objects only if dates are provided, otherwise keep null
    let startCalendarDate = null;
    let endCalendarDate = null;

    if (this.startDate) {
        const startDateObj = new Date(this.startDate);
        startCalendarDate = new CalendarDate(
            startDateObj.getFullYear(),
            startDateObj.getMonth() + 1,
            startDateObj.getDate()
        );

        if (this.startDate.includes(' ')) {
            const timePart = this.startDate.split(' ')[1];
            if (timePart) {
                const timeComponents = timePart.split(':');
                startHour = timeComponents[0];
                startMinute = timeComponents[1];
            }
        }
    }

    if (this.endDate) {
        const endDateObj = new Date(this.endDate);
        endCalendarDate = new CalendarDate(
            endDateObj.getFullYear(),
            endDateObj.getMonth() + 1,
            endDateObj.getDate()
        );

        if (this.endDate.includes(' ')) {
            const timePart = this.endDate.split(' ')[1];
            if (timePart) {
                const timeComponents = timePart.split(':');
                endHour = timeComponents[0];
                endMinute = timeComponents[1];
            }
        }
    }

    return {
        form: {
            perPage: this.filters?.perPage?.toString() || '10',
        },
        selectedStoreIds: this.filters?.store_id ?
            (this.filters.store_id === 'all' ? ['all'] : this.filters.store_id.split(',')) :
            ['all'],
        dateRange: {
            start: startCalendarDate,
            end: endCalendarDate
        },
        startTime: { hour: startHour, minute: startMinute },
        endTime: { hour: endHour, minute: endMinute },
        breadcrumbs: [
            { name: 'Dashboard', link: route('dashboard') },
            { name: 'User Referrals', link: route('user-referrals'), is_active: true },
        ],
        isExporting: false,
        stores: [],
    }
  },
  computed: {
    formattedDateResult() {
      if (!this.dateRange.start) {
        return 'Select date range';
      }

      let dateResult = '';
      const options = { month: 'short', day: 'numeric', year: 'numeric' };

      if (this.dateRange.start) {
        const startDate = new Date(this.dateRange.start.toString());
        dateResult = startDate.toLocaleDateString('en-US', options);

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        const startDate = new Date(this.dateRange.start.toString());
        const endDate = new Date(this.dateRange.end.toString());

        dateResult = `${startDate.toLocaleDateString('en-US', options)} ${this.startTime.hour}:${this.startTime.minute} - ${endDate.toLocaleDateString('en-US', options)} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },
    storeOptions() {
      // Add "All Stores" option
      const allStoresOption = { value: 'all', label: 'All Stores' };

      // Map store data to the format expected by MultiSelectCombobox
      const storeOptions = this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));

      // Return combined options with "All Stores" first
      return [allStoresOption, ...storeOptions];
    }
  },
  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // Helper method to get store ID parameter from selected store IDs
    getStoreIdParam() {
      // If "all" is selected or no stores are selected, use null (all stores)
      if (this.selectedStoreIds.includes('all') || this.selectedStoreIds.length === 0) {
        // If "all" is selected along with other stores, keep only "all"
        if (this.selectedStoreIds.includes('all') && this.selectedStoreIds.length > 1) {
          this.$nextTick(() => {
            this.selectedStoreIds = ['all'];
          });
        }
        return 'all';
      }
      // If only one store is selected (and it's not "all"), use that store ID
      else if (this.selectedStoreIds.length === 1) {
        return this.selectedStoreIds[0];
      }
      // If multiple stores are selected, join them with commas
      else {
        return this.selectedStoreIds.join(',');
      }
    },
    reset() {
      const params = pickBy({
        store_id: this.getStoreIdParam() === 'all' ? null : this.getStoreIdParam(),
        page: 1,
        perPage: this.form.perPage,
      });

      this.$inertia.get(route('user-referrals'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page', 'filters'],
      });
    },
    resetFilters() {
      this.form = {
        perPage: '10',
      };

      this.selectedStoreIds = ['all'];

      this.$inertia.visit('/user-referrals', {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },
    async exportData() {
      this.isExporting = true;

      const params = new URLSearchParams({
        store_id: this.getStoreIdParam() === 'all' ? '' : this.getStoreIdParam(),
      });

      try {
        const response = await fetch(route('user-referrals.export') + `?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        }

        document.body.appendChild(a);
        a.click();

        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },
    async loadStores() {
      try {
        const response = await axios.get('/stores/all');
        this.stores = response.data;
      } catch (error) {
        console.error('Failed to load stores:', error);
      }
    },
  },
  mounted() {
    this.loadStores();
  },
  watch: {
    'form.perPage'(newValue) {
        const params = pickBy({
            store_id: this.getStoreIdParam() === 'all' ? null : this.getStoreIdParam(),
            startDate: this.dateRange.start ? this.formatDateToSQL(new Date(this.dateRange.start.toString())) : null,
            endDate: this.dateRange.end ? this.formatDateToSQL(new Date(this.dateRange.end.toString())) : null,
            page: 1,
            perPage: String(newValue),
        });

        this.$inertia.get(route('user-referrals'), params, {
            preserveState: true,
            preserveScroll: true,
            replace: true,
            only: ['data', 'page', 'filters'],
        });
    },
    filters: {
      deep: true,
      handler(newFilters) {
        if (newFilters) {
          this.form.perPage = newFilters.perPage?.toString() || this.form.perPage;

          if (newFilters.store_id) {
            this.selectedStoreIds = newFilters.store_id === 'all'
              ? ['all']
              : newFilters.store_id.split(',');
          }
        }
      }
    },
    // Watch for changes in selectedStoreIds
    selectedStoreIds: {
      handler() {
        console.log('Selected store IDs changed:', this.selectedStoreIds);
      },
      deep: true
    }
  }
}
</script>