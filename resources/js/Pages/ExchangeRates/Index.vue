<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Exchange Rates" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter v-model="form.search" @reset="reset" />
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Exchange Rates</h1>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow class="text-left font-bold">
            <TableHead>From Currency</TableHead>
            <TableHead>To Currency</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('rate')">
              <SortableHeader title="Rate" field="rate" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Last Updated" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(rate, index) in exchangeRates.data" :key="rate.id"
            :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>
              <div class="flex items-center gap-2">
                <Avatar class="size-6">
                  <AvatarImage :src="rate.from_currency.photo" :alt="rate.from_currency.code" />
                  <AvatarFallback>{{ rate.from_currency.code }}</AvatarFallback>
                </Avatar>
                <span>{{ rate.from_currency.code }}</span>
              </div>
            </TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <Avatar class="size-6">
                  <AvatarImage :src="rate.to_currency.photo" :alt="rate.to_currency.code" />
                  <AvatarFallback>{{ rate.to_currency.code }}</AvatarFallback>
                </Avatar>
                <span>{{ rate.to_currency.code }}</span>
              </div>
            </TableCell>
            <TableCell>{{ rate.rate }}</TableCell>
            <TableCell>{{ rate.created_at }}</TableCell>
          </TableRow>
          <TableRow v-if="exchangeRates.data.length === 0">
            <TableCell class="text-center border-0" colspan="4">No exchange rates found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="exchangeRates" />
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';

export default {
  components: {
    Head,
    Card,
    CardContent,
    SearchFilter,
    Separator,
    ShowEntries,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Breadcrumb,
    Avatar,
    AvatarFallback,
    AvatarImage,
  },
  layout: Layout,
  props: {
    filters: Object,
    exchangeRates: Object,
    sort: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Exchange Rates', link: route('exchange-rates'), is_active: true },
      ]
    };
  },
  watch: {
    form: {
      deep: true,
      handler: throttle(function () {
        const params = {};
        Object.keys(this.form).forEach(key => {
          if (this.form[key] !== null &&
            this.form[key] !== undefined &&
            this.form[key] !== this.defaultValues[key]) {
            params[key] = this.form[key];
          }
        });

        this.$inertia.get('/exchange-rates', pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ['exchangeRates'],
          replace: true
        });
      }, 150),
    },
  },
  methods: {
    reset() {
      this.form = {
        search: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.get('/exchange-rates', {}, {
        preserveState: true,
        preserveScroll: true,
        only: ['exchangeRates'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },
  },
};
</script>