<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Create User" />
    <h1 class="mb-8 text-3xl font-bold">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('users')">Users</Link>
      <span class="text-indigo-400 font-medium">/</span> Create
    </h1>
    <div class="flex justify-center">
      <div class="w-full max-w-5xl bg-white rounded-md shadow overflow-hidden">
        <form @submit.prevent="store">
          <div class="flex flex-wrap -mb-8 -mr-6 p-8">
            <text-input v-model="form.name" :error="form.errors.name" class="pb-8 pr-6 w-full lg:w-1/2" label="Name" />
            <text-input v-model="form.email" :error="form.errors.email" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Email" />
            <text-input v-model="form.password" :error="form.errors.password" class="pb-8 pr-6 w-full lg:w-1/2"
              type="password" autocomplete="new-password" label="Password" />
            <select-input v-model="form.role" :error="form.errors.role" class="pb-8 pr-6 w-full lg:w-1/2" label="Role">
              <option v-for="role in roles" :key="role.id" :value="role.name">{{ role.name }}</option>
            </select-input>
            <file-input v-model="form.photo" :error="form.errors.photo" class="pb-8 pr-6 w-full lg:w-1/2" type="file"
              accept="image/*" label="Avatar" />
            <div class="pb-8 pr-6 w-full lg:w-1/2">
              <label class="block text-sm font-medium text-gray-700">Status</label>
              <label class="switch mt-2">
                <input type="checkbox" v-model="form.is_active" :true-value= true :false-value= false />
                <span class="slider round"></span>
              </label>
              <span class="ml-2 text-sm text-gray-600">
                {{ form.is_active === true ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
          <div class="flex items-center justify-end px-8 py-4 bg-gray-50 border-t border-gray-100">
            <loading-button :loading="form.processing" class="btn-indigo" type="submit">Create User</loading-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3'
import Layout from '@/Shared/Layout.vue'
import FileInput from '@/Shared/FileInput.vue'
import TextInput from '@/Shared/TextInput.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import LoadingButton from '@/Shared/LoadingButton.vue'
import axios from 'axios'

export default {
  components: {
    FileInput,
    Head,
    Link,
    LoadingButton,
    SelectInput,
    TextInput,
  },
  layout: Layout,
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        name: '',
        email: '',
        password: '',
        role: '',
        photo: null,
        is_active: true,
      }),
      roles: [],
    }
  },
  mounted() {
    this.loadRoles();
  },
  methods: {
    async loadRoles() {
      try {
        const response = await axios.get('/roles/all');
        this.roles = response.data;
      } catch (error) {
        console.error('Failed to load roles:', error);
      }
    },
    store() {
      this.form.post(route('users.store'));
    },
  },
}
</script>
