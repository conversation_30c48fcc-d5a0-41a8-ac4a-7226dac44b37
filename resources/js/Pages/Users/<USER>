<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center mb-4 max-w-3xl gap-4 flex-wrap">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">{{ form.is_active === true ? 'Active' :
          'Inactive'
        }}</span>
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
      <img :src="user.photo" class="block w-10 h-10 rounded-full" alt="User avatar" />
    </div>

    <div>
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <form @submit.prevent="update">
              <div class="grid md:grid-cols-2 gap-4 mb-4">
                <TextInput v-model="form.name" :error="form.errors.name" label="First Name" />
                <TextInput v-model="form.email" :error="form.errors.email" label="Email" />
                <TextInput v-model="form.password" :error="form.errors.password" label="Password" type="password" />
                <SelectInput v-model="roleSelectIsOpen" :error="form.errors.role" label="Role"
                  :option-values="roleOptionValues" :value="form.role"
                  popover-content-class="w-[701px] max-w-[calc(100vw-4rem)] md:w-[701px] md:max-w-[calc((100vw-219px-80px)/2)] lg:max-w-[calc((100vw-279px-96px)/2)]">
                  <template #selected>
                    <span class="capitalize">
                      {{ form.role
                        ? roleOptionValues.find(option => option.value === form.role)?.value
                        : `Select Role` }}
                    </span>
                  </template>
                  <CommandItem v-for="option in roleOptionValues" :key="option.value" :value="option.value" @select="(ev) => {
                    if (typeof ev.detail.value === 'string') {
                      form.role = ev.detail.value
                    }
                    form.role = option.value
                    roleSelectIsOpen = false
                  }">
                    <span class="capitalize">{{ option.value }}</span>
                    <Check class="ml-auto h-4 w-4" :class="[
                      form.role === option.value ? 'opacity-100' : 'opacity-0'
                    ]" />
                  </CommandItem>
                </SelectInput>
                
                <!-- Add merchant selection dropdown when role is merchant or admin -->
                <SelectInput v-if="form.role === 'merchant' || form.role === 'admin'" 
                  v-model="merchantSelectIsOpen" 
                  :error="form.errors.merchant" 
                  label="Merchant"
                  :option-values="merchantOptionValues" 
                  :value="form.merchant"
                  popover-content-class="w-[701px] max-w-[calc(100vw-4rem)] md:w-[701px] md:max-w-[calc((100vw-219px-80px)/2)] lg:max-w-[calc((100vw-279px-96px)/2)]">
                  <template #selected>
                    <span class="capitalize">
                      {{ form.merchant
                        ? merchantOptionValues.find(option => option.id === form.merchant)?.name
                        : `Select Merchant` }}
                    </span>
                  </template>
                  <CommandItem v-for="option in merchantOptionValues" :key="option.id" :value="option.name" @select="(ev) => {
                    form.merchant = option.id
                    merchantSelectIsOpen = false
                  }">
                    <span class="capitalize">{{ option.name }}</span>
                    <Check class="ml-auto h-4 w-4" :class="[
                      form.merchant === option.id ? 'opacity-100' : 'opacity-0'
                    ]" />
                  </CommandItem>
                </SelectInput>
                
                <FileInput v-model="form.photo" :error="form.errors.photo" type="file" accept="image/*"
                  label="Avatar" />
                <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status">
                  <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                </SwitchInput>
              </div>
              <div class="flex items-center justify-end gap-4 flex-wrap">
                <Button v-if="!user.deleted_at" variant="destructive" @click="destroy" type="button"
                  label="Delete User" />
                <Button type="submit" label="Update User" :loading="form.processing" />
                <Button type="button" label="Issue PAT" :loading="form.processing" @click="issueToken"/>
              </div>
            </form>

          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import { SelectItem } from '@/Components/ui/select';
import FileInput from '@/Shared/FileInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Label } from '@/Components/ui/label';
import Button from '@/Shared/Button.vue';
import axios from 'axios';
import { route } from 'ziggy-js';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Check } from 'lucide-vue-next';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Separator } from '@/Components/ui/separator';
import TextareaInput from '@/Shared/TextareaInput.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    SelectInput,
    SelectItem,
    FileInput,
    SwitchInput,
    Label,
    Button,
    Breadcrumb,
    CommandItem,
    Check,
    TabsContent,
    TableRow,
    TableCell,
    TableHead,
    TableHeader,
    TableBody,
    Table,
    DisplayLabel,
    TabsTrigger,
    TabsList,
    Tabs,
    Separator,
    TextareaInput
  },
  layout: Layout,
  props: {
    user: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.user.name,
        email: this.user.email,
        password: '',
        role: this.user.role,
        photo: null,
        is_active: this.user.is_active,
        merchant: this.user.merchant_id, // Add merchant field
      }),
      roles: [],
      merchants: [], // Add merchants array
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Users', link: route('users') },
        { name: 'Edit', link: route('users.edit', this.user.id), is_active: true },
      ],
      roleSelectIsOpen: false,
      merchantSelectIsOpen: false // Add merchant select state
    };
  },
  mounted() {
    this.loadRoles();
    this.loadMerchants(); // Load merchants on mount
  },
  methods: {
    async loadRoles() {
      try {
        const response = await axios.get('/roles/all');
        this.roles = response.data;
      } catch (error) {
        console.error('Failed to load roles:', error);
      }
    },
    async loadMerchants() {
      try {
        const response = await axios.get('/merchants/all');
        this.merchants = response.data;
      } catch (error) {
        console.error('Failed to load merchants:', error);
      }
    },
    update() {
      this.form.post(route('users.update', this.user.id), {
        onSuccess: () => this.form.reset('password', 'photo'),
      });
    },
    issueToken() {
      if (confirm('Are you sure you want to reissue Token?')) {
        this.$inertia.post(route('users.issueToken', this.user.id));
      }
    },
    destroy() {
      if (confirm('Are you sure you want to delete this user?')) {
        this.$inertia.delete(route('users.destroy', this.user.id));
      }
    },
  },
  computed: {
    roleOptionValues() {
      return this.roles.map(role => ({ value: role.name }));
    },
    merchantOptionValues() {
      return this.merchants.map(merchant => ({ 
        id: merchant.id, 
        name: merchant.name 
      }));
    }
  }
};
</script>
