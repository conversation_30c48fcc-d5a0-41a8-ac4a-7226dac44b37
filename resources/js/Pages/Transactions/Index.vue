<template>
    <div class="p-4 lg:gap-6 lg:p-6">

        <Head title="Transactions" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        <Card>
            <CardContent class="p-0">
                <div class="p-4">
                    <SearchFilter v-model="form.search" @reset="reset">
                    </SearchFilter>
                </div>
            </CardContent>
        </Card>
        <div>
            <Separator class="my-5" />
        </div>
        <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
            <h1 class="text-2xl font-bold">Transactions</h1>
            <div class="flex items-center gap-3 flex-wrap">
                <Button label="Export CSV" class="cursor-pointer" />
                <Button label="Export PDF" class="cursor-pointer" />
            </div>
        </div>
        <div class="bg-white overflow-x-auto mb-6">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>ID</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead>Reference ID</TableHead>
                        <TableHead>Transaction Type</TableHead>
                        <TableHead>Debit</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow class="bg-[#F1F5F9]">
                        <TableCell>AUD</TableCell>
                        <TableCell>0.00</TableCell>
                        <TableCell>2.7950</TableCell>
                        <TableCell>0.00</TableCell>
                        <TableCell>2.7969</TableCell>
                        <TableCell>0.00</TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell>BTC</TableCell>
                        <TableCell>0.00</TableCell>
                        <TableCell>************</TableCell>
                        <TableCell>0.00</TableCell>
                        <TableCell>************</TableCell>
                        <TableCell>0.00</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
    TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';

export default {
    components: {
        Head,
        Link,
        Card,
        CardContent,
        SearchFilter,
        Label,
        Select,
        SelectContent,
        SelectGroup,
        SelectItem,
        SelectTrigger,
        SelectValue,
        Separator,
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
        Button,
        TextInput,
        SelectInput,
        SwitchInput,
        Table,
        TableBody,
        TableCell,
        TableHead,
        TableHeader,
        TableRow,
        SortableHeader,
        Avatar,
        AvatarFallback,
        AvatarImage,
        Icon,
        Badge,
        Pagination,
        ShowEntries,
        Info,
        Tooltip,
        Breadcrumb,
        CommandItem,
        Check
    },
    layout: Layout,
    props: {
        filters: Object,
        sort: Object,
    },
    data() {
        return {
            defaultValues: {
                sort: 'created_at',
                direction: 'desc',
                perPage: 10,
            },
            form: {
                search: this.filters?.search,
                trashed: this.filters?.trashed,
                sort: this.sort?.field ?? 'created_at',
                direction: this.sort?.direction ?? 'desc',
                perPage: this.filters?.perPage?.toString() || '10',
            },
            errors: {},
            breadcrumbs: [],
        };
    },
    methods: {
        reset() { },
        changeSort(field) {
            this.form.direction = this.form.sort === field
                ? this.form.direction === 'asc'
                    ? 'desc'
                    : 'asc'
                : 'asc';
            this.form.sort = field;
        },
    },
};
</script>