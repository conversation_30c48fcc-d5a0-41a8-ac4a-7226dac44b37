<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="GSC Player Logs" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Store Multi-Select -->
          <div class="space-y-2">
            <MultiSelectCombobox
              v-model="selectedStoreIds"
              :options="storeOptions"
              placeholder="Select Stores"
              search-placeholder="Search stores..."
              empty-text="No stores found"
              label="Store"
            />
          </div>

          <!-- Account Input -->
          <div class="space-y-2">
            <Label>Account:</Label>
            <Input v-model="form.account" type="text" placeholder="Enter account" class="w-full" />
          </div>

          <!-- Username Input -->
          <div class="space-y-2">
            <Label>Username:</Label>
            <Input v-model="form.username" type="text" placeholder="Enter username" class="w-full" />
          </div>

          <!-- Date Range Picker -->
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate date-range-button ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Center the calendar properly -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- Search and Reset Buttons -->
          <div class="md:col-span-2 flex justify-end space-x-4">
            <UIButton class="min-w-[100px]" type="button" variant="outline" @click="resetFilters">
              Reset
            </UIButton>
            <UIButton class="min-w-[100px]" type="button" @click="search">
              Search
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">GSC Player Logs</h1>
        <div class="flex gap-2">
          <UIButton class="min-w-[100px]" type="button" @click="exportData" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </UIButton>
        </div>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead @click="sort('username')" class="cursor-pointer">
                Username
                <span v-if="form.sort === 'username'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('account')" class="cursor-pointer">
                Account
                <span v-if="form.sort === 'account'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('site')" class="cursor-pointer">
                Site
                <span v-if="form.sort === 'site'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('product')" class="cursor-pointer">
                Product
                <span v-if="form.sort === 'product'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('total_turnover')" class="cursor-pointer">
                Total Turnover
                <span v-if="form.sort === 'total_turnover'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('total_bet')" class="cursor-pointer">
                Total Bet
                <span v-if="form.sort === 'total_bet'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('total_payout')" class="cursor-pointer">
                Total Payout
                <span v-if="form.sort === 'total_payout'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('total_win_loss')" class="cursor-pointer">
                Total Win/Loss
                <span v-if="form.sort === 'total_win_loss'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('last_match_time')" class="cursor-pointer">
                Date & Time
                <span v-if="form.sort === 'last_match_time'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            <TableRow v-for="(item, index) in items" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ item.username }}</TableCell>
              <TableCell>{{ item.account }}</TableCell>
              <TableCell>{{ getSiteName(item.site) }}</TableCell>
              <TableCell>{{ getProductName(item.product) }}</TableCell>
              <TableCell>{{ formatAmount(item.total_turnover) }}</TableCell>
              <TableCell>{{ formatAmount(item.total_bet) }}</TableCell>
              <TableCell :class="(item.total_bet + item.total_win_loss) < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(item.total_bet + item.total_win_loss) }}
              </TableCell>
              <TableCell :class="item.total_win_loss < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(item.total_win_loss) }}
              </TableCell>
              <TableCell>{{ item.last_match_time }}</TableCell>
              <TableCell>
                <UIButton variant="ghost" size="icon" @click="openPopover(item)">
                  <Eye class="w-4 h-4" />
                </UIButton>
              </TableCell>
            </TableRow>

            <TableRow v-if="!items.length">
              <TableCell class="text-center border-0" colspan="9">No record found.</TableCell>
            </TableRow>
          </TableBody>

          <tfoot v-if="items.length > 0" class="bg-gray-50 font-medium text-sm">
            <tr>
              <td class="px-2 py-1 text-center" colspan="4">Grand Total:</td>
              <td class="px-2 py-1">{{ formatAmount(grandTotals.total_turnover) }}</td>
              <td class="px-2 py-1">{{ formatAmount(grandTotals.total_bet) }}</td>
              <td class="px-2 py-1" :class="(grandTotals.total_bet + grandTotals.total_win_loss) < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(grandTotals.total_bet + grandTotals.total_win_loss) }}
              </td>
              <td class="px-2 py-1" :class="grandTotals.total_win_loss < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(grandTotals.total_win_loss) }}
              </td>
              <td class="px-2 py-1" colspan="2"></td>
            </tr>
          </tfoot>
        </Table>
      </div>

      <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
        <ShowEntries v-model="form.perPage" />
        <Pagination v-if="page && page.total > 0" class="flex justify-end" :data="page" />
      </div>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Input } from '@/Components/ui/input';
import { Separator } from '@/Components/ui/separator';
import { Button as UIButton } from '@/Components/ui/button';
import { TableRow } from '@/Components/ui/table';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Eye, Loader2, CalendarIcon } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { CalendarDate } from '@internationalized/date';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import GameLogTraits from '@/Traits/GameLogTraits';
import MultiSelectCombobox from '@/Components/ui/multi-select-combobox.vue';

export default {
  mixins: [GameLogTraits],
  components: {
    Head,
    Card,
    CardContent,
    Label,
    Input,
    Separator,
    UIButton,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Badge,
    Pagination,
    Breadcrumb,
    ShowEntries,
    Eye,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Loader2,
    CalendarIcon,
    RangeCalendar,
    Popover,
    PopoverContent,
    PopoverTrigger,
    MultiSelectCombobox,
  },
  layout: Layout,
  props: {
    filters: Object,
    startDate: String,
    endDate: String,
    data: {
      type: Object,
      default: () => ({
        items: [],
        grand_total: {
          total_turnover: 0,
          total_bet: 0,
          total_win_loss: 0
        }
      })
    },
    stores: {
      type: Array,
      default: () => []
    },
    page: {
      type: Object,
      default: () => ({})
    }
  },

  computed: {
    items() {
      return this.data?.items || [];
    },
    grandTotals() {
      return this.data?.grand_total || {
        total_turnover: 0,
        total_bet: 0,
        total_win_loss: 0
      };
    },
    storeOptions() {
      // Add "All Stores" option
      const allStoresOption = { value: 'all', label: 'All Stores' };

      // Map store data to the format expected by MultiSelectCombobox
      const storeOptions = this.stores.map(store => ({
        value: store.id.toString(),
        label: store.name
      }));

      // Return combined options with "All Stores" first
      return [allStoresOption, ...storeOptions];
    },
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        const startDate = new Date(this.dateRange.start.toString());
        const options = { month: 'short', day: 'numeric', year: 'numeric' };
        dateResult = startDate.toLocaleDateString('en-US', options);

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        const startDate = new Date(this.dateRange.start.toString());
        const endDate = new Date(this.dateRange.end.toString());
        const options = { month: 'short', day: 'numeric', year: 'numeric' };

        dateResult = `${startDate.toLocaleDateString('en-US', options)} ${this.startTime.hour}:${this.startTime.minute} - ${endDate.toLocaleDateString('en-US', options)} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    }
  },

  data() {
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    if (this.startDate && this.startDate.includes(' ')) {
      const timePart = this.startDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        startHour = timeComponents[0];
        startMinute = timeComponents[1];
      }
    }

    if (this.endDate && this.endDate.includes(' ')) {
      const timePart = this.endDate.split(' ')[1];
      if (timePart) {
        const timeComponents = timePart.split(':');
        endHour = timeComponents[0];
        endMinute = timeComponents[1];
      }
    }

    const startDateObj = new Date(this.startDate);
    const endDateObj = new Date(this.endDate);

    return {
      form: {
        account: this.filters?.account || '',
        username: this.filters?.username || '',
        perPage: this.filters?.perPage?.toString() || '10',
        sort: this.filters?.sort || 'last_match_time',
        direction: this.filters?.direction || 'desc',
      },
      selectedStoreIds: this.filters?.store_id ?
        (this.filters.store_id === 'all' ? ['all'] : this.filters.store_id.split(',')) :
        ['all'],
      dateRange: {
        start: new CalendarDate(startDateObj.getFullYear(), startDateObj.getMonth() + 1, startDateObj.getDate()),
        end: new CalendarDate(endDateObj.getFullYear(), endDateObj.getMonth() + 1, endDateObj.getDate())
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Game Logs', link: route('game-logs.gsc-player-logs'), is_active: true },
      ],
      isExporting: false,
    }
  },

  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    openPopover(item) {
      const params = pickBy({
        account: item.account,
        page: 1,
        perPage: this.form.perPage,
      });

      this.$inertia.get(route('game-logs.gsc-player-detail-logs'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
      });
    },

    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // Helper method to get store ID parameter from selected store IDs
    getStoreIdParam() {
      // If "all" is selected or no stores are selected, use null (all stores)
      if (this.selectedStoreIds.includes('all') || this.selectedStoreIds.length === 0) {
        // If "all" is selected along with other stores, keep only "all"
        if (this.selectedStoreIds.includes('all') && this.selectedStoreIds.length > 1) {
          this.$nextTick(() => {
            this.selectedStoreIds = ['all'];
          });
        }
        return 'all';
      }
      // If only one store is selected (and it's not "all"), use that store ID
      else if (this.selectedStoreIds.length === 1) {
        return this.selectedStoreIds[0];
      }
      // If multiple stores are selected, join them with commas
      else {
        return this.selectedStoreIds.join(',');
      }
    },

    search() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = pickBy({
        store_id: this.getStoreIdParam() === 'all' ? null : this.getStoreIdParam(),
        account: this.form.account,
        username: this.form.username,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        page: 1,
        perPage: this.form.perPage,
        sort: this.form.sort,
        direction: this.form.direction,
      });

      this.$inertia.get(route('game-logs.gsc-player-logs'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page', 'filters'],
      });
    },

    sort(field) {
      // If clicking on the same field, toggle direction
      if (this.form.sort === field) {
        this.form.direction = this.form.direction === 'asc' ? 'desc' : 'asc';
      } else {
        this.form.sort = field;
        this.form.direction = 'desc'; // Default to descending when changing fields
      }

      this.search();
    },

    resetFilters() {
      this.form = {
        account: '',
        username: '',
        perPage: '10',
        sort: 'last_match_time',
        direction: 'desc',
      };

      this.selectedStoreIds = ['all'];

      this.$inertia.visit('/game-logs/gsc-player-logs', {
        method: 'get',
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },

    async exportData() {
      this.isExporting = true;

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = new URLSearchParams({
        store_id: this.getStoreIdParam() === 'all' ? '' : this.getStoreIdParam(),
        account: this.form.account || '',
        username: this.form.username || '',
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        sort: this.form.sort,
        direction: this.form.direction,
      });

      try {
        const response = await fetch(route('game-logs.gsc-player-logs.export') + `?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        } else {
          a.download = 'gsc-player-logs-export.xlsx';
        }

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },
  },

  watch: {
    'form.perPage'(newValue) {
      const params = pickBy({
        store_id: this.getStoreIdParam() === 'all' ? null : this.getStoreIdParam(),
        account: this.form.account,
        username: this.form.username,
        page: 1,
        perPage: newValue,
        sort: this.form.sort,
        direction: this.form.direction,
      });

      this.$inertia.get(route('game-logs.gsc-player-logs'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page'],
      });
    },
    // Watch for changes in selectedStoreIds
    selectedStoreIds: {
      handler() {
        console.log('Selected store IDs changed:', this.selectedStoreIds);
      },
      deep: true
    },
    // Watch for changes in filters
    filters: {
      deep: true,
      handler(newFilters) {
        if (newFilters) {
          this.form.perPage = newFilters.perPage?.toString() || this.form.perPage;
          this.form.account = newFilters.account || this.form.account;
          this.form.username = newFilters.username || this.form.username;

          if (newFilters.store_id) {
            this.selectedStoreIds = newFilters.store_id === 'all'
              ? ['all']
              : newFilters.store_id.split(',');
          }
        }
      }
    }
  }
}
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
