<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="GSC Player Detail Logs" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <Card>
      <CardContent class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Date Range Picker with time selection -->
          <div class="space-y-2">
            <Label>Date Range:</Label>
            <Popover>
              <PopoverTrigger as-child>
                <UIButton variant="outline"
                  :class="`w-full justify-start text-left font-normal truncate ${!dateRange ? 'text-muted-foreground' : ''}`">
                  <CalendarIcon class="mr-2 h-4 w-4 flex-shrink-0" />
                  <span class="truncate block">{{ formattedDateResult }}</span>
                </UIButton>
              </PopoverTrigger>
              <PopoverContent class="w-[300px] sm:w-[600px] p-0">
                <div class="p-3">
                  <!-- Responsive calendar - 1 month on mobile, 2 months on desktop -->
                  <div class="flex justify-center">
                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="1"
                      class="sm:hidden mx-auto"
                      @update:start-value="(startDate) => dateRange.start = startDate" />

                    <RangeCalendar v-model="dateRange" initial-focus :number-of-months="2"
                      class="hidden sm:block"
                      @update:start-value="(startDate) => dateRange.start = startDate" />
                  </div>

                  <Separator class="my-4" />

                  <!-- Time picker with improved mobile layout -->
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 px-1 sm:px-3 pb-3">
                    <div class="flex flex-col">
                      <Label for="start-time" class="mb-2 text-center font-medium">Start Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="startTime.hour" :default-value="startTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="startTime.minute" :default-value="startTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="startTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div class="flex flex-col">
                      <Label for="end-time" class="mb-2 text-center font-medium">End Time</Label>
                      <div class="flex items-center justify-center space-x-2">
                        <Select v-model="endTime.hour" :default-value="endTime.hour">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.hour" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="hour in 24" :key="hour" :value="(hour - 1).toString().padStart(2, '0')">
                                {{ (hour - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                        <span>:</span>
                        <Select v-model="endTime.minute" :default-value="endTime.minute">
                          <SelectTrigger class="w-[70px] sm:w-[100px] px-2 sm:px-3">
                            <SelectValue :placeholder="endTime.minute" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem v-for="minute in 60" :key="minute" :value="(minute - 1).toString().padStart(2, '0')">
                                {{ (minute - 1).toString().padStart(2, '0') }}
                              </SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          <!-- Search and Reset Buttons -->
          <div class="md:col-span-2 flex justify-end space-x-4">
            <UIButton class="min-w-[100px]" type="button" variant="outline" @click="resetFilters">
              Reset
            </UIButton>
            <UIButton class="min-w-[100px]" type="button" @click="reset">
              Search
            </UIButton>
          </div>
        </div>
      </CardContent>
    </Card>

    <div>
      <Separator class="my-6" />
    </div>

    <div class="space-y-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">GSC Player Detail Logs</h1>
        <div class="flex gap-2">
          <UIButton class="min-w-[100px]" type="button" @click="exportData" :disabled="isExporting">
            <Loader2 v-if="isExporting" class="w-4 h-4 mr-2 animate-spin" />
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </UIButton>
        </div>
      </div>

      <div class="bg-white rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Username</TableHead>
              <TableHead>Account</TableHead>
              <TableHead>Site</TableHead>
              <TableHead>Product</TableHead>
              <TableHead @click="sort('turnover')" class="cursor-pointer">
                Turnover
                <span v-if="form.sort === 'turnover'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('bet')" class="cursor-pointer">
                Bet
                <span v-if="form.sort === 'bet'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('payout')" class="cursor-pointer">
                Payout
                <span v-if="form.sort === 'payout'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead @click="sort('win_loss')" class="cursor-pointer">
                Win/Loss
                <span v-if="form.sort === 'win_loss'" class="ml-1">
                  {{ form.direction === 'asc' ? '↑' : '↓' }}
                </span>
              </TableHead>
              <TableHead>Match Time</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            <TableRow v-for="(item, index) in data.items" :key="index" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ item.username || '-' }}</TableCell>
              <TableCell>{{ item.account }}</TableCell>
              <TableCell>{{ getSiteName(item.site) }}</TableCell>
              <TableCell>{{ getProductName(item.product) }}</TableCell>
              <TableCell>{{ formatAmount(item.turnover) }}</TableCell>
              <TableCell>{{ formatAmount(item.bet) }}</TableCell>
              <TableCell :class="(Number(item.bet) + Number(item.win_loss)) < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(Number(item.bet) + Number(item.win_loss)) }}
              </TableCell>
              <TableCell :class="item.win_loss < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(item.win_loss) }}
              </TableCell>
              <TableCell>{{ item.match_time_mas }}</TableCell>
            </TableRow>

            <TableRow v-if="data.items.length === 0">
              <TableCell class="text-center border-0" colspan="7">No record found.</TableCell>
            </TableRow>
          </TableBody>
          <tfoot v-if="items.length > 0" class="bg-gray-50 font-medium text-sm">
            <tr>
              <td class="px-2 py-1 text-center" colspan="4">Grand Total:</td>
              <td class="px-2 py-1">{{ formatAmount(calculateTotal('turnover')) }}</td>
              <td class="px-2 py-1">{{ formatAmount(calculateTotal('bet')) }}</td>
              <td class="px-2 py-1" :class="(calculateTotal('bet') + calculateTotal('win_loss')) < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(calculateTotal('bet') + calculateTotal('win_loss')) }}
              </td>
              <td class="px-2 py-1" :class="calculateTotal('win_loss') < 0 ? 'text-red-600 font-medium' : ''">
                {{ formatAmount(calculateTotal('win_loss')) }}
              </td>
              <td class="px-2 py-1"></td>
            </tr>
          </tfoot>
        </Table>
      </div>

      <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
        <ShowEntries v-model="form.perPage" />
        <Pagination v-if="page && page.total > 0" class="flex justify-end" :data="page" />
      </div>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { Label } from '@/Components/ui/label';
import { Input } from '@/Components/ui/input';
import { Separator } from '@/Components/ui/separator';
import { Button as UIButton } from '@/Components/ui/button';
import { TableRow } from '@/Components/ui/table';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Eye, Loader2, CalendarIcon } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import { CalendarDate } from '@internationalized/date';
import GameLogTraits from '@/Traits/GameLogTraits';

export default {
  mixins: [GameLogTraits],
  components: {
    Head,
    Card,
    CardContent,
    Label,
    Input,
    Separator,
    UIButton,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Badge,
    Pagination,
    Breadcrumb,
    ShowEntries,
    Eye,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Popover,
    PopoverContent,
    PopoverTrigger,
    RangeCalendar,
    CalendarIcon,
    Loader2,
  },
  layout: Layout,
  props: {
    filters: Object,
    startDate: String,
    endDate: String,
    data: {
      type: Object,
      default: () => ({
        items: []
      })
    },
    page: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    let startHour = "00";
    let startMinute = "00";
    let endHour = "23";
    let endMinute = "59";

    const now = new Date();
    let startDateObj = now;
    let endDateObj = now;

    if (this.startDate) {
      startDateObj = new Date(this.startDate);
      if (this.startDate.includes(' ')) {
        const timePart = this.startDate.split(' ')[1];
        if (timePart) {
          const timeComponents = timePart.split(':');
          startHour = timeComponents[0];
          startMinute = timeComponents[1];
        }
      }
    }

    if (this.endDate) {
      endDateObj = new Date(this.endDate);
      if (this.endDate.includes(' ')) {
        const timePart = this.endDate.split(' ')[1];
        if (timePart) {
          const timeComponents = timePart.split(':');
          endHour = timeComponents[0];
          endMinute = timeComponents[1];
        }
      }
    }

    return {
      form: {
        account: this.filters?.account || '',
        perPage: this.filters?.perPage?.toString() || '10',
        sort: this.filters?.sort || '',
        direction: this.filters?.direction || '',
      },
      dateRange: {
        start: new CalendarDate(startDateObj.getFullYear(), startDateObj.getMonth() + 1, startDateObj.getDate()),
        end: new CalendarDate(endDateObj.getFullYear(), endDateObj.getMonth() + 1, endDateObj.getDate())
      },
      startTime: {
        hour: startHour,
        minute: startMinute
      },
      endTime: {
        hour: endHour,
        minute: endMinute
      },
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Game Logs', link: route('game-logs.gsc-player-logs') },
        { name: 'Player Detail', link: route('game-logs.gsc-player-detail-logs'), is_active: true },
      ],
      isExporting: false,
    }
  },

  computed: {
    formattedDateResult() {
      let dateResult = 'Pick a date';

      if (this.dateRange.start) {
        const startDate = new Date(this.dateRange.start.toString());
        const options = { month: 'short', day: 'numeric', year: 'numeric' };
        dateResult = startDate.toLocaleDateString('en-US', options);

        if (this.startTime.hour && this.startTime.minute) {
          dateResult += ` ${this.startTime.hour}:${this.startTime.minute}`;
        }
      }

      if (this.dateRange.end) {
        const startDate = new Date(this.dateRange.start.toString());
        const endDate = new Date(this.dateRange.end.toString());
        const options = { month: 'short', day: 'numeric', year: 'numeric' };

        dateResult = `${startDate.toLocaleDateString('en-US', options)} ${this.startTime.hour}:${this.startTime.minute} - ${endDate.toLocaleDateString('en-US', options)} ${this.endTime.hour}:${this.endTime.minute}`;
      }

      return dateResult;
    },
    items() {
      return this.data?.items || [];
    }
  },

  methods: {
    formatAmount(value) {
      return Number(value || 0).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    formatDateToSQL(date) {
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    search() {
      const params = pickBy({
        account: this.form.account,
        page: 1,
        perPage: this.form.perPage,
      });

      this.$inertia.get(route('game-logs.gsc-player-detail-logs'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page', 'filters'],
      });
    },

    sort(field) {
      // If clicking on the same field, toggle direction
      if (this.form.sort === field) {
        this.form.direction = this.form.direction === 'asc' ? 'desc' : 'asc';
      } else {
        this.form.sort = field;
        this.form.direction = 'desc'; // Default to descending when changing fields
      }

      this.reset();
    },

    reset() {
      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = pickBy({
        account: this.form.account,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        page: 1,
        perPage: this.form.perPage,
        sort: this.form.sort,
        direction: this.form.direction,
      });

      this.$inertia.get(route('game-logs.gsc-player-detail-logs'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page', 'filters'],
      });
    },

    resetFilters() {
      const currentAccount = this.form.account;

      this.form = {
        account: currentAccount,
        perPage: '10',
      };

      this.startTime = {
        hour: "00",
        minute: "00"
      };

      this.endTime = {
        hour: "23",
        minute: "59"
      };

      const today = new Date();
      this.dateRange = {
        start: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate()),
        end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
      };

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(0, 0, 0);

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(23, 59, 59);

      const params = {
        account: currentAccount,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      };

      this.$inertia.visit('/game-logs/gsc-player-detail-logs', {
        method: 'get',
        data: params,
        preserveState: false,
        preserveScroll: false,
        replace: true,
      });
    },

    async exportData() {
      this.isExporting = true;

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = new URLSearchParams({
        account: this.form.account || '',
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
      });

      try {
        const response = await fetch(route('game-logs.gsc-player-detail-logs.export') + `?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          }
        } else {
          // Include account in filename if available
          const accountInfo = this.form.account ? `_${this.form.account}` : '';
          a.download = `gsc-player-detail-logs${accountInfo}_${new Date().toISOString().slice(0,10)}.xlsx`;
        }

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },

    calculateTotal(field) {
      return this.items.reduce((total, item) => {
        return total + Number(item[field] || 0);
      }, 0);
    },
  },

  watch: {
    page(newPage) {
      // This ensures that when the page prop changes due to pagination,
      // we update our local form state to match the server state
      if (this.filters?.sort) {
        this.form.sort = this.filters.sort;
      }
      if (this.filters?.direction) {
        this.form.direction = this.filters.direction;
      }
    },

    'form.perPage'(newValue) {
      if (!this.dateRange.start || !this.dateRange.end) {
        const today = new Date();
        this.dateRange = {
          start: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate()),
          end: new CalendarDate(today.getFullYear(), today.getMonth() + 1, today.getDate())
        };
      }

      const startDateWithTime = new Date(this.dateRange.start.toString());
      startDateWithTime.setHours(parseInt(this.startTime.hour), parseInt(this.startTime.minute));

      const endDateWithTime = new Date(this.dateRange.end.toString());
      endDateWithTime.setHours(parseInt(this.endTime.hour), parseInt(this.endTime.minute));

      const params = pickBy({
        account: this.form.account,
        startDate: this.formatDateToSQL(startDateWithTime),
        endDate: this.formatDateToSQL(endDateWithTime),
        page: 1,
        perPage: newValue,
        sort: this.form.sort,
        direction: this.form.direction,
      });

      this.$inertia.get(route('game-logs.gsc-player-detail-logs'), params, {
        preserveState: true,
        preserveScroll: true,
        replace: true,
        only: ['data', 'page'],
      });
    },
  },

  mounted() {
    if (this.endTime.hour === "00" && this.endTime.minute === "00") {
      this.endTime.hour = "23";
      this.endTime.minute = "59";
    }
  }
}
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}

.cursor-pointer:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
</style>
