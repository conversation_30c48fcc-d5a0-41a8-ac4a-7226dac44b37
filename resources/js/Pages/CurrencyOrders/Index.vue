<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Currency Orders" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter v-model="form.search" @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Trashed:</Label>
              <Select v-model="form.trashed" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Please Select Trash" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="with">
                      With Trashed
                    </SelectItem>
                    <SelectItem value="only">
                      Only Trashed
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Currency Order Overview</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <Button label="Create" icon="plus" class="cursor-pointer" @click="createCurrencyOrder" />
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create Currency Order</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateCurrencyOrder">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-4 max-h-[75vh]">
                  <div class="grid gap-2">
                    <Label>Order Type:</Label>
                    <Select @update:modelValue="handleTypeChange" :value="dialog.currency_order_type_id"
                      class="form-select mt-1 w-full">
                      <SelectTrigger>
                        <SelectValue placeholder="Select Order Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectItem v-for="type in currencyOrderTypes" :key="type.id" :value="type.value.toString()">
                            {{
                              type.name
                            }}</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                    <span v-if="errors.currency_order_type_id" class="text-red-600 text-sm">{{
                      errors.currency_order_type_id }}</span>
                  </div>
                  <SelectInput v-if="isFieldVisible('customer')" v-model="customerSelectIsOpen"
                    :error="errors.customer_id" label="Customer" :option-values="customerOptionValues"
                    :value="dialog.customer_id" popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]"
                    :disabled="['e', 'r'].includes(dialog.currency_order_type_id)">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedCustomerOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedCustomerOptionValue?.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ selectedCustomerOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.customer_id
                          ? selectedCustomerOptionValue?.value
                          : `Select Customer` }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in customerOptionValues" :key="option.value" :value="option.value"
                      @select="(ev) => {
                        if (typeof ev.detail.value === 'string') {
                          dialog.customer_id = ev.detail.value
                        }
                        dialog.customer_id = option.id.toString()
                        customerSelectIsOpen = false
                      }">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        dialog.customer_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                      ]" />
                    </CommandItem>
                  </SelectInput>
                  <SelectInput v-if="isFieldVisible('in_currency_id')" v-model="inCurrencySelectIsOpen"
                    :error="errors.in_currency_id" label="Currency In" :option-values="inCurrencyOptionValues"
                    :value="dialog.in_currency_id" popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedInCurrencyOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedInCurrencyOptionValue?.photo" alt="Agent Photo" />
                          <AvatarFallback>{{ selectedInCurrencyOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.in_currency_id
                          ? selectedInCurrencyOptionValue?.value
                          : `Select Currency In` }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in inCurrencyOptionValues" :key="option.value" :value="option.value"
                      @select="(ev) => {
                        if (typeof ev.detail.value === 'string') {
                          dialog.in_currency_id = ev.detail.value
                        }
                        dialog.in_currency_id = option.id.toString()
                        inCurrencySelectIsOpen = false
                      }">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        dialog.in_currency_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                      ]" />
                    </CommandItem>
                  </SelectInput>
                  <SelectInput v-if="isFieldVisible('out_currency_id')" v-model="outCurrencySelectIsOpen"
                    :error="errors.out_currency_id" label="Currency Out" :option-values="outCurrencyOptionValues"
                    :value="dialog.out_currency_id" popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedOutCurrencyOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedOutCurrencyOptionValue?.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ selectedOutCurrencyOptionValue?.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ dialog.out_currency_id
                          ? selectedOutCurrencyOptionValue?.value
                          : `Select Currency Out` }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in outCurrencyOptionValues" :key="option.value" :value="option.value"
                      @select="(ev) => {
                        if (typeof ev.detail.value === 'string') {
                          dialog.out_currency_id = ev.detail.value
                        }
                        dialog.out_currency_id = option.id.toString()
                        outCurrencySelectIsOpen = false
                      }">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        dialog.out_currency_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                      ]" />
                    </CommandItem>
                  </SelectInput>
                  <TextInput v-if="isFieldVisible('exchange_rate')" v-model="dialog.exchange_rate"
                    :error="errors.exchange_rate" label="Exchange Rate" />
                  <TextInput v-if="isFieldVisible('receivable_amount')" v-model="dialog.receivable_amount"
                    :error="errors.receivable_amount" label="Receive Amount" />
                  <TextInput v-if="isFieldVisible('payable_amount')" v-model="dialog.payable_amount"
                    :error="errors.payable_amount" label="Payable Amount" />
                  <TextInput v-if="isFieldVisible('processing_fee')" v-model="dialog.processing_fee"
                    :error="errors.processing_fee" label="Processing Fee" />
                  <div v-if="dialog.currency_order_type_id === 'po'" class="flex items-center gap-4">
                    <div class="flex-grow">
                      <Label>Profit:</Label>
                      <div class="mt-1">RM{{ dialog.profit }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter class="px-2">
                <div class="flex justify-between w-full">
                  <Button 
                    v-if="dialog.currency_order_type_id === 'po'"
                    type="button"
                    variant="destructive"
                    @click="calculateProfit"
                    label="Calculate"
                  />
                  <Button type="submit" label="Create Currency Order" />
                </div>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Time Remaining</TableHead>
            <TableHead>Actions</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Reference</TableHead>
            <TableHead>In</TableHead>
            <TableHead>Out</TableHead>
            <TableHead>Payable Amount <span class="font-normal">(Fulfilled Amount)</span></TableHead>
            <TableHead>Receivable Amount <span class="font-normal">(Fulfilled Amount)</span></TableHead>
            <TableHead>Customer</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(currencyOrder, index) in currencyOrders.data" :key="currencyOrder.id" class="px-2"
            :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ currencyOrder.created_at }}</TableCell>
            <TableCell>
              <Countdown :timestamps="currencyOrder.status_timestamps" />
            </TableCell>
            <TableCell>
              <Link :href="route('currency-orders.edit', currencyOrder.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
            <TableCell>
              <Badge class="text-xs rounded-full min-w-max" :class="{
                '!bg-[#ffc009] !text-primary': currencyOrder.status === 'Pending',
                '!bg-[#009A15]': currencyOrder.status === 'Completed',
                '!bg-[#dc3545]': currencyOrder.status === 'Partially Completed',
              }">
                {{ currencyOrder.status }}
              </Badge>
            </TableCell>
            <TableCell>{{ currencyOrder.reference }}</TableCell>
            <TableCell>{{ currencyOrder.in_currency?.code }}</TableCell>
            <TableCell>{{ currencyOrder.out_currency?.code }}</TableCell>
            <TableCell>{{ currencyOrder.payable_amount }}</TableCell>
            <TableCell>{{ currencyOrder.receivable_amount }}</TableCell>
            <TableCell>{{ currencyOrder.customer }}</TableCell>
          </TableRow>
          <TableRow v-if="currencyOrders.data.length === 0">
            <TableCell class="text-center border-0" colspan="9">No currency orders found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="currencyOrders" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from "@inertiajs/vue3";
import throttle from "lodash/throttle";
import Layout from "@/Shared/Layout.vue";
import pickBy from "lodash/pickBy";
import axios from 'axios';
import { Card, CardContent } from "@/Components/ui/card";
import SearchFilter from "@/Shared/SearchFilter.vue";
import { Label } from "@/Components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/Components/ui/select";
import { Separator } from "@/Components/ui/separator";
import ShowEntries from "@/Shared/ShowEntries.vue";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/Components/ui/dialog";
import Button from "@/Shared/Button.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@/Components/ui/avatar";
import TextInput from "@/Shared/TextInput.vue";
import {
  TableRow
} from "@/Components/ui/table";
import { TableCell, TableHead, TableHeader, TableBody, Table } from "@/Shared/table";
import SortableHeader from "@/Shared/SortableHeader.vue";
import Pagination from "@/Shared/Pagination.vue";
import { Badge } from "@/Components/ui/badge";
import Countdown from "@/Shared/Countdown.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { Check, Info } from "lucide-vue-next";
import Breadcrumb from "@/Shared/Breadcrumb.vue";
import { CommandItem } from "@/Components/ui/command";

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    SelectInput,
    Avatar,
    AvatarFallback,
    AvatarImage,
    TextInput,
    Table,
    TableBody,
    TableHeader,
    TableRow,
    TableCell,
    TableHead,
    SortableHeader,
    Pagination,
    Badge,
    Countdown,
    Tooltip,
    Info,
    Breadcrumb,
    CommandItem,
    Check
  },
  layout: Layout,
  props: {
    filters: Object,
    currencyOrders: Object,
    sort: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: "created_at",
        direction: "desc",
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        trashed: this.filters.trashed,
        sort: this.sort?.field ?? "created_at",
        direction: this.sort?.direction ?? "desc",
        perPage: this.filters.perPage?.toString() || '10',
      },
      dialog: {
        payable_amount: '',
        receivable_amount: '',
        commission: '',
        processing_fee: '',
        exchange_rate: '',
        customer_id: '',
        currency_order_type_id: '',
        in_currency_id: '',
        out_currency_id: '',
        initial_exchange_rate: '',
        profit: '0.00',
      },
      customers: [],
      currencies: [],
      currencyOrderTypes: [],
      specialCustomers: {
        expense: null,
        revenue: null
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Currency Orders', link: route('currency-orders'), is_active: true },
      ],
      customerSelectIsOpen: false,
      inCurrencySelectIsOpen: false,
      outCurrencySelectIsOpen: false,
      rateOperation: null,
      ignoreWatch: false,
      lastModifiedField: null,
    };
  },
  watch: {
    form: {
      deep: true,
      handler: throttle(function () {
        const params = {};
        Object.keys(this.form).forEach((key) => {
          if (
            this.form[key] !== null &&
            this.form[key] !== undefined &&
            this.form[key] !== this.defaultValues[key]
          ) {
            params[key] = this.form[key];
          }
        });

        this.$inertia.get("/currency-orders", pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ["currencyOrders"],
          replace: true,
        });
      }, 150),
    },
    'dialog.in_currency_id': function (newVal, oldVal) {
      if (this.dialog.currency_order_type_id === 'po') {
        if (newVal === this.dialog.out_currency_id) {
          this.dialog.out_currency_id = '';
        }
        this.fetchExchangeRate();
      }
    },
    'dialog.out_currency_id': function (newVal, oldVal) {
      if (this.dialog.currency_order_type_id === 'po') {
        if (newVal === this.dialog.in_currency_id) {
          this.dialog.in_currency_id = '';
        }
        this.fetchExchangeRate();
      }
    },
    'dialog.receivable_amount': function (newVal) {
      if (!this.dialog.exchange_rate || this.ignoreWatch) return;

      this.lastModifiedField = 'receivable';
      this.ignoreWatch = true;
      if (!newVal || newVal === '') {
        this.dialog.payable_amount = '';
      } else {
        const numVal = parseFloat(newVal);
        if (!isNaN(numVal)) {
          const rate = parseFloat(this.dialog.exchange_rate);
          this.dialog.payable_amount = this.rateOperation === 'multiply'
            ? (numVal * rate).toFixed(8)
            : (numVal / rate).toFixed(8);
        }
      }
      this.$nextTick(() => {
        this.ignoreWatch = false;
      });
    },
    'dialog.payable_amount': function (newVal) {
      if (!this.dialog.exchange_rate || this.ignoreWatch) return;

      this.lastModifiedField = 'payable';
      this.ignoreWatch = true;
      if (!newVal || newVal === '') {
        this.dialog.receivable_amount = '';
      } else {
        const numVal = parseFloat(newVal);
        if (!isNaN(numVal)) {
          const rate = parseFloat(this.dialog.exchange_rate);
          this.dialog.receivable_amount = this.rateOperation === 'multiply'
            ? (numVal / rate).toFixed(8)
            : (numVal * rate).toFixed(8);
        }
      }
      this.$nextTick(() => {
        this.ignoreWatch = false;
      });
    },
    'dialog.exchange_rate': function (newVal) {
      if (!newVal || this.ignoreWatch) return;

      this.ignoreWatch = true;
      if (newVal === '') {
        this.dialog.payable_amount = '';
        this.dialog.receivable_amount = '';
      } else {
        const rate = parseFloat(newVal);
        if (!isNaN(rate)) {
          if (this.lastModifiedField === 'receivable') {
            const receivable = parseFloat(this.dialog.receivable_amount);
            if (!isNaN(receivable)) {
              this.dialog.payable_amount = this.rateOperation === 'multiply'
                ? (receivable * rate).toFixed(8)
                : (receivable / rate).toFixed(8);
            }
          } else if (this.lastModifiedField === 'payable') {
            const payable = parseFloat(this.dialog.payable_amount);
            if (!isNaN(payable)) {
              this.dialog.receivable_amount = this.rateOperation === 'multiply'
                ? (payable / rate).toFixed(8)
                : (payable * rate).toFixed(8);
            }
          }
        }
      }
      this.$nextTick(() => {
        this.ignoreWatch = false;
      });
    },
  },
  mounted() {
    this.loadData();
  },
  computed: {
    filteredOutCurrencies() {
      if (!this.dialog.in_currency_id) {
        return this.currencies;
      }
      return this.currencies.filter(
        (currency) => currency.id.toString() !== this.dialog.in_currency_id
      );
    },
    filteredInCurrencies() {
      if (!this.dialog.out_currency_id) {
        return this.currencies;
      }
      return this.currencies.filter(
        (currency) => currency.id.toString() !== this.dialog.out_currency_id
      );
    },
    customerOptionValues() {
      return this.filteredCustomers().map(customer => ({ ...customer, value: customer.name }));
    },
    selectedCustomerOptionValue() {
      return this.customerOptionValues.find(option => option.id.toString() === this.dialog.customer_id);
    },
    inCurrencyOptionValues() {
      return this.currencies.map(currency => ({ ...currency, value: `${currency.name} (${currency.code})` }));
    },
    selectedInCurrencyOptionValue() {
      return this.inCurrencyOptionValues.find(option => option.id.toString() === this.dialog.in_currency_id);
    },
    outCurrencyOptionValues() {
      return this.filteredOutCurrencies.map(currency => ({ ...currency, value: `${currency.name} (${currency.code})` }));
    },
    selectedOutCurrencyOptionValue() {
      return this.outCurrencyOptionValues.find(option => option.id.toString() === this.dialog.out_currency_id);
    },
  },
  methods: {
    async loadData() {
      try {
        const [customersResponse, currenciesResponse, orderTypesResponse] = await Promise.all([
          axios.get('/customers/all'),
          axios.get('/currencies/all'),
          axios.get('/currency-order-types/all'),
        ]);

        this.customers = customersResponse.data;
        this.currencies = currenciesResponse.data;
        this.currencyOrderTypes = orderTypesResponse.data;

        this.specialCustomers.expense = this.customers.find(c => c.name === 'Expense')?.id;
        this.specialCustomers.revenue = this.customers.find(c => c.name === 'Revenue')?.id;
      } catch (error) {
        console.error('Failed to load data:', error);
      }
    },
    handleTypeChange(newType) {
      this.dialog.currency_order_type_id = newType;

      if (newType === 'e') {
        this.dialog.customer_id = this.specialCustomers.expense.toString();
      } else if (newType === 'r') {
        this.dialog.customer_id = this.specialCustomers.revenue.toString();
      } else {
        this.dialog.customer_id = '';
      }

      this.dialog.exchange_rate = '';
    },
    filteredCustomers() {
      if (this.dialog.currency_order_type_id === 'e' || this.dialog.currency_order_type_id === 'r') {
        return this.customers;
      } else {
        return this.customers.filter(
          (customer) => customer.id !== this.specialCustomers.expense && customer.id !== this.specialCustomers.revenue
        );
      }
    },
    reset() {
      this.form = {
        search: null,
        trashed: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.get("/currency-orders", {}, {
        preserveState: true,
        preserveScroll: true,
        only: ["currencyOrders"],
        replace: true,
      });
    },
    changeSort(field) {
      if (field === "created_at") {
        this.form.direction = this.form.direction === "asc" ? "desc" : "asc";
        this.form.sort = field;
      }
    },
    isFieldVisible(field) {
      const visibleFields = {
        e: ['customer', 'out_currency_id', 'payable_amount'],
        po: ['customer', 'in_currency_id', 'out_currency_id', 'exchange_rate', 'receivable_amount', 'payable_amount', 'processing_fee'],
        r: ['customer', 'in_currency_id', 'exchange_rate', 'receivable_amount'],
        tpp: ['customer', 'out_currency_id', 'payable_amount'],
        tpr: ['customer', 'in_currency_id', 'exchange_rate', 'receivable_amount', 'processing_fee'],
      };

      const currentType = this.dialog.currency_order_type_id || '';
      return visibleFields[currentType]?.includes(field);
    },
    createCurrencyOrder() {
      this.dialog.payable_amount = '';
      this.dialog.receivable_amount = '';
      this.dialog.commission = '';
      this.dialog.processing_fee = '';
      this.dialog.exchange_rate = '';
      this.dialog.customer_id = '';
      this.dialog.currency_order_type_id = '';
      this.dialog.in_currency_id = '';
      this.dialog.out_currency_id = '';
      this.createDialogIsOpen = true;
    },
    submitCreateCurrencyOrder() {
      this.$inertia.post(route('currency-orders.store'), {
        payable_amount: this.dialog.payable_amount,
        receivable_amount: this.dialog.receivable_amount,
        commission: this.dialog.commission,
        processing_fee: this.dialog.processing_fee,
        exchange_rate: this.dialog.exchange_rate,
        customer_id: this.dialog.customer_id,
        currency_order_type_id: this.dialog.currency_order_type_id,
        in_currency_id: this.dialog.in_currency_id,
        out_currency_id: this.dialog.out_currency_id,
      }, {
        preserveScroll: true,
        onSuccess: () => {
          this.dialog.payable_amount = '';
          this.dialog.receivable_amount = '';
          this.dialog.commission = '';
          this.dialog.processing_fee = '';
          this.dialog.exchange_rate = '';
          this.dialog.customer_id = '';
          this.dialog.currency_order_type_id = '';
          this.dialog.in_currency_id = '';
          this.dialog.out_currency_id = '';
          this.errors = {};
          this.createDialogIsOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        },
      });
    },
    async fetchExchangeRate() {
      if (this.dialog.in_currency_id &&
        this.dialog.out_currency_id &&
        this.dialog.in_currency_id !== this.dialog.out_currency_id) {
        try {
          const response = await axios.get(route('exchange-rates.find-rate'), {
            params: {
              currency_in_id: this.dialog.in_currency_id,
              currency_out_id: this.dialog.out_currency_id
            }
          });

          if (response.data.success && response.data.rate) {
            this.dialog.exchange_rate = Number(response.data.rate).toFixed(10);
            this.dialog.initial_exchange_rate = this.dialog.exchange_rate;
            this.rateOperation = response.data.operation;

            this.dialog.receivable_amount = '';
            this.dialog.payable_amount = '';
            this.dialog.profit = '0.00';
          }
        } catch (error) {
          console.error('Failed to fetch exchange rate:', error);
        }
      } else {
        this.dialog.exchange_rate = '';
        this.dialog.initial_exchange_rate = '';
        this.rateOperation = null;
        this.dialog.profit = '0.00';
      }
    },
    async calculateProfit() {
      if (!this.dialog.initial_exchange_rate || 
          !this.dialog.exchange_rate || 
          !this.dialog.payable_amount || 
          !this.dialog.receivable_amount ||
          !this.dialog.in_currency_id ||
          !this.dialog.out_currency_id) {
        this.dialog.profit = '0.00';
        return;
      }

      try {
        const response = await axios.post(route('exchange-rates.calculate-profit'), {
          initial_rate: this.dialog.initial_exchange_rate,
          manual_rate: this.dialog.exchange_rate,
          receivable_amount: this.dialog.receivable_amount,
          payable_amount: this.dialog.payable_amount,
          currency_in: this.selectedInCurrencyOptionValue?.code,
          currency_out: this.selectedOutCurrencyOptionValue?.code,
          processing_fee: this.dialog.processing_fee || 0
        });

        if (response.data.success) {
          this.dialog.profit = response.data.profit;
        }
      } catch (error) {
        console.error('Failed to calculate profit:', error);
        this.dialog.profit = '0.00';
      }
    },
  },
};
</script>
