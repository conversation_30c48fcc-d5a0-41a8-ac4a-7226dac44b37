<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Create Currency Order" />
    <h1 class="mb-8 text-3xl font-bold">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('currency-orders')">Currency Orders</Link>
      <span class="text-indigo-400 font-medium">/</span> Create
    </h1>
    <div class="flex justify-center">
      <div class="w-full max-w-5xl bg-white rounded-md shadow overflow-hidden">
        <form @submit.prevent="store">
          <div class="flex flex-wrap -mb-8 -mr-6 p-8">
            <select-input v-model="form.currency_order_type_id" :error="form.errors.currency_order_type_id"
              class="pb-8 pr-6 w-full lg:w-1/2" label="Order Type" @update:modelValue="handleTypeChange">
              <option v-for="type in currencyOrderTypes" :key="type.id" :value="type.value">{{ type.name }}</option>
            </select-input>

            <select-input :key="form.currency_order_type_id" v-if="isFieldVisible('customer')"
              v-model="form.customer_id" :error="form.errors.customer_id" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Customer" :disabled="['e', 'r'].includes(form.currency_order_type_id)">
              <option v-for="customer in customers" :key="customer.id" :value="customer.id">{{ customer.name }}</option>
            </select-input>

            <select-input v-if="isFieldVisible('in_currency_id')" v-model="form.in_currency_id"
              :error="form.errors.in_currency_id" class="pb-8 pr-6 w-full lg:w-1/2" label="In Currency">
              <option v-for="currency in filteredInCurrencies" :key="currency.id" :value="currency.id">{{ currency.name
                }}
              </option>
            </select-input>

            <select-input v-if="isFieldVisible('out_currency_id')" v-model="form.out_currency_id"
              :error="form.errors.out_currency_id" class="pb-8 pr-6 w-full lg:w-1/2" label="Out Currency">
              <option v-for="currency in filteredOutCurrencies" :key="currency.id" :value="currency.id">{{ currency.name
                }}
              </option>
            </select-input>

            <text-input v-if="isFieldVisible('payable_amount')" v-model="form.payable_amount"
              :error="form.errors.payable_amount" class="pb-8 pr-6 w-full lg:w-1/2" label="Payable Amount" />

            <text-input v-if="isFieldVisible('receivable_amount')" v-model="form.receivable_amount"
              :error="form.errors.receivable_amount" class="pb-8 pr-6 w-full lg:w-1/2" label="Receivable Amount" />

            <text-input v-if="isFieldVisible('processing_fee')" v-model="form.processing_fee"
              :error="form.errors.processing_fee" class="pb-8 pr-6 w-full lg:w-1/2" label="Processing Fee" />

            <text-input v-if="isFieldVisible('exchange_rate')" v-model="form.exchange_rate"
              :error="form.errors.exchange_rate" class="pb-8 pr-6 w-full lg:w-1/2" label="Exchange Rate" />
          </div>
          <div class="flex items-center justify-end px-8 py-4 bg-gray-50 border-t border-gray-100">
            <loading-button :loading="form.processing" class="btn-indigo" type="submit">
              Create Currency Order
            </loading-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import LoadingButton from '@/Shared/LoadingButton.vue';
import axios from 'axios';
import { route } from 'ziggy-js';

export default {
  components: {
    Head,
    Link,
    LoadingButton,
    TextInput,
    SelectInput,
  },
  layout: Layout,
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        payable_amount: '',
        receivable_amount: '',
        commission: '',
        processing_fee: '',
        exchange_rate: '',
        customer_id: '',
        currency_order_type_id: '',
        in_currency_id: '',
        out_currency_id: '',
      }),
      customers: [],
      currencies: [],
      currencyOrderTypes: [],
      specialCustomers: {
        expense: null,
        revenue: null
      }
    };
  },
  mounted() {
    this.loadData();
  },
  computed: {
    filteredInCurrencies() {
      return this.currencies.filter(currency => currency.id !== this.form.out_currency_id);
    },
    filteredOutCurrencies() {
      return this.currencies.filter(currency => currency.id !== this.form.in_currency_id);
    }
  },
  methods: {
    async loadData() {
      try {
        const [customersResponse, currenciesResponse, orderTypesResponse] = await Promise.all([
          axios.get('/customers/all'),
          axios.get('/currencies/all'),
          axios.get('/currency-order-types/all'),
        ]);

        this.customers = customersResponse.data;
        this.currencies = currenciesResponse.data;
        this.currencyOrderTypes = orderTypesResponse.data;

        this.specialCustomers.expense = this.customers.find(c => c.name === 'Expense')?.id;
        this.specialCustomers.revenue = this.customers.find(c => c.name === 'Revenue')?.id;
      } catch (error) {
        console.error('Failed to load data:', error);
      }
    },
    handleTypeChange(newType) {
      this.form.in_currency_id = '';
      this.form.out_currency_id = '';

      if (newType === 'e') {
        this.form.customer_id = this.specialCustomers.expense;
      } else if (newType === 'r') {
        this.form.customer_id = this.specialCustomers.revenue;
      } else {
        this.form.customer_id = '';
      }
    },
    isFieldVisible(field) {
      const visibleFields = {
        e: ['customer', 'out_currency_id', 'payable_amount'],
        po: ['customer', 'in_currency_id', 'out_currency_id', 'exchange_rate', 'receivable_amount', 'payable_amount', 'processing_fee'],
        r: ['customer', 'in_currency_id', 'exchange_rate', 'receivable_amount'],
        tpp: ['customer', 'out_currency_id', 'payable_amount'],
        tpr: ['customer', 'in_currency_id', 'exchange_rate', 'receivable_amount', 'processing_fee'],
      };

      const currentType = this.form.currency_order_type_id || '';
      return visibleFields[currentType]?.includes(field);
    },
    store() {
      this.form.post(route('currency-orders.store'));
    },
  },
};
</script>
