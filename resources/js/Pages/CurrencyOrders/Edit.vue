<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`Edit Currency Order - ${form.reference}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start max-w-3xl mb-4">
      <h1 class="text-2xl font-bold">
        Edit {{ form.reference }}
      </h1>
    </div>
    <div class="mb-6">
      <Tabs default-value="currency-order-details">
        <TabsList class="max-w-[1200px] md:grid-cols-6">
          <TabsTrigger value="currency-order-details">
            Currency Order Details
          </TabsTrigger>
          <TabsTrigger value="invoice">
            Invoice
          </TabsTrigger>
          <TabsTrigger value="notes">
            Notes
          </TabsTrigger>
          <TabsTrigger value="feeds">
            Feeds
          </TabsTrigger>
          <TabsTrigger value="audits">
            Audits
          </TabsTrigger>
          <TabsTrigger value="official-receipts">
            Official Receipts
          </TabsTrigger>
        </TabsList>
        <TabsContent value="currency-order-details"
          class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <div>
            <Card>
              <CardContent class="p-0">
                <div class="p-4">
                  <form @submit.prevent="update">
                    <div class="xl:columns-2 space-y-4 mb-5">
                      <div v-for="(value, label) in fields" :key="label" class="break-inside-avoid">
                        <DisplayLabel :label="label" :value="value" />
                      </div>
                      <div class="break-inside-avoid">
                        <TextareaInput label-side v-model="form.remarks" :error="form.errors.remarks" label="Remarks" />
                      </div>
                    </div>
                    <div class="flex items-center justify-end gap-4 flex-wrap">
                      <Button type="submit" label="Update Currency Order" :loading="form.processing" />
                      <Dialog :open="isDialogOpen" @update:open="setDialogOpen">
                        <DialogTrigger asChild v-if="showAddTransactionButton">
                          <Button type="button" label="Add Transaction" />
                        </DialogTrigger>
                        <DialogContent class="sm:max-w-[600px]">
                          <DialogHeader>
                            <DialogTitle>Add Transaction</DialogTitle>
                            <DialogDescription>
                              {{ getDialogDescription }}
                            </DialogDescription>
                          </DialogHeader>
                          <form @submit.prevent="addTransaction">
                            <div class="grid gap-6 py-4">
                              <div class="space-y-4">
                                <SelectInput v-model="currencySelectIsOpen" :error="transactionForm.errors.currency_id"
                                  label="Currency" :option-values="currencyOptionValues"
                                  :value="transactionForm.currency_id"
                                  popover-content-class="w-[calc(100vw-3rem)] max-w-[550px]">
                                  <template #selected>
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="selectedCurrencyOptionValue?.photo" class="size-6">
                                        <AvatarImage :src="selectedCurrencyOptionValue?.photo" alt="Currency Photo" />
                                        <AvatarFallback>{{ selectedCurrencyOptionValue?.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ transactionForm.currency_id
                                        ? selectedCurrencyOptionValue?.value
                                        : `Select Currency` }}</span>
                                    </div>
                                  </template>
                                  <CommandItem v-for="option in currencyOptionValues" :key="option.value"
                                    :value="option.value" @select="(ev) => {
                                      if (typeof ev.detail.value === 'string') {
                                        transactionForm.currency_id = ev.detail.value
                                      }
                                      transactionForm.currency_id = option.id.toString()
                                      currencySelectIsOpen = false
                                    }">
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="option.photo" class="size-6">
                                        <AvatarImage :src="option.photo" alt="Currency Photo" />
                                        <AvatarFallback>{{ option.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ option.value }}</span>
                                    </div>
                                    <Check class="ml-auto h-4 w-4" :class="[
                                      transactionForm.currency_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                                    ]" />
                                  </CommandItem>
                                </SelectInput>

                                <SelectInput v-model="transactionTypeSelectIsOpen"
                                  :error="transactionForm.errors.transaction_type_id" label="Transaction Type"
                                  :option-values="transactionTypeOptionValues"
                                  :value="transactionForm.transaction_type_id"
                                  popover-content-class="w-[calc(100vw-3rem)] max-w-[550px]">
                                  <template #selected>
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="selectedTransactionTypeOptionValue?.photo" class="size-6">
                                        <AvatarImage :src="selectedTransactionTypeOptionValue?.photo"
                                          alt="Transaction Type Photo" />
                                        <AvatarFallback>{{ selectedTransactionTypeOptionValue?.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ transactionForm.currency_id
                                        ? selectedTransactionTypeOptionValue?.value
                                        : `Select Transaction Type` }}</span>
                                    </div>
                                  </template>
                                  <CommandItem v-for="option in transactionTypeOptionValues" :key="option.value"
                                    :value="option.value" @select="(ev) => {
                                      if (typeof ev.detail.value === 'string') {
                                        transactionForm.transaction_type_id = ev.detail.value
                                      }
                                      transactionForm.transaction_type_id = option.actualValue.toString()
                                      transactionTypeSelectIsOpen = false
                                    }">
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="option.photo" class="size-6">
                                        <AvatarImage :src="option.photo" alt="Currency Photo" />
                                        <AvatarFallback>{{ option.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ option.value }}</span>
                                    </div>
                                    <Check class="ml-auto h-4 w-4" :class="[
                                      transactionForm.transaction_type_id === option.actualValue.toString() ? 'opacity-100' : 'opacity-0'
                                    ]" />
                                  </CommandItem>
                                </SelectInput>

                                <SelectInput v-if="showBankField" v-model="bankSelectIsOpen"
                                  :error="transactionForm.errors.bank_id" label="Bank" :option-values="bankOptionValues"
                                  :value="transactionForm.bank_id"
                                  popover-content-class="w-[calc(100vw-3rem)] max-w-[550px]">
                                  <template #selected>
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="selectedBankOptionValue?.photo" class="size-6">
                                        <AvatarImage :src="selectedBankOptionValue?.photo" alt="Bank Photo" />
                                        <AvatarFallback>{{ selectedBankOptionValue?.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ transactionForm.bank_id
                                        ? selectedBankOptionValue?.value
                                        : `Select Bank` }}</span>
                                    </div>
                                  </template>
                                  <CommandItem v-for="option in bankOptionValues" :key="option.value"
                                    :value="option.value" @select="(ev) => {
                                      if (typeof ev.detail.value === 'string') {
                                        transactionForm.bank_id = ev.detail.value
                                      }
                                      transactionForm.bank_id = option.id.toString()
                                      bankSelectIsOpen = false
                                    }">
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="option.photo" class="size-6">
                                        <AvatarImage :src="option.photo" alt="Bank Photo" />
                                        <AvatarFallback>{{ option.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ option.value }}</span>
                                    </div>
                                    <Check class="ml-auto h-4 w-4" :class="[
                                      transactionForm.bank_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                                    ]" />
                                  </CommandItem>
                                </SelectInput>

                                <SelectInput v-if="showCurrencyOrderField" v-model="currencyOrderSelectIsOpen"
                                  :error="transactionForm.errors.currency_order_id" label="Currency Order"
                                  :option-values="currencyOrderOptionValues" :value="transactionForm.currency_order_id"
                                  popover-content-class="w-[calc(100vw-3rem)] max-w-[550px]">
                                  <template #selected>
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="selectedCurrencyOrderOptionValue?.photo" class="size-6">
                                        <AvatarImage :src="selectedCurrencyOrderOptionValue?.photo"
                                          alt="Currency Order Photo" />
                                        <AvatarFallback>{{ selectedCurrencyOrderOptionValue?.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ transactionForm.currency_order_id
                                        ? selectedCurrencyOrderOptionValue?.value
                                        : 'Select Currency Order' }}</span>
                                    </div>
                                  </template>
                                  <CommandItem v-for="option in currencyOrderOptionValues" :key="option.value"
                                    :value="option.value" @select="(ev) => {
                                      if (typeof ev.detail.value === 'string') {
                                        transactionForm.currency_order_id = ev.detail.value
                                      }
                                      transactionForm.currency_order_id = option.id.toString()
                                      currencyOrderSelectIsOpen = false
                                    }">
                                    <div class="flex items-center gap-2">
                                      <Avatar v-if="option.photo" class="size-6">
                                        <AvatarImage :src="option.photo" alt="Currency Order Photo" />
                                        <AvatarFallback>{{ option.name }}</AvatarFallback>
                                      </Avatar>
                                      <span>{{ option.value }}</span>
                                    </div>
                                    <Check class="ml-auto h-4 w-4" :class="[
                                      transactionForm.currency_order_id === option.id.toString() ? 'opacity-100' : 'opacity-0'
                                    ]" />
                                  </CommandItem>
                                </SelectInput>

                                <text-input v-model="transactionForm.amount" :error="transactionForm.errors.amount"
                                  class="w-full" label="Amount" />
                              </div>
                            </div>
                            <DialogFooter>
                              <DialogClose asChild>
                                <Button type="button" label="Cancel" @click="closeDialog" class="mt-4 sm:mt-0" />
                              </DialogClose>
                              <Button type="submit" label="Add Transaction" :loading="transactionForm.processing" />
                            </DialogFooter>
                          </form>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="invoice">

        </TabsContent>
        <TabsContent value="notes">

        </TabsContent>
        <TabsContent value="feeds">

        </TabsContent>
        <TabsContent value="audits">

        </TabsContent>
        <TabsContent value="official-receipts">

        </TabsContent>
      </Tabs>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Transactions</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Type</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Debit</TableHead>
              <TableHead>Credit</TableHead>
              <TableHead>Remarks</TableHead>
              <TableHead class="cursor-pointer" @click="changeTransactionSort('created_at')">
                <SortableHeader title="Created At" field="created_at" :current-sort="transactionForm.sort"
                  :direction="transactionForm.direction" />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="(transaction, index) in transactions.data" :key="transaction.id"
              :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ transaction.type }}</TableCell>
              <TableCell>{{ transaction.currency }}</TableCell>
              <TableCell>{{ transaction.debit }}</TableCell>
              <TableCell>{{ transaction.credit }}</TableCell>
              <TableCell>{{ transaction.remarks }}</TableCell>
              <TableCell>{{ transaction.created_at }}</TableCell>
            </TableRow>
            <TableRow v-if="!transactions.data?.length">
              <TableCell class="text-center border-0" colspan="6">
                No transactions found.
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
      <Pagination class="mt-6" :data="transactions" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import TextInput from "@/Shared/TextInput.vue";
import TextareaInput from '@/Shared/TextareaInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import LoadingButton from '@/Shared/LoadingButton.vue';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
  DialogClose,
  DialogDescription,
} from '@/Components/ui/dialog';
import axios from 'axios';
import { Card, CardContent } from '@/Components/ui/card';
import Button from '@/Shared/Button.vue';
import { Label } from '@/Components/ui/label';
import { Input } from '@/Components/ui/input';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select";
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import SearchFilter from "@/Shared/SearchFilter.vue";
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@/Components/ui/avatar";
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import SortableHeader from "@/Shared/SortableHeader.vue";
import Pagination from "@/Shared/Pagination.vue";
import { Badge } from "@/Components/ui/badge";
import throttle from "lodash/throttle";
import pickBy from "lodash/pickBy";
import { CommandItem } from '@/Components/ui/command';
import { Check } from 'lucide-vue-next';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Separator } from '@/Components/ui/separator';

export default {
  components: {
    Head,
    Link,
    TextInput,
    TextareaInput,
    SelectInput,
    LoadingButton,
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogTrigger,
    DialogClose,
    DialogDescription,
    Card,
    CardContent,
    Label,
    Input,
    Button,
    SelectItem,
    Breadcrumb,
    SearchFilter,
    Table,
    TableBody,
    TableHeader,
    TableRow,
    TableCell,
    TableHead,
    SortableHeader,
    Pagination,
    Badge,
    Select,
    SelectContent,
    SelectGroup,
    SelectTrigger,
    SelectValue,
    Avatar,
    AvatarFallback,
    AvatarImage,
    CommandItem,
    Check,
    DisplayLabel,
    TabsContent,
    TabsTrigger,
    TabsList,
    Tabs,
    SwitchInput,
    Separator,
  },
  layout: Layout,
  props: {
    currencyOrder: Object,
    transactionTypes: Array,
    banks: Array,
    currencies: Array,
    currencyOrders: Array,
  },
  data() {
    return {
      isDialogOpen: false,
      form: this.$inertia.form({
        _method: 'put',
        payable_amount: this.currencyOrder.payable_amount,
        receivable_amount: this.currencyOrder.receivable_amount,
        processing_fee: this.currencyOrder.processing_fee,
        exchange_rate: this.currencyOrder.exchange_rate,
        remarks: this.currencyOrder.remarks || '',
      }),
      transactionForm: this.$inertia.form({
        currency_id: '',
        transaction_type_id: '',
        bank_id: '',
        currency_order_id: '',
        amount: '',
        initiator_currency_order_id: '',
        search: null,
        sort: 'created_at',
        direction: 'desc',
        per_page: 10,
      }),
      fields: {
        'Order Type': this.currencyOrder.currency_order_type.name,
        Customer: this.currencyOrder.customer.name,
        'In Currency': this.currencyOrder.in_currency?.name || 'N/A',
        'Out Currency': this.currencyOrder.out_currency?.name || 'N/A',
        'Payable Amount': this.currencyOrder.payable_amount || 'N/A',
        'Receivable Amount': this.currencyOrder.receivable_amount || 'N/A',
        'Processing Fee': this.currencyOrder.processing_fee || 'N/A',
        'Exchange Rate': this.currencyOrder.exchange_rate || 'N/A',
      },
      transactions: {
        data: [],
        links: [],
        current_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        prev_page_url: null,
        last_page: 1,
        per_page: 10,
        total: 0,
      },
      localCurrencies: [],
      localTransactionTypes: [],
      localBanks: [],
      localCurrencyOrders: [],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Currency Orders', link: route('currency-orders') },
        { name: 'Edit', link: route('currency-orders.edit', this.currencyOrder.id), is_active: true },
      ],
      currencySelectIsOpen: false,
      transactionTypeSelectIsOpen: false,
      bankSelectIsOpen: false,
      currencyOrderSelectIsOpen: false,
    };
  },
  computed: {
    showBankField() {
      const selectedType = this.localTransactionTypes.find(
        (type) => type.value === this.transactionForm.transaction_type_id
      );
      return selectedType?.value?.toLowerCase() === "account";
    },
    showCurrencyOrderField() {
      const selectedType = this.localTransactionTypes.find(
        (type) => type.value === this.transactionForm.transaction_type_id
      );
      return selectedType?.value?.toLowerCase() === "contra";
    },
    getDialogDescription() {
      const orderType = this.currencyOrder.currency_order_type.value;
      switch (orderType) {
        case 'e':
        case 'tpp':
          return `Pay ${this.currencyOrder.out_currency?.code}`;
        case 'r':
        case 'tpr':
          return `Receive ${this.currencyOrder.in_currency?.code}`;
        case 'po':
          return `Receive ${this.currencyOrder.in_currency?.code} | Pay ${this.currencyOrder.out_currency?.code}`;
        default:
          return '';
      }
    },
    showAddTransactionButton() {
      const status = this.currencyOrder.currency_order_status?.value || '';
      return !['completed', 'cancelled', 'expired'].includes(status);
    },
    currencyOptionValues() {
      return this.localCurrencies.map(currency => ({ ...currency, value: `${currency.name} (${currency.code})` }));
    },
    selectedCurrencyOptionValue() {
      return this.currencyOptionValues.find(option => option.id.toString() === this.transactionForm.currency_id);
    },
    transactionTypeOptionValues() {
      return this.localTransactionTypes.map(type => ({ ...type, actualValue: type.value, value: type.name }));
    },
    selectedTransactionTypeOptionValue() {
      return this.transactionTypeOptionValues.find(option => option.actualValue.toString() === this.transactionForm.transaction_type_id);
    },
    bankOptionValues() {
      return this.localBanks.map(bank => ({ ...bank, value: `${bank.currency?.code}${bank.total_balance} - ${bank.holder_name} (${bank.name})` }));
    },
    selectedBankOptionValue() {
      return this.bankOptionValues.find(option => option.id.toString() === this.transactionForm.bank_id);
    },
    currencyOrderOptionValues() {
      return this.localCurrencyOrders.map(order => ({ ...order, value: order.reference }));
    },
    selectedCurrencyOrderOptionValue() {
      return this.currencyOrderOptionValues.find(option => option.id.toString() === this.transactionForm.currency_order_id);
    },
  },
  watch: {
    'transactionForm.search': throttle(function () {
      this.loadTransactions();
    }, 150),
    'transactionForm.sort': function () {
      this.loadTransactions();
    },
    'transactionForm.direction': function () {
      this.loadTransactions();
    },
    'transactionForm.per_page': function () {
      this.loadTransactions();
    },
    'transactionForm.currency_id'(newValue) {
      if (newValue) {
        this.filterBanks(newValue);
        this.filterCurrencyOrders(newValue);
      } else {
        this.localBanks = [];
        this.localCurrencyOrders = [];
      }
    },
  },
  methods: {
    closeDialog() {
      this.isDialogOpen = false;
      this.transactionForm.reset();
    },
    setDialogOpen(value) {
      this.isDialogOpen = value;
      if (!value) {
        this.transactionForm.reset();
      } else {
        this.transactionForm.initiator_currency_order_id = this.currencyOrder.id;
      }
    },
    async loadData() {
      try {
        const [filteredCurrenciesResponse, transactionTypesResponse] = await Promise.all([
          axios.get('/currencies/filter', {
            params: {
              in_currency_id: this.currencyOrder.in_currency_id,
              out_currency_id: this.currencyOrder.out_currency_id,
            },
          }),
          axios.get('/transaction-types/all'),
        ]);

        this.localCurrencies = filteredCurrenciesResponse.data;
        this.localTransactionTypes = transactionTypesResponse.data;
      } catch (error) {
        console.error('Failed to load data:', error);
      }
    },
    async filterBanks(currencyId) {
      try {
        const response = await axios.get('/banks/filter', {
          params: {
            currency_id: currencyId,
          },
        });
        this.localBanks = response.data;
      } catch (error) {
        console.error('Failed to filter banks:', error);
        this.localBanks = [];
      }
    },
    async filterCurrencyOrders(currencyId) {
      try {
        const response = await axios.get('/currency-orders/filter', {
          params: {
            currency_order_id: this.currencyOrder.id,
            currency_id: currencyId,
          },
        });
        this.localCurrencyOrders = response.data;
      } catch (error) {
        console.error('Failed to filter currency orders:', error);
        this.localCurrencyOrders = [];
      }
    },
    update() {
      this.form.put(route('currency-orders.update', this.currencyOrder.id));
    },
    async addTransaction() {
      try {
        await this.transactionForm.post(route("transactions.store"), {
          preserveScroll: true,
          preserveState: true,
          onSuccess: (response) => {
            this.closeDialog();
            this.loadTransactions();
          },
        });
      } catch (error) {
        console.error("Transaction failed:", error);
      }
    },
    async loadTransactions() {
      try {
        const response = await axios.get(
          route('currency-orders.transactions.query', this.currencyOrder.id),
          { params: pickBy(this.transactionForm) }
        );
        this.transactions = response.data;
      } catch (error) {
        console.error('Failed to load transactions:', error);
      }
    },
    resetTransactionFilter() {
      this.transactionForm.reset();
      this.transactionForm.search = null;
      this.transactionForm.sort = 'created_at';
      this.transactionForm.direction = 'desc';
      this.transactionForm.per_page = 10;
    },
    changeTransactionSort(field) {
      if (field === 'created_at') {
        this.transactionForm.direction =
          this.transactionForm.direction === 'asc' ? 'desc' : 'asc';
        this.transactionForm.sort = field;
      }
    },
  },
  created() {
    this.localTransactionTypes = this.transactionTypes || [];
    this.localCurrencyOrders = this.currencyOrders || [];
    this.loadData();
    this.loadTransactions();
  },
};
</script>
