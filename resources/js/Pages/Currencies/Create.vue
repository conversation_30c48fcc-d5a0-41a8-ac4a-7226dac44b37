<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Create Currency" />
    <h1 class="mb-8 text-3xl font-bold">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('currencies')">Currencies</Link>
      <span class="text-indigo-400 font-medium">/</span> Create
    </h1>
    <div class="flex justify-center">
      <div class="w-full max-w-5xl bg-white rounded-md shadow overflow-hidden">
        <form @submit.prevent="store">
          <div class="flex flex-wrap -mb-8 -mr-6 p-8">
            <text-input v-model="form.name" :error="form.errors.name" class="pb-8 pr-6 w-full lg:w-1/2" label="Name" />
            <text-input v-model="form.code" :error="form.errors.code" class="pb-8 pr-6 w-full lg:w-1/2" label="Code" />
            <text-input v-model="form.symbol" :error="form.errors.symbol" class="pb-8 pr-6 w-full lg:w-1/2"
              label="Symbol" />
            <file-input v-model="form.photo" :error="form.errors.photo" class="pb-8 pr-6 w-full lg:w-1/2" type="file"
              accept="image/*" label="Logo" />
            <div class="pb-8 pr-6 w-full lg:w-1/2">
              <label class="block text-sm font-medium text-gray-700">Status</label>
              <label class="switch mt-2">
                <input type="checkbox" v-model="form.is_active" :true-value=true :false-value=false />
                <span class="slider round"></span>
              </label>
              <span class="ml-2 text-sm text-gray-600">
                {{ form.is-active === true ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
          <div class="flex items-center justify-end px-8 py-4 bg-gray-50 border-t border-gray-100">
            <loading-button :loading="form.processing" class="btn-indigo" type="submit">
              Create Currency
            </loading-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3'
import Layout from '@/Shared/Layout.vue'
import FileInput from '@/Shared/FileInput.vue'
import TextInput from '@/Shared/TextInput.vue'
import LoadingButton from '@/Shared/LoadingButton.vue'

export default {
  components: {
    Head,
    Link,
    LoadingButton,
    FileInput,
    TextInput,
  },
  layout: Layout,
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        name: '',
        code: '',
        symbol: '',
        photo: null,
        is_active: true,
      }),
    }
  },
  methods: {
    store() {
      this.form.post(route('currencies.store'));
    },
  },
}
</script>
