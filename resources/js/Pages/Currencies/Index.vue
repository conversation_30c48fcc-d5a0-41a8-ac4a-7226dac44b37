<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Currencies" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter v-model="form.search" @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Trashed:</Label>
              <Select v-model="form.trashed" class="form-select mt-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Please Select Trash" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="with">
                      With Trashed
                    </SelectItem>
                    <SelectItem value="only">
                      Only Trashed
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Currencies</h1>
      <div>
        <Dialog v-model:open="createDialogIsOpen">
          <Button label="Create" icon="plus" class="cursor-pointer" @click="createCurrency" />
          <DialogContent class="sm:max-w-[425px]">
            <DialogHeader class="px-2">
              <DialogTitle>Create Currency</DialogTitle>
              <DialogDescription></DialogDescription>
            </DialogHeader>
            <form @submit.prevent="submitCreateCurrency">
              <div class="overflow-y-auto mb-4 p-2">
                <div class="grid gap-4 max-h-[75vh]">
                  <TextInput v-model="dialog.name" :error="errors.name" label="Name" />
                  <TextInput v-model="dialog.code" :error="errors.code" label="Code" />
                  <TextInput v-model="dialog.symbol" :error="errors.symbol" label="Symbol" />
                  <SwitchInput v-model="dialog.is_crypto" :error="errors.is_crypto" label="Currency Type">
                    <Label>{{ dialog.is_crypto === true ? 'Crypto' : 'Fiat' }}</Label>
                  </SwitchInput>
                  <SwitchInput v-model="dialog.is_active" :error="errors.is_active" label="Status">
                    <Label>{{ dialog.is_active === true ? 'Active' : 'Inactive' }}</Label>
                  </SwitchInput>
                </div>
              </div>
              <DialogFooter class="px-2">
                <Button type="submit" label="Create Currency" />
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow class="text-left font-bold">
            <TableHead>Logo</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('code')">
              <SortableHeader title="Code" field="code" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('name')">
              <SortableHeader title="Name" field="name" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead>Symbol</TableHead>
            <TableHead>Status</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(currency, index) in currencies.data" :key="currency.id"
            :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell class="text-center">
              <Avatar class="size-6 align-middle">
                <AvatarImage :src="currency.photo" alt="User avatar" />
                <AvatarFallback>{{ currency.name }}</AvatarFallback>
              </Avatar>
            </TableCell>
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('currencies.edit', currency.id)">
              {{ currency.code }}
              <icon v-if="currency.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('currencies.edit', currency.id)">
              {{ currency.name }}
              <icon v-if="currency.deleted_at" name="trash" class="shrink-0 ml-2 w-3 h-3 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>{{ currency.symbol }}</TableCell>
            <TableCell>
              <span class="px-3 py-1 text-xs font-medium rounded-full"
                :class="currency.is_active === true ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                {{ currency.is_active === true ? 'Active' : 'Inactive' }}
              </span>
            </TableCell>
            <TableCell>{{ currency.created_at }}</TableCell>
            <TableCell>
              <Link :href="route('currencies.edit', currency.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
          </TableRow>
          <TableRow v-if="currencies.data.length === 0">
            <TableCell class="text-center border-0" colspan="7">No currencies found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="currencies" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import TextInput from '@/Shared/TextInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';
import Icon from '@/Shared/Icon.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import Tooltip from '@/Shared/Tooltip.vue';
import { Info } from 'lucide-vue-next';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Icon,
    Tooltip,
    Info,
    Breadcrumb,
    Avatar,
    AvatarFallback,
    AvatarImage,
  },
  layout: Layout,
  props: {
    filters: Object,
    currencies: Object,
    sort: Object,
  },
  data() {
    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
      },
      form: {
        search: this.filters.search,
        trashed: this.filters.trashed,
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
      },
      dialog: {
        name: '',
        code: '',
        symbol: '',
        is_active: true,
        is_crypto: false,
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Currencies', link: route('currencies'), is_active: true },
      ]
    };
  },
  watch: {
    form: {
      deep: true,
      handler: throttle(function () {
        const params = {};
        Object.keys(this.form).forEach(key => {
          if (this.form[key] !== null &&
            this.form[key] !== undefined &&
            this.form[key] !== this.defaultValues[key]) {
            params[key] = this.form[key];
          }
        });

        this.$inertia.get('/currencies', pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ['currencies'],
          replace: true
        });
      }, 150),
    },
  },
  methods: {
    reset() {
      this.form = {
        search: null,
        trashed: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
      };

      this.$inertia.get('/currencies', {}, {
        preserveState: true,
        preserveScroll: true,
        only: ['currencies'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
    },
    createCurrency() {
      this.dialog.name = '';
      this.dialog.code = '';
      this.dialog.symbol = '';
      this.dialog.is_active = true;
      this.dialog.is_crypto = false;
      this.createDialogIsOpen = true;
    },
    submitCreateCurrency() {
      this.$inertia.post(route('currencies.store'), {
        name: this.dialog.name,
        code: this.dialog.code,
        symbol: this.dialog.symbol,
        is_active: this.dialog.is_active,
        is_crypto: this.dialog.is_crypto,
      }, {
        preserveScroll: true,
        onSuccess: () => {
          this.dialog.name = '';
          this.dialog.code = '';
          this.dialog.symbol = '';
          this.dialog.is_active = true;
          this.dialog.is_crypto = false;
          this.createDialogIsOpen = true;
          this.errors = {};
          this.createDialogIsOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        },
      });
    },
  },
};
</script>
