<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center max-w-3xl gap-4 flex-wrap mb-4">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_crypto ? 'bg-[#6366F1]' : 'bg-[#475569]'}`">
        {{ form.is_crypto === true ? 'Crypto' : 'Fiat' }}
      </span>
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">{{ form.is_active === true ? 'Active' :
          'Inactive'
        }}</span>
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
      <img :src="currency.photo" class="block ml-4 w-10 h-10 rounded-full" alt="Currency logo" />
    </div>
    <div>
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <form @submit.prevent="update">
              <div class="grid md:grid-cols-2 gap-4 mb-4">
                <TextInput v-model="form.name" :error="form.errors.name" label="Name" />
                <TextInput v-model="form.code" :error="form.errors.code" label="Code" />
                <TextInput v-model="form.symbol" :error="form.errors.symbol" label="Symbol" />
                <FileInput v-model="form.photo" :error="form.errors.photo" type="file" accept="image/*" label="Logo" />
                <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status">
                  <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                </SwitchInput>
                <SwitchInput v-model="form.is_crypto" :error="form.errors.is_crypto" label="Currency Type">
                  <Label>{{ form.is_crypto === true ? 'Crypto' : 'Fiat' }}</Label>
                </SwitchInput>
              </div>
              <div class="flex items-center justify-end gap-4 flex-wrap">
                <Button v-if="!currency.deleted_at" variant="destructive" @click="destroy" type="button"
                  label="Delete Currency" />
                <Button type="submit" label="Update Currency" :loading="form.processing" />
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import FileInput from '@/Shared/FileInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import Button from '@/Shared/Button.vue';
import { Label } from '@/Components/ui/label';
import Breadcrumb from '@/Shared/Breadcrumb.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    FileInput,
    SwitchInput,
    Button,
    Label,
    Breadcrumb
  },
  layout: Layout,
  props: {
    currency: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.currency.name,
        code: this.currency.code,
        symbol: this.currency.symbol,
        photo: null,
        is_active: this.currency.is_active,
        is_crypto: this.currency.is_crypto,
      }),
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Currencies', link: route('currencies') },
        { name: 'Edit', link: route('currencies.edit', this.currency.id), is_active: true },
      ]
    };
  },
  methods: {
    update() {
      this.form.post(route('currencies.update', this.currency.id), {
        onSuccess: () => this.form.reset('photo'),
      });
    },
    destroy() {
      if (confirm('Are you sure you want to delete this currency?')) {
        this.$inertia.delete(route('currencies.destroy', this.currency.id));
      }
    },
  },
};
</script>