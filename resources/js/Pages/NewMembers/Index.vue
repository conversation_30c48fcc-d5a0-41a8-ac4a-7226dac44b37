<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="New Members" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <div class="flex items-center justify-between mb-5">
      <h1 class="text-2xl font-bold">New Members</h1>
      <div class="flex gap-2">
        <Button @click="exportData" class="min-w-[100px]" :disabled="isExporting">
          <Loader2 v-if="isExporting" class="mr-2 h-4 w-4 animate-spin" />
          {{ isExporting ? 'Exporting...' : 'Export' }}
        </Button>
      </div>
    </div>

    <!-- New Members Table -->
    <Card>
      <CardContent class="p-6">
        <div class="overflow-x-auto">
          <table class="w-full border-collapse">
            <thead>
              <tr class="border-b">
                <th class="text-left p-3 font-medium">User ID</th>
                <th class="text-left p-3 font-medium">Username</th>
                <th class="text-left p-3 font-medium">Phone Number</th>
                <th class="text-left p-3 font-medium">Registration Date</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="newMembers.length === 0">
                <td colspan="4" class="text-center p-6 text-muted-foreground">
                  No new members found for the selected period.
                </td>
              </tr>
              <tr v-else v-for="member in newMembers" :key="member.user_id" class="border-b hover:bg-muted/50">
                <td class="p-3">{{ member.user_id }}</td>
                <td class="p-3">{{ member.username }}</td>
                <td class="p-3">{{ member.phone_no || '-' }}</td>
                <td class="p-3">{{ formatDate(member.created_at) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Loader2 } from 'lucide-vue-next';

export default {
  components: {
    Head,
    Breadcrumb,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    Button,
    Loader2
  },
  props: {
    newMembers: Array,
    startDate: String,
    endDate: String,
    store_id: [String, Number, null],
    auth: Object,
  },
  data() {
    return {
      breadcrumbs: [
        {
          title: 'Dashboard',
          href: '/'
        },
        {
          title: 'New Members',
          href: '/new-members'
        }
      ],
      isExporting: false,
    };
  },
  methods: {
    async exportData() {
      this.isExporting = true;

      const params = new URLSearchParams({
        store_id: this.store_id || '',
        startDate: this.startDate,
        endDate: this.endDate,
      });

      try {
        const response = await fetch(`/new-members/export?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          } else {
            a.download = 'new-members-export.xlsx';
          }
        } else {
          a.download = 'new-members-export.xlsx';
        }

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        return new Date(dateString).toLocaleString();
      } catch (e) {
        return dateString;
      }
    }
  },
  layout: Layout,
};
</script>
