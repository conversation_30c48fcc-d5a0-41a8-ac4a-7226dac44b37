<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="New Members" />

    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <div class="flex items-center justify-between mb-5">
      <h1 class="text-2xl font-bold">New Members</h1>
      <div class="flex gap-2">
        <Button @click="exportData" class="min-w-[100px]" :disabled="isExporting">
          <Loader2 v-if="isExporting" class="mr-2 h-4 w-4 animate-spin" />
          {{ isExporting ? 'Exporting...' : 'Export' }}
        </Button>
      </div>
    </div>

    <!-- New Members Table -->
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>User ID</TableHead>
            <TableHead>Username</TableHead>
            <TableHead>Phone Number</TableHead>
            <TableHead>Registration Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(member, index) in newMembers" :key="member.user_id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ member.user_id }}</TableCell>
            <TableCell>{{ member.username }}</TableCell>
            <TableCell>{{ member.phone_no || '-' }}</TableCell>
            <TableCell>{{ formatDate(member.created_at) }}</TableCell>
          </TableRow>
          <TableRow v-if="newMembers.length === 0">
            <TableCell class="text-center border-0" colspan="4">No new members found for the selected period.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <!-- Pagination -->
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination v-if="pagination && pagination.total > 0" class="flex justify-end" :data="pagination" />
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Button } from '@/Components/ui/button';
import { Loader2 } from 'lucide-vue-next';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Shared/table';
import Pagination from '@/Shared/Pagination.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import pickBy from 'lodash/pickBy';

export default {
  components: {
    Head,
    Breadcrumb,
    Button,
    Loader2,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Pagination,
    ShowEntries
  },
  props: {
    newMembers: Array,
    pagination: Object,
    startDate: String,
    endDate: String,
    store_id: [String, Number, null],
    perPage: [String, Number],
    auth: Object,
  },
  data() {
    return {
      breadcrumbs: [
        {
          title: 'Dashboard',
          href: '/'
        },
        {
          title: 'New Members',
          href: '/new-members'
        }
      ],
      isExporting: false,
      form: {
        perPage: this.perPage ? this.perPage.toString() : '10',
      },
    };
  },
  watch: {
    'form.perPage'() {
      this.updatePerPage();
    }
  },
  methods: {
    updatePerPage() {
      const params = {
        store_id: this.store_id,
        startDate: this.startDate,
        endDate: this.endDate,
        perPage: this.form.perPage,
      };

      this.$inertia.get('/new-members', pickBy(params), {
        preserveState: true,
        preserveScroll: true,
        replace: true
      });
    },
    async exportData() {
      this.isExporting = true;

      const params = new URLSearchParams({
        store_id: this.store_id || '',
        startDate: this.startDate,
        endDate: this.endDate,
      });

      try {
        const response = await fetch(`/new-members/export?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Export failed with status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition && contentDisposition.includes('filename=')) {
          const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
          const matches = filenameRegex.exec(contentDisposition);
          if (matches && matches[1]) {
            a.download = matches[1].replace(/['"]/g, '');
          } else {
            a.download = 'new-members-export.xlsx';
          }
        } else {
          a.download = 'new-members-export.xlsx';
        }

        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

      } catch (error) {
        console.error('Export failed:', error);
      } finally {
        this.isExporting = false;
      }
    },
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        return new Date(dateString).toLocaleString();
      } catch (e) {
        return dateString;
      }
    }
  },
  layout: Layout,
};
</script>
