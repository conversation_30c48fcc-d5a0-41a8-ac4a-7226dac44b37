import '../css/fonts.css'
import '../css/app.css'
import '../css/index.css'
import '../css/scrollbar.css'
import { createApp, h } from 'vue'
import { createInertiaApp } from '@inertiajs/vue3'
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import {
  faSquarePollHorizontal,
  faUsers,
  faUsersGear,
  faPlus,
  faEdit,
  faMoneyBill,
  faMoneyBillTransfer,
  faMoneyBillTrendUp,
  faUniversity,
  faUserTag,
  faExchangeAlt,
  faBoxesStacked,
  faChartLine,
} from '@fortawesome/free-solid-svg-icons'
import { ZiggyVue } from '../../vendor/tightenco/ziggy'

// Add all necessary icons
library.add(
  faSquarePollHorizontal,
  faUsers,
  faUsersGear,
  faPlus,
  faEdit,
  faMoneyBill,
  faMoneyBillTransfer,
  faMoneyBillTrendUp,
  faUniversity,
  faUserTag,
  faExchangeAlt,
  faBoxesStacked,
  faChartLine
)

createInertiaApp({
  resolve: name => {
    const pages = import.meta.glob('./Pages/**/*.vue', { eager: true })
    return pages[`./Pages/${name}.vue`]
  },
  title: title => title ? `${title} - UW` : 'UW',
  setup({ el, App, props, plugin }) {
    createApp({ render: () => h(App, props) })
      .use(plugin)
      .use(ZiggyVue)
      .component('font-awesome-icon', FontAwesomeIcon)
      .mount(el)
  },
})
