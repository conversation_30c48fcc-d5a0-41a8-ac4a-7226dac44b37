<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('merchants', function (Blueprint $table) {
            $table->dropForeign(['user_id']);

            $table->dropColumn([
                'language',
                'email',
                'phone',
                'remark',
                'user_id',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('merchants', function (Blueprint $table) {
            $table->after('name', function ($table) {
                $table->string('language')->default('zh-CN');
                $table->string('email')->nullable();
                $table->string('phone')->nullable();
                $table->longText('remark')->nullable();
                $table->foreignId('user_id')->constrained()->nullable();
            });
        });
    }
};
