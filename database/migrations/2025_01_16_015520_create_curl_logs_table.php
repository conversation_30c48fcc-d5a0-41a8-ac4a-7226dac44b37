<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('curl_logs', function (Blueprint $table) {
            $table->id();
            $table->timestamps();

            $table->string('endpoint', 255)->index(); // endpoint: varchar(255), Index
            $table->longText('request'); // request: longtext
            $table->longText('request_msg')->nullable(); // request_msg: longtext, nullable
            $table->longText('response'); // response: longtext
            $table->longText('response_msg')->nullable(); // response_msg: longtext, nullable
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('curl_logs');
    }
};
