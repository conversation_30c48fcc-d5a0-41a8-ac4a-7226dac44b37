<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchant_providers', function (Blueprint $table) {
            $table->id();
            $table->timestamps();

            $table->string('provider_code');
            $table->foreignId('merchant_id')->constrained();
            $table->foreignId('provider_id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchant_providers');
    }
};
