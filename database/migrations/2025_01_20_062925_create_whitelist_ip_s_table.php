<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whitelist_ip_s', function (Blueprint $table) {
            $table->id();
            $table->timestamps();

            $table->string('ip')->index();
            $table->string('remark')->nullable();
            $table->foreignId('merchant_id')->constrained();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whitelist_ip_s');
    }
};
