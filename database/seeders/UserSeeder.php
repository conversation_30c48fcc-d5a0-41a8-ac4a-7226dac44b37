<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::firstOrCreate(
            [
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Admin',
                'password' => bcrypt('password'),
            ]
        );

        $superAdminRole = Role::where('name', 'superadmin')->first();
        $merchantRole = Role::where('name', 'merchant')->first();

        if (! $superAdminRole) {
            $this->command->warn('Superadmin role not found.');
        }

        if (! $merchantRole) {
            $this->command->warn('Superadmin role not found.');
        }

        $user->assignRole($superAdminRole);
        $this->command->info('Superadmin role assigned to '.$user->email.'.');
    }
}
