<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Roles
        $roles = [
            'superadmin',
            'merchant',
        ];

        // Create Roles
        foreach ($roles as $role) {
            Role::updateOrCreate(['name' => $role]);
        }

        // Permissions
        $permissions = $this->getAllPermissions();

        foreach ($permissions as $permission) {
            Permission::updateOrCreate(['name' => $permission]);
        }

        $merchantPermissions = $this->getMerchantPermissions();
        Role::where('name', 'superadmin')->first()->syncPermissions($permissions);
        Role::where('name', 'merchant')->first()->syncPermissions($merchantPermissions);

        $this->command->info('Roles and Permissions seeded successfully.');
    }

    private function getAllPermissions()
    {
        return [
            'view dashboard',
            'view reports',

            'view users',
            'create users',
            'edit users',
            'delete users',

            'view merchants',
            'create merchants',
            'edit merchants',
            'delete merchants',

            'view roles',
            'manage roles',
            'manage permissions',
        ];
    }

    private function getMerchantPermissions()
    {
        return [
            'view dashboard',
            'view reports',
        ];
    }
}
